# 测试环境配置文件

# Redis配置
redis.host=127.0.0.1
redis.port=6377
redis.username=r-wz9zemom88r2yiqd8l
redis.password=vIdaPrinter@2022
redis.database=0
redis.timeout=2000

# 应用模式
app.mode=development

# 微信支付配置（测试用）
wechat.pay.mchId=test_mch_id
wechat.pay.appId=test_app_id
wechat.pay.apiV3Key=test_api_v3_key_32_chars_padding
wechat.pay.privateKeyPath=/path/to/test/private.key
wechat.pay.serialNo=test_serial_no