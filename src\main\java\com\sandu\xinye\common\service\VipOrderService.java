package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.sandu.xinye.common.enums.UserTier;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.VipOrder;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * VIP订单服务
 * 处理VIP订单的创建、查询、状态管理
 */
public class VipOrderService {

    public static final VipOrderService me = new VipOrderService();

    /**
     * 创建VIP订单（增强幂等性）
     */
    public RetKit createOrder(Integer userId, String plan) {
        // 兼容旧接口，默认作为一次性购买
        return createOrder(userId, plan, "one_time", null);
    }

    /**
     * 创建VIP订单（带购买方式）
     */
    public RetKit createOrder(Integer userId, String plan, String purchaseType) {
        return createOrder(userId, plan, purchaseType, null);
    }

    /**
     * 创建VIP订单（支持幂等键与购买方式）
     */
    public RetKit createOrder(Integer userId, String plan, String purchaseType, String idempotentKey) {
        long startTime = System.currentTimeMillis();
        boolean success = false;

        try {
            // 参数验证
            if (userId == null) {
                return RetKit.fail("用户ID不能为空");
            }

            if (!isValidPlan(plan)) {
                return RetKit.fail("无效的套餐类型");
            }

            // 购买方式校验与兼容
            final String finalPurchaseType;
            if (StrKit.isBlank(purchaseType)) {
                finalPurchaseType = "one_time";
            } else if (!"one_time".equals(purchaseType) && !"subscribe".equals(purchaseType)) {
                return RetKit.fail("无效的购买方式，支持: subscribe, one_time");
            } else {
                finalPurchaseType = purchaseType;
            }

            // 检查用户是否存在
            User user = User.dao.findById(userId);
            if (user == null) {
                return RetKit.fail("用户不存在");
            }

            // 幂等性检查：如果提供了幂等键，先检查是否已存在
            if (StrKit.notBlank(idempotentKey)) {
                VipOrder existingOrder = findOrderByIdempotentKey(idempotentKey);
                if (existingOrder != null) {
                    LogKit.info("幂等键重复，返回已存在订单: " + existingOrder.getOrderNo());
                    Map<String, Object> payParams = generatePayParams(existingOrder);
                    return RetKit.ok("订单已存在")
                        .set("orderNo", existingOrder.getOrderNo())
                        .set("amount", existingOrder.getAmountYuan())
                        .set("plan", plan)
                        .set("payParams", payParams);
                }
            }

            // 检查是否有未支付的订单
            VipOrder existingOrder = findUnpaidOrder(userId);
            if (existingOrder != null) {
                // 检查订单是否已过期
                if (isOrderExpired(existingOrder)) {
                    LogKit.info("发现过期未支付订单，自动关闭: " + existingOrder.getOrderNo());
                    closeExpiredOrder(existingOrder);
                } else {
                    return RetKit.fail("您有未支付的订单，请先完成支付或取消订单")
                        .set("existingOrderNo", existingOrder.getOrderNo());
                }
            }

            // 使用事务创建订单，确保幂等性
            VipOrder order = null;
            boolean txSuccess = Db.tx(() -> {
                VipOrder newOrder = buildOrder(userId, plan, finalPurchaseType, idempotentKey);
                boolean saveSuccess = newOrder.save();

                if (saveSuccess) {
                    // 将订单保存到外部变量
                    return true;
                } else {
                    return false;
                }
            });

            if (txSuccess) {
                // 重新查询订单以获取完整信息
                order = findUnpaidOrder(userId);
                success = true;
            }

            if (order == null) {
                return RetKit.fail("创建订单失败");
            }

            LogKit.info("VIP订单创建成功: " + order.getOrderNo() + ", 用户: " + userId + ", 套餐: " + plan);

            // 生成支付参数
            Map<String, Object> payParams = generatePayParams(order);

            RetKit result = RetKit.ok("订单创建成功")
                .set("orderNo", order.getOrderNo())
                .set("amount", order.getAmountYuan())
                .set("plan", plan)
                .set("purchaseType", finalPurchaseType)
                .set("payParams", payParams);

            success = true;
            return result;

        } catch (Exception e) {
            LogKit.error("创建VIP订单失败", e);

            // 检查是否是唯一约束冲突
            if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
                return RetKit.fail("订单创建冲突，请稍后重试");
            }

            return RetKit.fail("创建订单失败: " + e.getMessage());

        } finally {
            // 记录监控指标
            long responseTime = System.currentTimeMillis() - startTime;
            VipMetricsService.me.recordOrderCreate(success, responseTime);
        }
    }

    /**
     * 查询订单详情
     */
    public RetKit getOrder(Integer userId, String orderNo) {
        try {
            if (userId == null || StrKit.isBlank(orderNo)) {
                return RetKit.fail("参数不能为空");
            }

            VipOrder order = VipOrder.dao.findFirst(
                "SELECT * FROM vip_order WHERE order_no = ? AND user_id = ?",
                orderNo, userId
            );

            if (order == null) {
                return RetKit.fail("订单不存在");
            }

            Map<String, Object> orderInfo = buildOrderInfo(order);

            return RetKit.ok("查询成功").set("order", orderInfo);

        } catch (Exception e) {
            LogKit.error("查询VIP订单失败", e);
            return RetKit.fail("查询订单失败");
        }
    }

    /**
     * 查询用户的订单列表
     */
    public RetKit getUserOrders(Integer userId, int page, int pageSize) {
        try {
            if (userId == null) {
                return RetKit.fail("用户ID不能为空");
            }

            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 10;

            String sql = "SELECT * FROM vip_order WHERE user_id = ? ORDER BY create_time DESC LIMIT ?, ?";
            int offset = (page - 1) * pageSize;

            java.util.List<VipOrder> orders = VipOrder.dao.find(sql, userId, offset, pageSize);

            java.util.List<Map<String, Object>> orderList = new java.util.ArrayList<>();
            for (VipOrder order : orders) {
                orderList.add(buildOrderInfo(order));
            }

            return RetKit.ok("查询成功")
                .set("orders", orderList)
                .set("page", page)
                .set("pageSize", pageSize);

        } catch (Exception e) {
            LogKit.error("查询用户订单列表失败", e);
            return RetKit.fail("查询失败");
        }
    }

    /**
     * 构建订单对象
     */
    private VipOrder buildOrder(Integer userId, String plan, String purchaseType, String idempotentKey) {
        VipOrder order = new VipOrder();
        Date now = new Date();

        // 价格按 plan + purchaseType
        int amount = VipPriceConfigService.me.getPlanPrice(plan, purchaseType);

        order.setOrderNo(generateOrderNo())
             .setUserId(userId)
             .setPlan(plan)
             .setTier(planToTier(plan).name())
             .setAmount(amount)
             .setChannel(VipOrder.CHANNEL_WECHAT)
             .setStatus(VipOrder.STATUS_CREATED)
             .setCreateTime(now)
             .setUpdateTime(now);

        // 订阅订单标识（持久化字段将在后续表结构变更后补充）
        // order.set("subscription_type", "subscribe".equals(purchaseType) ? plan : "once");
        // order.set("auto_renew", "subscribe".equals(purchaseType) ? 1 : 0);

        // 设置订单过期时间（30分钟后）
        // Date expireTime = new Date(now.getTime() + 30 * 60 * 1000);
        // order.set("expire_time", expireTime);

        // 写入订阅字段
        if ("subscribe".equals(purchaseType)) {
            order.setSubscriptionType(plan);
            order.setAutoRenew(true);
        } else {
            order.setSubscriptionType(VipOrder.SUBSCRIPTION_ONCE);
            order.setAutoRenew(false);
        }

        // 幂等键
        // if (StrKit.notBlank(idempotentKey)) {
        //     order.set("idempotent_key", idempotentKey);
        // }

        return order;
    }

    /**
     * 构建订单信息
     */
    private Map<String, Object> buildOrderInfo(VipOrder order) {
        Map<String, Object> info = new HashMap<>();
        info.put("orderNo", order.getOrderNo());
        info.put("plan", order.getPlan());
        info.put("planName", getPlanName(order.getPlan()));
        info.put("amount", order.getAmountYuan());
        info.put("status", order.getStatus());
        info.put("statusName", getStatusName(order.getStatus()));
        info.put("createTime", order.getCreateTime());
        info.put("paidTime", order.getPaidTime());
        info.put("channelTradeNo", order.getChannelTradeNo());

        return info;
    }

    /**
     * 生成支付参数
     */
    private Map<String, Object> generatePayParams(VipOrder order) {
        try {
            if (!WechatPayService.me.isAvailable()) {
                LogKit.warn("微信支付服务不可用，返回模拟参数");
                return createMockPayParams(order);
            }

            return WechatPayService.me.createAppOrder(order);

        } catch (Exception e) {
            LogKit.error("生成支付参数失败", e);
            return createMockPayParams(order);
        }
    }

    /**
     * 创建模拟支付参数（用于测试）
     */
    private Map<String, Object> createMockPayParams(VipOrder order) {
        Map<String, Object> params = new HashMap<>();
        params.put("appid", "wx_mock_appid");
        params.put("partnerid", "mock_mchid");
        params.put("prepayid", "wx_mock_prepay_" + System.currentTimeMillis());
        params.put("package", "Sign=WXPay");
        params.put("noncestr", "mock_nonce_" + System.currentTimeMillis());
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("sign", "mock_signature");
        params.put("_mock", true);

        return params;
    }

    /**
     * 查找用户未支付的订单
     */
    private VipOrder findUnpaidOrder(Integer userId) {
        return VipOrder.dao.findFirst(
            "SELECT * FROM vip_order WHERE user_id = ? AND status = ? ORDER BY create_time DESC LIMIT 1",
            userId, VipOrder.STATUS_CREATED
        );
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "VIP" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
    }

    /**
     * 验证套餐类型
     */
    private boolean isValidPlan(String plan) {
        return VipOrder.PLAN_MONTHLY.equals(plan) || VipOrder.PLAN_YEARLY.equals(plan);
    }

    /**
     * 套餐转用户等级
     */
    private UserTier planToTier(String plan) {
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                return UserTier.VIP_MONTHLY;
            case VipOrder.PLAN_YEARLY:
                return UserTier.VIP_YEARLY;
            default:
                return UserTier.VIP_MONTHLY;
        }
    }

    /**
     * 获取套餐价格（使用价格配置服务）
     */
    private int getPlanPrice(String plan) {
        return VipPriceConfigService.me.getPlanPrice(plan);
    }

    /**
     * 计算实际支付价格（考虑促销活动）
     */
    private int calculateActualPrice(String plan, Integer userId) {
        return VipPriceConfigService.me.calculateActualPrice(plan, userId);
    }

    /**
     * 获取套餐名称
     */
    private String getPlanName(String plan) {
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                return "VIP月度会员";
            case VipOrder.PLAN_YEARLY:
                return "VIP年度会员";
            default:
                return "VIP会员";
        }
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(String status) {
        switch (status) {
            case VipOrder.STATUS_CREATED:
                return "待支付";
            case VipOrder.STATUS_PAID:
                return "已支付";
            case VipOrder.STATUS_CLOSED:
                return "已关闭";
            case VipOrder.STATUS_FAILED:
                return "支付失败";
            default:
                return "未知状态";
        }
    }

    /**
     * 根据幂等键查找订单
     */
    private VipOrder findOrderByIdempotentKey(String idempotentKey) {
        // TODO: 实现幂等键查询，需要数据库字段支持
        // return VipOrder.dao.findFirst("SELECT * FROM vip_order WHERE idempotent_key = ?", idempotentKey);
        return null;
    }

    /**
     * 检查订单是否已过期
     */
    private boolean isOrderExpired(VipOrder order) {
        if (order == null) {
            return true;
        }

        // TODO: 实现过期时间检查，需要数据库字段支持
        // Date expireTime = order.getExpireTime();
        // if (expireTime != null) {
        //     return expireTime.before(new Date());
        // }

        // 临时实现：检查创建时间是否超过30分钟
        Date createTime = order.getCreateTime();
        if (createTime != null) {
            long diffMinutes = (System.currentTimeMillis() - createTime.getTime()) / (1000 * 60);
            return diffMinutes > 30;
        }

        return false;
    }

    /**
     * 关闭过期订单
     */
    private void closeExpiredOrder(VipOrder order) {
        try {
            order.setStatus(VipOrder.STATUS_CLOSED)
                 .setUpdateTime(new Date());

            boolean success = order.update();
            if (success) {
                LogKit.info("过期订单已关闭: " + order.getOrderNo());
            } else {
                LogKit.error("关闭过期订单失败: " + order.getOrderNo());
            }

        } catch (Exception e) {
            LogKit.error("关闭过期订单异常: " + order.getOrderNo(), e);
        }
    }
}
