package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.enums.UserTier;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.VipOrder;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * VIP发货服务
 * 处理VIP开通和续期逻辑
 */
public class VipDeliveryService {
    
    public static final VipDeliveryService me = new VipDeliveryService();
    
    /**
     * 发货VIP（开通或续期）
     */
    public boolean deliverVip(VipOrder order) {
        long startTime = System.currentTimeMillis();
        boolean success = false;

        try {
            LogKit.info("开始VIP发货处理: " + order.getOrderNo());
            
            // 检查订单状态
            if (!VipOrder.STATUS_PAID.equals(order.getStatus())) {
                LogKit.error("订单状态不正确，无法发货: " + order.getStatus());
                return false;
            }
            
            // 获取用户
            User user = User.dao.findById(order.getUserId());
            if (user == null) {
                LogKit.error("用户不存在: " + order.getUserId());
                return false;
            }
            
            // 计算新的VIP等级和到期时间
            UserTier newTier = UserTier.fromString(order.getTier());
            Date newExpireTime = calculateNewExpireTime(user, order.getPlan());
            
            // 记录变更前状态
            UserTier oldTier = user.getUserTierEnum();
            Date oldExpireTime = user.getVipExpireTime();
            
            // 使用事务更新用户VIP信息
            boolean txSuccess = Db.tx(() -> {
                // 更新用户VIP信息
                user.setUserTier(newTier.name())
                    .setVipExpireTime(newExpireTime)
                    .set("update_time", new Date());
                
                boolean updateSuccess = user.update();
                if (!updateSuccess) {
                    LogKit.error("更新用户VIP信息失败: " + user.getUserId());
                    return false;
                }
                
                // 记录发货日志（可选）
                logDelivery(order, oldTier, newTier, oldExpireTime, newExpireTime);
                
                return true;
            });
            
            if (txSuccess) {
                LogKit.info("VIP发货成功: 用户" + user.getUserId() +
                           ", 等级" + oldTier.name() + "->" + newTier.name() +
                           ", 到期时间" + newExpireTime);
                success = true;
                return true;
            } else {
                LogKit.error("VIP发货失败: " + order.getOrderNo());
                return false;
            }

        } catch (Exception e) {
            LogKit.error("VIP发货处理异常: " + order.getOrderNo(), e);
            return false;

        } finally {
            // 记录监控指标
            long responseTime = System.currentTimeMillis() - startTime;
            VipMetricsService.me.recordVipDelivery(success, responseTime);
        }
    }
    
    /**
     * 计算新的VIP到期时间
     */
    private Date calculateNewExpireTime(User user, String plan) {
        Date currentExpireTime = user.getVipExpireTime();
        Date now = new Date();
        
        // 确定起始时间
        Date startTime;
        if (currentExpireTime != null && currentExpireTime.after(now)) {
            // 如果当前VIP未过期，从到期时间开始叠加
            startTime = currentExpireTime;
            LogKit.info("用户VIP未过期，从到期时间叠加: " + currentExpireTime);
        } else {
            // 如果当前VIP已过期或没有VIP，从现在开始
            startTime = now;
            LogKit.info("用户VIP已过期或首次开通，从当前时间开始");
        }
        
        // 根据套餐类型计算新的到期时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                calendar.add(Calendar.MONTH, 1);
                break;
            case VipOrder.PLAN_YEARLY:
                calendar.add(Calendar.YEAR, 1);
                break;
            default:
                LogKit.error("未知的套餐类型: " + plan);
                calendar.add(Calendar.MONTH, 1); // 默认1个月
        }
        
        return calendar.getTime();
    }
    
    /**
     * 记录发货日志
     */
    private void logDelivery(VipOrder order, UserTier oldTier, UserTier newTier, Date oldExpireTime, Date newExpireTime) {
        try {
            String action = (oldTier == UserTier.FREE) ? "open" : "extend";
            
            String sql = "INSERT INTO vip_delivery_log (order_no, user_id, action, from_tier, to_tier, from_expire, to_expire, create_time) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            Db.update(sql, 
                order.getOrderNo(),
                order.getUserId(),
                action,
                oldTier.name(),
                newTier.name(),
                oldExpireTime,
                newExpireTime,
                new Date()
            );
            
            LogKit.info("VIP发货日志记录成功: " + order.getOrderNo());
            
        } catch (Exception e) {
            LogKit.error("记录VIP发货日志失败: " + order.getOrderNo(), e);
            // 不影响主流程，只记录错误
        }
    }
    
    /**
     * 检查并处理过期的VIP用户
     * 建议定时任务调用
     */
    public void processExpiredVipUsers() {
        int processedCount = 0;

        try {
            LogKit.info("开始处理过期VIP用户");

            String sql = "SELECT userId FROM user WHERE userTier != 'FREE' AND vipExpireTime < NOW()";

            List<Record> expiredUsers = Db.query(sql);

            for (Record record : expiredUsers) {
                Integer userId = (Integer) record.get("userId");
                try {
                    User user = User.dao.findById(userId);
                    if (user != null) {
                        user.setUserTier(UserTier.FREE.name()).update();
                        LogKit.info("用户VIP已过期，降级为FREE: " + userId);
                        processedCount++;
                    }
                } catch (Exception e) {
                    LogKit.error("处理过期VIP用户失败: " + userId, e);
                }
            }

            LogKit.info("过期VIP用户处理完成，共处理 " + processedCount + " 个用户");

        } catch (Exception e) {
            LogKit.error("处理过期VIP用户异常", e);
        } finally {
            // 记录监控指标
            VipMetricsService.me.recordVipExpireProcess(processedCount);
        }
    }
}
