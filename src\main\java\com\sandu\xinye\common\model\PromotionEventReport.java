package com.sandu.xinye.common.model;

import com.sandu.xinye.common.model.base.BasePromotionEventReport;

/**
 * 推广事件上报记录模型
 * 用于记录向代理商平台成功上报的事件，作为统计与审计的唯一口径
 */
@SuppressWarnings("serial")
public class PromotionEventReport extends BasePromotionEventReport<PromotionEventReport> {
	public static final PromotionEventReport dao = new PromotionEventReport().dao();

	// 事件类型常量
	public static final int ACTION_TYPE_REGISTER_BIND = 1;  // 注册绑定
	public static final int ACTION_TYPE_FIRST_PRINT = 2;    // 首次有效打印
	public static final int ACTION_TYPE_VIP_SUBSCRIBE = 3;  // VIP订阅

	// 成功状态常量
	public static final int SUCCESS_TRUE = 1;   // 成功
	public static final int SUCCESS_FALSE = 0;  // 失败
}
