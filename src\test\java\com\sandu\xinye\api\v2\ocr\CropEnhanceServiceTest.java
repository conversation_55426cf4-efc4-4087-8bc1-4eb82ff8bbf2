package com.sandu.xinye.api.v2.ocr;

import com.sandu.xinye.common.kit.RetKit;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * 文档切边矫正服务单元测试
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public class CropEnhanceServiceTest {
    
    private CropEnhanceService service;
    
    @Before
    public void setUp() {
        service = CropEnhanceService.me;
    }
    
    @Test
    public void testValidateImageFile_NullFile() {
        // 测试空文件验证
        try {
            java.lang.reflect.Method method = CropEnhanceService.class.getDeclaredMethod("validateImageFile", 
                com.jfinal.upload.UploadFile.class);
            method.setAccessible(true);
            
            RetKit result = (RetKit) method.invoke(service, (Object) null);
            
            assertFalse("空文件应该验证失败", result.isTrue("success"));
            assertEquals("错误信息应该正确", "请上传图片文件", result.getStr("msg"));
            
        } catch (Exception e) {
            fail("测试异常: " + e.getMessage());
        }
    }
    
    @Test
    public void testGetFileExtension() {
        try {
            java.lang.reflect.Method method = CropEnhanceService.class.getDeclaredMethod("getFileExtension", String.class);
            method.setAccessible(true);
            
            assertEquals("jpg", method.invoke(service, "test.jpg"));
            assertEquals("png", method.invoke(service, "image.png"));
            assertEquals("jpeg", method.invoke(service, "photo.jpeg"));
            assertEquals("", method.invoke(service, "noextension"));
            assertEquals("", method.invoke(service, ""));
            
        } catch (Exception e) {
            fail("测试异常: " + e.getMessage());
        }
    }
    
    @Test
    public void testConvertCropPoints() {
        try {
            java.lang.reflect.Method method = CropEnhanceService.class.getDeclaredMethod("convertCropPoints", int[].class);
            method.setAccessible(true);
            
            int[] position = {100, 150, 1800, 120, 1850, 2800, 80, 2850};
            @SuppressWarnings("unchecked")
            java.util.List<java.util.Map<String, Integer>> result = 
                (java.util.List<java.util.Map<String, Integer>>) method.invoke(service, position);
            
            assertEquals("应该返回4个点", 4, result.size());
            
            // 检查第一个点
            assertEquals("第一个点x坐标", Integer.valueOf(100), result.get(0).get("x"));
            assertEquals("第一个点y坐标", Integer.valueOf(150), result.get(0).get("y"));
            
            // 检查最后一个点
            assertEquals("最后一个点x坐标", Integer.valueOf(80), result.get(3).get("x"));
            assertEquals("最后一个点y坐标", Integer.valueOf(2850), result.get(3).get("y"));
            
        } catch (Exception e) {
            fail("测试异常: " + e.getMessage());
        }
    }
    
    @Test
    public void testConvertCropPoints_EmptyArray() {
        try {
            java.lang.reflect.Method method = CropEnhanceService.class.getDeclaredMethod("convertCropPoints", int[].class);
            method.setAccessible(true);
            
            int[] position = {};
            @SuppressWarnings("unchecked")
            java.util.List<java.util.Map<String, Integer>> result = 
                (java.util.List<java.util.Map<String, Integer>>) method.invoke(service, position);
            
            assertEquals("空数组应该返回空列表", 0, result.size());
            
        } catch (Exception e) {
            fail("测试异常: " + e.getMessage());
        }
    }
    
    @Test
    public void testConvertCropPoints_OddLength() {
        try {
            java.lang.reflect.Method method = CropEnhanceService.class.getDeclaredMethod("convertCropPoints", int[].class);
            method.setAccessible(true);
            
            int[] position = {100, 150, 1800}; // 奇数长度
            @SuppressWarnings("unchecked")
            java.util.List<java.util.Map<String, Integer>> result = 
                (java.util.List<java.util.Map<String, Integer>>) method.invoke(service, position);
            
            assertEquals("奇数长度应该只处理完整的点对", 1, result.size());
            assertEquals("第一个点x坐标", Integer.valueOf(100), result.get(0).get("x"));
            assertEquals("第一个点y坐标", Integer.valueOf(150), result.get(0).get("y"));
            
        } catch (Exception e) {
            fail("测试异常: " + e.getMessage());
        }
    }
}
