# Redis原子操作优化方案

## 当前状况

目前在 `WechatPaySignatureUtil.checkNonceReplay()` 方法中，使用了 get + setex 两步操作来检查和记录nonce，存在极小概率的并发穿透风险。

```java
// 当前实现（有小概率并发问题）
String existingValue = cache.get(key);
if (existingValue == null) {
    cache.setex(key, NONCE_EXPIRE, "1");
    return true;
}
```

## 优化方案

### 方案一：使用Redis Lua脚本（推荐）

#### 1. Lua脚本实现

```lua
-- nonce_check.lua
-- KEYS[1]: nonce的key
-- ARGV[1]: 过期时间（秒）
-- ARGV[2]: 设置的值

if redis.call('exists', KEYS[1]) == 0 then
    redis.call('setex', KEYS[1], ARGV[1], ARGV[2])
    return 1
else
    return 0
end
```

#### 2. Java实现

```java
public static boolean checkNonceReplayAtomic(String timestamp, String nonce) {
    if (!RedisConfig.isAvailable()) {
        LogKit.warn("Redis不可用，跳过nonce重放检查");
        return true;
    }
    
    try {
        String key = NONCE_PREFIX + timestamp + ":" + nonce;
        Cache cache = Redis.use();
        
        // 使用Jedis执行Lua脚本
        try (Jedis jedis = cache.getJedis()) {
            String luaScript = 
                "if redis.call('exists', KEYS[1]) == 0 then " +
                "  redis.call('setex', KEYS[1], ARGV[1], ARGV[2]) " +
                "  return 1 " +
                "else " +
                "  return 0 " +
                "end";
            
            Object result = jedis.eval(luaScript, 
                Arrays.asList(key), 
                Arrays.asList(String.valueOf(NONCE_EXPIRE), "1"));
            
            if ("1".equals(result.toString())) {
                LogKit.info("记录新的nonce: " + nonce);
                return true;
            } else {
                LogKit.warn("检测到重复的nonce，可能是重放攻击: " + nonce);
                return false;
            }
        }
    } catch (Exception e) {
        LogKit.error("nonce重放检查失败", e);
        return true;
    }
}
```

### 方案二：使用SET NX EX命令

如果使用支持 SET NX EX 的Redis客户端（如Jedis 2.9+）：

```java
public static boolean checkNonceReplayNX(String timestamp, String nonce) {
    try {
        String key = NONCE_PREFIX + timestamp + ":" + nonce;
        
        // 使用Jedis的set方法，带NX和EX参数
        try (Jedis jedis = Redis.use().getJedis()) {
            SetParams params = SetParams.setParams().nx().ex(NONCE_EXPIRE);
            String result = jedis.set(key, "1", params);
            
            return "OK".equals(result);
        }
    } catch (Exception e) {
        LogKit.error("nonce重放检查失败", e);
        return true;
    }
}
```

### 方案三：本地缓存辅助（降级方案）

在Redis不可用时，使用本地JVM缓存作为降级方案：

```java
private static final ConcurrentHashMap<String, Long> localNonceCache = new ConcurrentHashMap<>();
private static final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();

static {
    // 定期清理过期的nonce
    cleanupExecutor.scheduleWithFixedDelay(() -> {
        long now = System.currentTimeMillis();
        localNonceCache.entrySet().removeIf(entry -> 
            now - entry.getValue() > NONCE_EXPIRE * 1000);
    }, 60, 60, TimeUnit.SECONDS);
}

public static boolean checkNonceReplayLocal(String timestamp, String nonce) {
    String key = timestamp + ":" + nonce;
    long now = System.currentTimeMillis();
    
    Long existing = localNonceCache.putIfAbsent(key, now);
    if (existing == null) {
        // 新的nonce
        return true;
    } else {
        // 检查是否过期
        if (now - existing > NONCE_EXPIRE * 1000) {
            // 已过期，更新时间戳
            localNonceCache.put(key, now);
            return true;
        }
        // 重复的nonce
        return false;
    }
}
```

## 实施建议

1. **短期方案**（已实施）
   - 使用 get + setex 两步操作
   - 在注释中明确标注存在小概率并发问题
   - 对于支付回调等关键场景，可接受这种权衡

2. **中期方案**（建议Q2实施）
   - 升级JFinal Redis插件，支持Lua脚本执行
   - 或直接使用Jedis客户端执行原子操作
   - 实施方案一或方案二

3. **长期方案**（建议年度规划）
   - 评估是否需要更强的防重放机制
   - 考虑引入分布式锁框架（如Redisson）
   - 统一处理所有原子操作场景

## 测试验证

### 并发测试

```java
@Test
public void testConcurrentNonceCheck() throws Exception {
    int threadCount = 100;
    String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
    String nonce = "test-nonce-" + UUID.randomUUID();
    
    CountDownLatch latch = new CountDownLatch(threadCount);
    AtomicInteger successCount = new AtomicInteger(0);
    
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);
    
    for (int i = 0; i < threadCount; i++) {
        executor.submit(() -> {
            try {
                if (WechatPaySignatureUtil.checkNonceReplay(timestamp, nonce)) {
                    successCount.incrementAndGet();
                }
            } finally {
                latch.countDown();
            }
        });
    }
    
    latch.await(10, TimeUnit.SECONDS);
    
    // 理想情况下，只有1个线程应该成功
    assertEquals("只应有一个请求成功", 1, successCount.get());
}
```

## 风险评估

- **当前风险**：极低概率的并发穿透（估计 < 0.01%）
- **影响范围**：仅影响完全同时的重复请求
- **缓解措施**：配合时间戳验证，实际风险可控

## 参考资料

- [Redis Lua脚本文档](https://redis.io/docs/manual/programmability/eval-intro/)
- [Jedis SET命令文档](https://github.com/redis/jedis)
- [分布式锁最佳实践](https://redis.io/docs/manual/patterns/distributed-locks/)