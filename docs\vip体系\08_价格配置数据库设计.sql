-- VIP价格配置数据库设计
-- 创建价格配置和促销活动相关的数据表

-- 1. 创建VIP价格配置表
CREATE TABLE IF NOT EXISTS `vip_price_config` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `plan` VARCHAR(20) NOT NULL COMMENT '套餐类型(monthly/yearly)',
  `price` INT NOT NULL COMMENT '当前价格(分)',
  `original_price` INT NOT NULL COMMENT '原价(分)',
  `description` VARCHAR(200) NULL COMMENT '价格描述',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  `update_time` DATETIME NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_plan` (`plan`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP价格配置表';

-- 插入默认价格配置
INSERT INTO `vip_price_config` (`plan`, `price`, `original_price`, `description`, `sort_order`, `create_time`, `update_time`) VALUES
('monthly', 990, 990, '月度VIP会员', 1, NOW(), NOW()),
('yearly', 9900, 9900, '年度VIP会员', 2, NOW(), NOW());

-- 2. 创建VIP促销活动表
CREATE TABLE IF NOT EXISTS `vip_promotion` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '促销ID',
  `promotion_id` VARCHAR(32) NOT NULL COMMENT '促销活动ID',
  `name` VARCHAR(100) NOT NULL COMMENT '促销活动名称',
  `plan` VARCHAR(20) NOT NULL COMMENT '适用套餐类型',
  `discount_type` VARCHAR(20) NOT NULL COMMENT '折扣类型(percentage/fixed)',
  `discount_value` INT NOT NULL COMMENT '折扣值(百分比或固定金额分)',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `end_time` DATETIME NOT NULL COMMENT '结束时间',
  `max_uses` INT NULL COMMENT '最大使用次数(NULL表示无限制)',
  `used_count` INT NOT NULL DEFAULT 0 COMMENT '已使用次数',
  `description` VARCHAR(500) NULL COMMENT '活动描述',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  `update_time` DATETIME NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_promotion_id` (`promotion_id`),
  KEY `idx_plan_active` (`plan`, `is_active`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP促销活动表';

-- 3. 创建用户促销使用记录表
CREATE TABLE IF NOT EXISTS `vip_promotion_usage` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '使用记录ID',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `promotion_id` VARCHAR(32) NOT NULL COMMENT '促销活动ID',
  `order_no` VARCHAR(32) NOT NULL COMMENT '订单号',
  `original_price` INT NOT NULL COMMENT '原价(分)',
  `discount_amount` INT NOT NULL COMMENT '折扣金额(分)',
  `final_price` INT NOT NULL COMMENT '最终价格(分)',
  `use_time` DATETIME NOT NULL COMMENT '使用时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_promotion_order` (`user_id`, `promotion_id`, `order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_promotion_id` (`promotion_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_use_time` (`use_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户促销使用记录表';

-- 4. 为vip_order表添加促销相关字段
ALTER TABLE `vip_order` 
ADD COLUMN `promotion_id` VARCHAR(32) NULL COMMENT '使用的促销活动ID' AFTER `amount`,
ADD COLUMN `original_amount` INT NULL COMMENT '原价金额(分)' AFTER `promotion_id`,
ADD COLUMN `discount_amount` INT NULL COMMENT '折扣金额(分)' AFTER `original_amount`;

-- 5. 创建价格历史记录表
CREATE TABLE IF NOT EXISTS `vip_price_history` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `plan` VARCHAR(20) NOT NULL COMMENT '套餐类型',
  `old_price` INT NULL COMMENT '原价格(分)',
  `new_price` INT NOT NULL COMMENT '新价格(分)',
  `change_reason` VARCHAR(200) NULL COMMENT '变更原因',
  `operator_id` INT NULL COMMENT '操作员ID',
  `operator_type` VARCHAR(20) NOT NULL COMMENT '操作员类型(admin/system)',
  `change_time` DATETIME NOT NULL COMMENT '变更时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan` (`plan`),
  KEY `idx_change_time` (`change_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP价格变更历史表';

-- 6. 创建促销活动统计视图
CREATE OR REPLACE VIEW `vip_promotion_stats` AS
SELECT 
    p.promotion_id,
    p.name,
    p.plan,
    p.discount_type,
    p.discount_value,
    p.start_time,
    p.end_time,
    p.max_uses,
    p.used_count,
    CASE 
        WHEN p.max_uses IS NULL THEN '无限制'
        ELSE CONCAT(p.used_count, '/', p.max_uses)
    END as usage_info,
    CASE 
        WHEN NOW() < p.start_time THEN '未开始'
        WHEN NOW() > p.end_time THEN '已结束'
        WHEN p.max_uses IS NOT NULL AND p.used_count >= p.max_uses THEN '已用完'
        ELSE '进行中'
    END as status,
    IFNULL(SUM(pu.discount_amount), 0) as total_discount_amount,
    COUNT(pu.id) as actual_usage_count
FROM vip_promotion p
LEFT JOIN vip_promotion_usage pu ON p.promotion_id = pu.promotion_id
GROUP BY p.id
ORDER BY p.create_time DESC;

-- 7. 创建价格配置触发器（记录价格变更历史）
DELIMITER //
CREATE TRIGGER vip_price_config_history 
AFTER UPDATE ON vip_price_config
FOR EACH ROW
BEGIN
    IF OLD.price != NEW.price THEN
        INSERT INTO vip_price_history (plan, old_price, new_price, change_reason, operator_type, change_time)
        VALUES (NEW.plan, OLD.price, NEW.price, '价格配置更新', 'admin', NOW());
    END IF;
END //
DELIMITER ;

-- 8. 创建促销活动使用触发器（更新使用次数）
DELIMITER //
CREATE TRIGGER vip_promotion_usage_counter 
AFTER INSERT ON vip_promotion_usage
FOR EACH ROW
BEGIN
    UPDATE vip_promotion 
    SET used_count = used_count + 1, update_time = NOW()
    WHERE promotion_id = NEW.promotion_id;
END //
DELIMITER ;

-- 9. 创建存储过程：自动清理过期促销活动
DELIMITER //
CREATE PROCEDURE CleanExpiredPromotions()
BEGIN
    DECLARE affected_rows INT DEFAULT 0;
    
    -- 禁用过期的促销活动
    UPDATE vip_promotion 
    SET is_active = 0, update_time = NOW()
    WHERE is_active = 1 
    AND end_time < NOW();
    
    SET affected_rows = ROW_COUNT();
    
    SELECT CONCAT('已禁用 ', affected_rows, ' 个过期促销活动') as result;
END //
DELIMITER ;

-- 10. 创建存储过程：获取套餐价格（含促销）
DELIMITER //
CREATE PROCEDURE GetPlanPrice(IN plan_type VARCHAR(20), IN user_id INT, OUT final_price INT, OUT promotion_info JSON)
BEGIN
    DECLARE original_price INT DEFAULT 0;
    DECLARE discount_amount INT DEFAULT 0;
    DECLARE promotion_id VARCHAR(32) DEFAULT NULL;
    DECLARE promotion_name VARCHAR(100) DEFAULT NULL;
    
    -- 获取原价
    SELECT price INTO original_price 
    FROM vip_price_config 
    WHERE plan = plan_type AND is_active = 1;
    
    -- 查找适用的促销活动
    SELECT p.promotion_id, p.name, 
           CASE 
               WHEN p.discount_type = 'percentage' THEN original_price * p.discount_value / 100
               WHEN p.discount_type = 'fixed' THEN p.discount_value
               ELSE 0
           END as discount
    INTO promotion_id, promotion_name, discount_amount
    FROM vip_promotion p
    WHERE p.plan = plan_type 
    AND p.is_active = 1
    AND p.start_time <= NOW() 
    AND p.end_time >= NOW()
    AND (p.max_uses IS NULL OR p.used_count < p.max_uses)
    AND NOT EXISTS (
        SELECT 1 FROM vip_promotion_usage pu 
        WHERE pu.user_id = user_id AND pu.promotion_id = p.promotion_id
    )
    ORDER BY discount DESC
    LIMIT 1;
    
    -- 计算最终价格
    SET final_price = original_price - IFNULL(discount_amount, 0);
    IF final_price < 1 THEN
        SET final_price = 1; -- 最低1分
    END IF;
    
    -- 构造促销信息JSON
    IF promotion_id IS NOT NULL THEN
        SET promotion_info = JSON_OBJECT(
            'promotion_id', promotion_id,
            'promotion_name', promotion_name,
            'original_price', original_price,
            'discount_amount', discount_amount,
            'final_price', final_price
        );
    ELSE
        SET promotion_info = JSON_OBJECT(
            'original_price', original_price,
            'final_price', final_price
        );
    END IF;
END //
DELIMITER ;

-- 11. 创建索引优化查询性能
CREATE INDEX `idx_promotion_plan_time_active` ON `vip_promotion` (`plan`, `start_time`, `end_time`, `is_active`);
CREATE INDEX `idx_promotion_usage_user_time` ON `vip_promotion_usage` (`user_id`, `use_time`);
CREATE INDEX `idx_order_promotion` ON `vip_order` (`promotion_id`);

-- 12. 插入示例促销活动
INSERT INTO `vip_promotion` (`promotion_id`, `name`, `plan`, `discount_type`, `discount_value`, `start_time`, `end_time`, `max_uses`, `description`, `create_time`, `update_time`) VALUES
('PROMO_NEW_USER', '新用户专享', 'monthly', 'percentage', 50, '2024-01-01 00:00:00', '2024-12-31 23:59:59', NULL, '新用户首次购买月度VIP享5折优惠', NOW(), NOW()),
('PROMO_YEAR_SALE', '年度大促', 'yearly', 'fixed', 2000, '2024-06-01 00:00:00', '2024-06-30 23:59:59', 1000, '年度VIP限时优惠，立减20元', NOW(), NOW());

-- 使用说明：
-- 1. 执行此脚本前请备份数据库
-- 2. 可通过 GetPlanPrice 存储过程获取含促销的价格
-- 3. 定期执行 CleanExpiredPromotions() 清理过期促销
-- 4. 通过 vip_promotion_stats 视图查看促销统计
-- 5. 价格变更会自动记录到 vip_price_history 表
