#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推广事件上报测试脚本
测试代理商平台推广事件上报接口
"""

import hashlib
import time
import json
import requests
import argparse


def calculate_sign(user, secret, timestamp):
    """计算签名"""
    sign_str = user + secret + timestamp
    return hashlib.sha1(sign_str.encode()).hexdigest()


def test_promotion_event(user_id, promotion_code, action_type=1):
    """测试推广事件上报"""
    
    # 配置参数（与Java代码中的配置保持一致）
    url = "https://support.xpyun.net/api/admin/member/promotionCall"
    secret = "123!@#wqee"
    project = "xprinter"
    timeout = 5
    
    # 生成时间戳
    timestamp = str(int(time.time() * 1000))
    
    # 计算签名
    sign = calculate_sign(str(user_id), secret, timestamp)
    
    # 构造请求体
    payload = {
        "user": str(user_id),
        "timestamp": timestamp,
        "sign": sign,
        "promotionCode": promotion_code,
        "project": project,
        "actionType": action_type
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=" * 60)
    print("推广事件上报测试")
    print("=" * 60)
    print(f"接口地址: {url}")
    print(f"HTTP方法: POST")
    print(f"超时时间: {timeout}秒")
    print()
    
    print("请求参数:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    print("请求头:")
    print(json.dumps(headers, indent=2, ensure_ascii=False))
    print()
    
    try:
        print("发送POST请求...")
        start_time = time.time()
        
        # 发送POST请求
        response = requests.post(
            url, 
            json=payload, 
            headers=headers, 
            timeout=timeout
        )
        
        duration = int((time.time() - start_time) * 1000)
        
        print(f"响应时间: {duration}ms")
        print(f"HTTP状态码: {response.status_code}")
        print()
        
        # 解析响应
        try:
            response_json = response.json()
            print("响应内容:")
            print(json.dumps(response_json, indent=2, ensure_ascii=False))
            
            # 分析业务结果
            print()
            print("=" * 60)
            print("结果分析:")
            print("=" * 60)
            
            if response.status_code == 200:
                data = response_json.get("data")
                message = response_json.get("message", "")
                
                if data is True:
                    print("✅ 推广事件上报成功")
                elif data is False:
                    print("❌ 推广事件上报失败")
                    print(f"   失败原因: {message}")
                else:
                    print(f"⚠️  未知状态: data={data}")
                    
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                
        except json.JSONDecodeError:
            print("响应内容 (非JSON):")
            print(response.text)
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def main():
    parser = argparse.ArgumentParser(description="推广事件上报测试脚本")
    parser.add_argument("--user", type=int, default=173972, help="用户ID")
    parser.add_argument("--code", type=str, default="RIPQFW", help="推广码")
    parser.add_argument("--action", type=int, default=1, help="事件类型 (1=注册绑定, 2=首次打印, 3=VIP订阅)")
    
    args = parser.parse_args()
    
    test_promotion_event(args.user, args.code, args.action)


if __name__ == "__main__":
    main()