package com.sandu.xinye.common.service;

import com.alibaba.fastjson.JSONObject;
import java.util.Map;

/**
 * 微信支付API响应封装类
 */
public class WechatPayResponse {
    
    private boolean success;
    private String errorMessage;
    private JSONObject data;
    private JSONObject errorData;
    private Map<String, String> headers;
    
    private WechatPayResponse() {}
    
    /**
     * 创建成功响应
     */
    public static WechatPayResponse success(JSONObject data, Map<String, String> headers) {
        WechatPayResponse response = new WechatPayResponse();
        response.success = true;
        response.data = data;
        response.headers = headers;
        return response;
    }
    
    /**
     * 创建错误响应
     */
    public static WechatPayResponse error(String errorMessage) {
        WechatPayResponse response = new WechatPayResponse();
        response.success = false;
        response.errorMessage = errorMessage;
        return response;
    }
    
    /**
     * 创建错误响应（带错误数据）
     */
    public static WechatPayResponse error(String errorMessage, JSONObject errorData) {
        WechatPayResponse response = new WechatPayResponse();
        response.success = false;
        response.errorMessage = errorMessage;
        response.errorData = errorData;
        return response;
    }
    
    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return success;
    }
    
    /**
     * 获取错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }
    
    /**
     * 获取响应数据
     */
    public JSONObject getData() {
        return data;
    }
    
    /**
     * 获取错误数据
     */
    public JSONObject getErrorData() {
        return errorData;
    }
    
    /**
     * 获取响应头
     */
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    /**
     * 获取指定响应头
     */
    public String getHeader(String name) {
        return headers != null ? headers.get(name) : null;
    }
    
    /**
     * 获取字符串字段
     */
    public String getString(String key) {
        return data != null ? data.getString(key) : null;
    }
    
    /**
     * 获取整数字段
     */
    public Integer getInteger(String key) {
        return data != null ? data.getInteger(key) : null;
    }
    
    /**
     * 检查是否包含指定字段
     */
    public boolean containsKey(String key) {
        return data != null && data.containsKey(key);
    }
    
    @Override
    public String toString() {
        if (success) {
            return "WechatPayResponse{success=true, data=" + data + "}";
        } else {
            return "WechatPayResponse{success=false, errorMessage='" + errorMessage + "', errorData=" + errorData + "}";
        }
    }
}
