package com.sandu.xinye.common.model;

import com.sandu.xinye.common.model.base.BaseVipOrder;
import com.sandu.xinye.common.enums.UserTier;

import java.util.Date;

/**
 * VIP订单模型
 */
@SuppressWarnings("serial")
public class VipOrder extends BaseVipOrder<VipOrder> {
	public static final VipOrder dao = new VipOrder().dao();

	// 订单状态常量
	public static final String STATUS_CREATED = "created";
	public static final String STATUS_PAID = "paid";
	public static final String STATUS_CLOSED = "closed";
	public static final String STATUS_FAILED = "failed";

	// 套餐类型常量
	public static final String PLAN_MONTHLY = "monthly";
	public static final String PLAN_YEARLY = "yearly";

	// 订阅类型常量
	public static final String SUBSCRIPTION_ONCE = "once";
	public static final String SUBSCRIPTION_MONTHLY = "monthly";
	public static final String SUBSCRIPTION_YEARLY = "yearly";

	// 支付渠道常量
	public static final String CHANNEL_WECHAT = "wechat";

	/**
	 * 获取用户等级枚举
	 */
	public UserTier getTierEnum() {
		String tierStr = getTier();
		return UserTier.fromString(tierStr);
	}

	/**
	 * 判断订单是否已支付
	 */
	public boolean isPaid() {
		return STATUS_PAID.equals(getStatus());
	}

	/**
	 * 判断订单是否可支付
	 */
	public boolean canPay() {
		return STATUS_CREATED.equals(getStatus());
	}

	/**
	 * 获取金额（元）
	 */
	public double getAmountYuan() {
		Integer amount = getAmount();
		return amount == null ? 0.0 : amount / 100.0;
	}

	/**
	 * 设置金额（元）
	 */
	public VipOrder setAmountYuan(double yuan) {
		return setAmount((int) Math.round(yuan * 100));
	}

	// ---------- 订阅/签约相关便捷访问器 ----------
	public VipOrder setSubscriptionType(String type) { set("subscription_type", type); return this; }
	public String getSubscriptionType() { return getStr("subscription_type"); }

	public VipOrder setAutoRenew(boolean auto) { set("auto_renew", auto ? 1 : 0); return this; }
	public boolean isAutoRenew() { Integer v = getInt("auto_renew"); return v != null && v == 1; }

	public VipOrder setContractId(String contractId) { set("contract_id", contractId); return this; }
	public String getContractId() { return getStr("contract_id"); }

	// 可选：订单过期时间与幂等键（若表有字段则可使用）
	public VipOrder setExpireTime(Date d) { set("expire_time", d); return this; }
	public Date getExpireTime() { return get("expire_time"); }
	public VipOrder setIdempotentKey(String k) { set("idempotent_key", k); return this; }
	public String getIdempotentKey() { return getStr("idempotent_key"); }
	
	// 购买类型相关方法
	public VipOrder setPurchaseType(String purchaseType) { set("purchase_type", purchaseType); return this; }
	public String getPurchaseType() { return getStr("purchase_type"); }
}
