package com.sandu.xinye.api.v2.vip;

import com.jfinal.aop.Before;
import com.jfinal.core.Controller;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.Kv;
import com.sandu.xinye.common.interceptor.WechatSignatureInterceptor;
import com.sandu.xinye.common.service.WechatPapCallbackService;

/**
 * PAP 回调控制器（串接订阅/订单骨架）
 * 前缀：/api/v2/vip/pap
 * 
 * 安全增强：所有回调接口添加签名验证
 */
public class VipPapCallbackController extends Controller {

    /** 
     * 签约成功回调
     * POST /api/v2/vip/pap/notify/sign
     */
    @Before(WechatSignatureInterceptor.class)
    public void sign() {
        String body = getRawData();
        boolean ok = WechatPapCallbackService.me.handleSignCallback(body);
        LogKit.info("签约回调处理完成，结果: " + ok);
        
        if (ok) {
            renderJson(Kv.by("code", "SUCCESS").set("message", "成功"));
        } else {
            renderJson(Kv.by("code", "FAIL").set("message", "处理失败"));
        }
    }

    /** 
     * 解约回调
     * POST /api/v2/vip/pap/notify/terminate
     */
    @Before(WechatSignatureInterceptor.class)
    public void terminate() {
        String body = getRawData();
        boolean ok = WechatPapCallbackService.me.handleTerminateCallback(body);
        LogKit.info("解约回调处理完成，结果: " + ok);
        
        if (ok) {
            renderJson(Kv.by("code", "SUCCESS").set("message", "成功"));
        } else {
            renderJson(Kv.by("code", "FAIL").set("message", "处理失败"));
        }
    }

    /** 
     * 扣款回调
     * POST /api/v2/vip/pap/notify/deduct
     */
    @Before(WechatSignatureInterceptor.class)
    public void deduct() {
        String body = getRawData();
        boolean ok = WechatPapCallbackService.me.handleDeductCallback(body);
        LogKit.info("扣款回调处理完成，结果: " + ok);
        
        if (ok) {
            renderJson(Kv.by("code", "SUCCESS").set("message", "成功"));
        } else {
            renderJson(Kv.by("code", "FAIL").set("message", "处理失败"));
        }
    }
}

