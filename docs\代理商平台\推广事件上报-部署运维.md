# 推广事件上报 - 部署运维指南

## 部署前检查清单

### 1. 数据库准备
- [ ] 执行数据库迁移脚本 `V2__create_promotion_event_report_table.sql`
- [ ] 确认表 `promotion_event_report` 创建成功
- [ ] 验证索引创建正确

```sql
-- 检查表结构
DESCRIBE promotion_event_report;

-- 检查索引
SHOW INDEX FROM promotion_event_report;
```

### 2. 配置文件检查
- [ ] 确认 `common_config.txt` 中推广相关配置完整
- [ ] 验证第三方接口 URL 和密钥配置正确
- [ ] 确认 failover 策略设置符合业务要求

```properties
# 必需配置项
promotion.agent.verify.enable=true
promotion.agent.verify.url=https://support.xpyun.net/api/admin/member/verifyPromotion
promotion.agent.verify.timeout=5000
promotion.agent.verify.failover=false
promotion.agent.verify.secret=<生产环境密钥>
promotion.agent.verify.project=xprinter
```

### 3. 代码部署
- [ ] 确认所有新增文件已包含在部署包中
- [ ] 验证路由配置正确加载
- [ ] 检查依赖库版本兼容性

## 部署步骤

### 1. 灰度部署建议
1. **配置验证阶段**
   - 先部署代码但设置 `promotion.agent.verify.enable=false`
   - 验证服务启动正常，接口可访问
   - 检查日志无异常

2. **小流量测试**
   - 设置 `promotion.agent.verify.enable=true`
   - 使用测试账号验证三种 actionType 流程
   - 监控第三方调用成功率和响应时间

3. **全量开放**
   - 确认测试无问题后，通知客户端开始调用
   - 持续监控关键指标

### 2. 回滚方案
- **紧急回滚**：设置 `promotion.agent.verify.enable=false`
- **完全回滚**：回滚到上一版本，数据表保留
- **数据恢复**：promotion_event_report 表数据可保留作为历史记录

## 监控与告警

### 1. 关键指标监控
- **成功率**：推广事件上报成功率应 > 95%
- **响应时间**：第三方接口调用平均响应时间应 < 3秒
- **错误率**：系统异常率应 < 1%
- **调用量**：监控每日调用量趋势

### 2. 日志监控
关键日志关键词：
- `推广事件上报开始` - 接口调用开始
- `推广事件上报完成` - 第三方调用完成
- `推广事件上报异常` - 系统异常
- `推广事件上报统计` - 定期统计输出

### 3. 告警规则建议
```
# 成功率告警
推广事件上报成功率 < 90% (5分钟内)

# 响应时间告警  
推广事件上报平均响应时间 > 5秒 (5分钟内)

# 异常告警
推广事件上报异常次数 > 10次 (1分钟内)

# 第三方服务异常
连续5次第三方调用失败
```

## 运维操作

### 1. 查看实时统计
```bash
# 获取统计信息
curl -H "Authorization: Bearer $TOKEN" \
  "$BASE_URL/api/v2/promotion-stats/stats"

# 输出详细统计到日志
curl -X POST -H "Authorization: Bearer $TOKEN" \
  "$BASE_URL/api/v2/promotion-stats/log-stats"
```

### 2. 数据库运维查询
```sql
-- 查看最近1小时上报情况
SELECT 
  action_type,
  COUNT(*) as total,
  SUM(success) as success_count,
  AVG(duration_ms) as avg_duration
FROM promotion_event_report 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY action_type;

-- 查看失败记录详情
SELECT 
  user_id, action_type, http_status, 
  third_party_raw_resp, created_at
FROM promotion_event_report 
WHERE success = 0 
ORDER BY created_at DESC 
LIMIT 20;

-- 查看响应时间分布
SELECT 
  CASE 
    WHEN duration_ms < 1000 THEN '<1s'
    WHEN duration_ms < 3000 THEN '1-3s'
    WHEN duration_ms < 5000 THEN '3-5s'
    ELSE '>5s'
  END as duration_range,
  COUNT(*) as count
FROM promotion_event_report 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY duration_range;
```

### 3. 故障排查

#### 第三方接口调用失败
1. 检查网络连通性
2. 验证 URL 和密钥配置
3. 查看第三方服务状态
4. 检查签名计算是否正确

#### 数据库写入失败
1. 检查数据库连接
2. 验证表结构和权限
3. 查看磁盘空间

#### 接口响应慢
1. 检查第三方接口响应时间
2. 查看数据库查询性能
3. 监控服务器资源使用

### 4. 性能优化建议
- 定期清理历史数据（建议保留3-6个月）
- 监控数据库表大小，必要时分表
- 考虑增加缓存减少重复查询
- 第三方调用超时时间根据实际情况调整

## 数据备份与恢复

### 1. 数据备份
```sql
-- 备份推广事件记录（按月）
CREATE TABLE promotion_event_report_backup_202412 AS 
SELECT * FROM promotion_event_report 
WHERE created_at >= '2024-12-01' AND created_at < '2025-01-01';
```

### 2. 数据清理
```sql
-- 清理3个月前的记录
DELETE FROM promotion_event_report 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 MONTH);
```

## 安全注意事项

1. **配置安全**
   - 生产环境密钥不得出现在日志中
   - 定期轮换第三方接口密钥
   - 限制统计接口访问权限

2. **数据安全**
   - 推广码在日志中脱敏显示
   - 第三方响应数据不包含敏感信息
   - 定期审计数据访问日志

3. **接口安全**
   - 上报接口已集成限流机制
   - 统计接口仅供内部管理使用
   - 建议增加 IP 白名单限制
