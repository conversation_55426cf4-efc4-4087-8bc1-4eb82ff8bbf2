package com.sandu.xinye.api.v2.promotion;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.PromotionEventReport;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.service.PromotionAgentClient;

import java.util.Date;

/**
 * 推广事件上报服务
 */
public class PromotionService {

    public static final PromotionService me = new PromotionService();

    /**
     * 上报推广事件（供旧接口/调试使用）。业务上已改为在对应业务流程中自动触发。
     *
     * @param user 当前用户
     * @param actionType 事件类型：1-注册绑定，2-首次有效打印，3-VIP订阅
     * @param promotionCode 推广码（actionType=1且未绑定时必填）
     * @param ipAddress 客户端IP
     * @return 处理结果
     */
    public RetKit reportEvent(User user, Integer actionType, String promotionCode, String ipAddress) {
        try {
            LogKit.info("处理推广事件上报 - userId:" + user.getUserId() + ", actionType:" + actionType);
            
            switch (actionType) {
                case PromotionEventReport.ACTION_TYPE_REGISTER_BIND:
                    return handleRegisterBind(user, promotionCode);
                    
                case PromotionEventReport.ACTION_TYPE_FIRST_PRINT:
                    return handleFirstPrint(user);
                    
                case PromotionEventReport.ACTION_TYPE_VIP_SUBSCRIBE:
                    return handleVipSubscribe(user);
                    
                default:
                    return RetKit.fail("不支持的事件类型：" + actionType);
            }
            
        } catch (Exception e) {
            LogKit.error("推广事件上报处理异常 - userId:" + user.getUserId() + 
                        ", actionType:" + actionType + ", error:" + e.getMessage(), e);
            return RetKit.fail("系统异常，请稍后重试");
        }
    }
    
    /**
     * 处理注册绑定事件（actionType=1）
     */
    private RetKit handleRegisterBind(User user, String promotionCode) {
        // 检查用户是否已绑定
        String existingCode = user.getPromotionCode();
        if (StrKit.notBlank(existingCode)) {
            LogKit.info("用户已绑定推广码 - userId:" + user.getUserId() + ", existingCode:" + existingCode);
            
            // 检查是否已成功上报过
            boolean hasReported = hasSuccessfulReport(user.getUserId(), PromotionEventReport.ACTION_TYPE_REGISTER_BIND);
            if (hasReported) {
                return RetKit.ok("用户已绑定且已上报");
            } else {
                // 已绑定但未成功上报过，可以补上报（但不改绑定状态）
                return doReport(user.getUserId(), existingCode, PromotionEventReport.ACTION_TYPE_REGISTER_BIND, 
                              "bind:" + existingCode, user.getPromotionBindTime());
            }
        }
        
        // 用户未绑定，要求提供推广码
        if (StrKit.isBlank(promotionCode)) {
            return RetKit.fail("用户未绑定推广码，请提供promotionCode参数");
        }
        
        // 调用第三方上报
        PromotionAgentClient.ReportResult result = PromotionAgentClient.me.reportEvent(
            user.getUserId(), promotionCode, PromotionEventReport.ACTION_TYPE_REGISTER_BIND);
        
        // 记录上报结果
        Date now = new Date();
        saveReportRecord(user.getUserId(), PromotionEventReport.ACTION_TYPE_REGISTER_BIND, 
                        result.isSuccess(), promotionCode, "bind:" + promotionCode, now, now, result);
        
        if (result.isSuccess()) {
            // 第三方返回成功，更新用户绑定状态
            boolean updated = user.setPromotionCode(promotionCode)
                                 .setPromotionBindTime(now)
                                 .update();
            if (updated) {
                LogKit.info("用户绑定推广码成功 - userId:" + user.getUserId() + ", promotionCode:" + promotionCode);
                return RetKit.ok("绑定成功");
            } else {
                LogKit.error("更新用户绑定状态失败 - userId:" + user.getUserId());
                return RetKit.fail("绑定状态更新失败");
            }
        } else {
            // 第三方返回失败或网络异常
            boolean failover = PropKit.use("common_config.txt").getBoolean("promotion.agent.verify.failover", false);
            if (failover) {
                // 允许降级绑定
                LogKit.info("第三方验证失败但允许降级绑定 - userId:" + user.getUserId());
                user.setPromotionCode(promotionCode).setPromotionBindTime(now).update();
                return RetKit.ok("绑定成功（降级）");
            } else {
                // 不允许绑定
                LogKit.info("第三方验证失败且不允许降级绑定 - userId:" + user.getUserId() + 
                           ", reason:" + result.getMessage());
                return RetKit.fail("绑定失败：" + result.getMessage());
            }
        }
    }
    
    /**
     * 处理首次有效打印事件（actionType=2）
     * 旧逻辑：保留用于兼容 /api/v2/promotion/report
     */
    private RetKit handleFirstPrint(User user) {
        // 检查用户是否已绑定推广码
        String promotionCode = user.getPromotionCode();
        if (StrKit.isBlank(promotionCode)) {
            return RetKit.fail("用户未绑定推广码，无法上报激活事件");
        }

        // 检查是否已成功上报过（激活事件仅上报一次）
        boolean hasReported = hasSuccessfulReport(user.getUserId(), PromotionEventReport.ACTION_TYPE_FIRST_PRINT);
        if (hasReported) {
            return RetKit.ok("激活事件已上报过");
        }

        // 兜底：若外部仍调用老接口，此处无法获知记录ID，仅以当前时间作为事件时间
        Date printTime = new Date();
        String bizRef = "print_record_id_placeholder";

        return doReport(user.getUserId(), promotionCode, PromotionEventReport.ACTION_TYPE_FIRST_PRINT,
                       bizRef, printTime);
    }

    /**
     * 处理VIP订阅事件（actionType=3）
     */
    private RetKit handleVipSubscribe(User user) {
        // 检查用户是否已绑定推广码
        String promotionCode = user.getPromotionCode();
        if (StrKit.isBlank(promotionCode)) {
            return RetKit.fail("用户未绑定推广码，无法上报VIP订阅事件");
        }

        // VIP订阅可以多次上报，不检查历史记录

        // 兜底：此处无法获知订单号，仅以当前时间作为事件时间
        Date subscribeTime = new Date();
        String bizRef = "order_id_placeholder";

        return doReport(user.getUserId(), promotionCode, PromotionEventReport.ACTION_TYPE_VIP_SUBSCRIBE,
                       bizRef, subscribeTime);
    }

    /**
     * 在“绑定推广码成功”时尝试上报 actionType=1（仅一次）
     */
    public void tryReportOnBindSuccess(User user) {
        try {
            if (user == null) {
                LogKit.warn("绑定上报用户为空");
                return;
            }
            if (StrKit.isBlank(user.getPromotionCode())) {
                LogKit.debug("用户推广码为空，跳过绑定上报 - userId:" + user.getUserId());
                return;
            }
            boolean hasReported = hasSuccessfulReport(user.getUserId(), PromotionEventReport.ACTION_TYPE_REGISTER_BIND);
            if (hasReported) {
                LogKit.debug("用户已上报过绑定事件，跳过 - userId:" + user.getUserId());
                return;
            }

            LogKit.info("触发绑定上报 - userId:" + user.getUserId() + ", promotionCode:" +
                       maskPromotionCode(user.getPromotionCode()) + ", bindTime:" + user.getPromotionBindTime());
            doReport(user.getUserId(), user.getPromotionCode(), PromotionEventReport.ACTION_TYPE_REGISTER_BIND,
                    "bind:" + user.getPromotionCode(), user.getPromotionBindTime());
        } catch (Exception e) {
            LogKit.error("绑定成功后上报异常 - userId:" + (user != null ? user.getUserId() : null) + ", err:" + e.getMessage(), e);
            // 记录异常监控
            try {
                com.sandu.xinye.common.monitor.PromotionEventMonitor.me.recordException(
                    "BindTriggerError", e.getMessage());
            } catch (Exception ignored) {}
        }
    }

    /**
     * 在“保存打印记录成功”时尝试上报 actionType=2（激活：近30天内有≥5个不同打印日期）
     * 仅上报一次；达标的这一条打印的时间作为 event_time；biz_ref=print_record.id
     */
    public void tryReportActivationOnPrint(Integer userId, Date printTime, Long printRecordId) {
        try {
            if (userId == null || printTime == null || printRecordId == null) {
                LogKit.warn("激活上报参数不完整 - userId:" + userId + ", printTime:" + printTime + ", recordId:" + printRecordId);
                return;
            }

            // 已上报过则跳过
            if (hasSuccessfulReport(userId, PromotionEventReport.ACTION_TYPE_FIRST_PRINT)) {
                LogKit.debug("用户已上报过激活事件，跳过 - userId:" + userId);
                return;
            }

            // 用户是否已绑定推广码
            User user = User.dao.findById(userId);
            if (user == null || StrKit.isBlank(user.getPromotionCode())) {
                LogKit.debug("用户未绑定推广码，跳过激活上报 - userId:" + userId);
                return;
            }


            // 基于 user_print_day 的近30天不同打印日数统计
            Date windowStart = new Date(printTime.getTime() - 30L * 24 * 60 * 60 * 1000); // 30天前
            Integer days = Db.queryInt(
                "SELECT COUNT(*) FROM user_print_day WHERE user_id = ? AND print_date BETWEEN DATE(?) AND DATE(?)",
                userId, windowStart, printTime);

            LogKit.info("激活条件检查 - userId:" + userId + ", 近30天打印日数:" + days + ", 需要:5");

            if (days != null && days.intValue() >= 5) {
                LogKit.info("用户达到激活条件，触发上报 - userId:" + userId + ", promotionCode:" +
                           maskPromotionCode(user.getPromotionCode()) + ", recordId:" + printRecordId);
                doReport(userId, user.getPromotionCode(), PromotionEventReport.ACTION_TYPE_FIRST_PRINT,
                        String.valueOf(printRecordId), printTime);
            } else {
                LogKit.debug("用户未达到激活条件 - userId:" + userId + ", 当前天数:" + days);
            }
        } catch (Exception e) {
            LogKit.error("打印记录后触发激活上报异常 - userId:" + userId + ", recordId:" + printRecordId +
                        ", err:" + e.getMessage(), e);
            // 记录异常监控
            try {
                com.sandu.xinye.common.monitor.PromotionEventMonitor.me.recordException(
                    "ActivationTriggerError", e.getMessage());
            } catch (Exception ignored) {}
        }
    }

    /**
     * 执行上报并记录结果
     */
    private RetKit doReport(Integer userId, String promotionCode, Integer actionType,
                           String bizRef, Date eventTime) {
        // 调用第三方上报
        PromotionAgentClient.ReportResult result = PromotionAgentClient.me.reportEvent(
            userId, promotionCode, actionType);

        // 记录监控（补充正常路径监控）
        try {
            com.sandu.xinye.common.monitor.PromotionEventMonitor.me.recordReport(
                result.isSuccess(), result.getDurationMs());
        } catch (Exception ignored) {}

        // 记录上报结果
        Date now = new Date();
        saveReportRecord(userId, actionType, result.isSuccess(), promotionCode, bizRef,
                        eventTime, now, result);

        if (result.isSuccess()) {
            return RetKit.ok("上报成功");
        } else {
            return RetKit.fail("上报失败：" + result.getMessage());
        }
    }

    /**
     * 检查用户是否已成功上报过指定类型的事件
     */
    private boolean hasSuccessfulReport(Integer userId, Integer actionType) {
        Record record = Db.findFirst(
            "SELECT id FROM promotion_event_report WHERE user_id = ? AND action_type = ? AND success = 1 LIMIT 1",
            userId, actionType);
        return record != null;
    }

    /**
     * 保存上报记录
     */
    private void saveReportRecord(Integer userId, Integer actionType, boolean success,
                                 String promotionCode, String bizRef, Date eventTime,
                                 Date reportedAt, PromotionAgentClient.ReportResult result) {
        try {
            PromotionEventReport report = new PromotionEventReport();
            report.setUserId(userId)
                  .setActionType(actionType)
                  .setSuccess(success ? PromotionEventReport.SUCCESS_TRUE : PromotionEventReport.SUCCESS_FALSE)
                  .setPromotionCode(promotionCode)
                  .setBizRef(bizRef)
                  .setEventTime(eventTime)
                  .setReportedAt(reportedAt)
                  .setHttpStatus(result.getHttpStatus())
                  .setDurationMs(result.getDurationMs())
                  .setThirdPartyRawResp(result.getRawResp())
                  .setCreatedAt(reportedAt)
                  .setUpdatedAt(reportedAt);

            boolean saved = report.save();
            if (saved) {
                LogKit.info("保存上报记录成功 - userId:" + userId + ", actionType:" + actionType +
                           ", success:" + success);
            } else {
                LogKit.error("保存上报记录失败 - userId:" + userId + ", actionType:" + actionType);
            }
        } catch (Exception e) {
            LogKit.error("保存上报记录异常 - userId:" + userId + ", actionType:" + actionType +
                        ", error:" + e.getMessage(), e);
        }
    }


    /**
     * 脱敏推广码（只显示前后各2位）
     */
    private String maskPromotionCode(String promotionCode) {
        if (StrKit.isBlank(promotionCode) || promotionCode.length() <= 4) {
            return promotionCode;
        }
        return promotionCode.substring(0, 2) + "****" +
               promotionCode.substring(promotionCode.length() - 2);
    }
}
