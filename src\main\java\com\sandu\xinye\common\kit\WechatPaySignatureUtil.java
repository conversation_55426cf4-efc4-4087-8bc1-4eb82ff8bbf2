package com.sandu.xinye.common.kit;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.redis.Cache;
import com.jfinal.plugin.redis.Redis;
import com.sandu.xinye.common.kit.WechatPayCertificateManager;

import java.nio.charset.StandardCharsets;
import java.security.Signature;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * 微信支付签名验证工具类
 * 实现签名验证、时间戳验证、防重放攻击等安全机制
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class WechatPaySignatureUtil {
    
    // Redis key前缀
    private static final String NONCE_PREFIX = "wechat:pay:nonce:";
    
    // 时间戳有效期（5分钟，单位：秒）
    private static final long TIMESTAMP_VALIDITY = 300;
    
    // nonce过期时间（5分钟+10秒缓冲，单位：秒）
    private static final int NONCE_EXPIRE = 310;
    
    /**
     * 验证微信支付回调签名
     * 
     * @param signature 签名字符串
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param body 请求体
     * @param serial 证书序列号
     * @return 验证是否通过
     */
    public static boolean verifySignature(String signature, String timestamp, 
                                         String nonce, String body, String serial) {
        try {
            // 1. 构造验签字符串
            String message = buildSignatureMessage(timestamp, nonce, body);
            
            // 2. 获取平台证书
            X509Certificate certificate = WechatPayCertificateManager.me.getCertificate(serial);
            if (certificate == null) {
                LogKit.error("未找到对应的微信支付平台证书，序列号: " + serial);
                // 如果证书管理器未实现，暂时返回true（仅用于开发测试）
                if (isDevelopmentMode()) {
                    LogKit.warn("开发模式下跳过证书验证");
                    return true;
                }
                return false;
            }
            
            // 3. 使用公钥验证签名
            Signature verifier = Signature.getInstance("SHA256withRSA");
            verifier.initVerify(certificate.getPublicKey());
            verifier.update(message.getBytes(StandardCharsets.UTF_8));
            
            // 4. Base64解码签名并验证
            byte[] signatureBytes = Base64.getDecoder().decode(signature);
            boolean verified = verifier.verify(signatureBytes);
            
            if (verified) {
                LogKit.info("微信支付签名验证成功");
            } else {
                LogKit.error("微信支付签名验证失败");
            }
            
            return verified;
            
        } catch (Exception e) {
            LogKit.error("验证微信支付签名异常", e);
            return false;
        }
    }
    
    /**
     * 构造验签字符串
     * 格式：timestamp\n + nonce\n + body\n
     */
    private static String buildSignatureMessage(String timestamp, String nonce, String body) {
        return timestamp + "\n" + nonce + "\n" + body + "\n";
    }
    
    /**
     * 验证时间戳是否有效（5分钟内）
     * 
     * @param timestamp 时间戳字符串（秒）
     * @return 是否有效
     */
    public static boolean isTimestampValid(String timestamp) {
        try {
            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis() / 1000;
            long timeDiff = Math.abs(currentTime - requestTime);
            
            boolean valid = timeDiff <= TIMESTAMP_VALIDITY;
            
            if (!valid) {
                LogKit.warn("时间戳验证失败，时间差: " + timeDiff + "秒");
            }
            
            return valid;
            
        } catch (NumberFormatException e) {
            LogKit.error("时间戳格式错误: " + timestamp);
            return false;
        }
    }
    
    /**
     * 检查并记录nonce，防止重放攻击
     * 
     * @param nonce 随机字符串
     * @param timestamp 时间戳
     * @return 是否为新的请求（非重放）
     */
    public static boolean checkAndRecordNonce(String nonce, String timestamp) {
        if (StrKit.isBlank(nonce) || StrKit.isBlank(timestamp)) {
            return false;
        }
        
        try {
            // 构造Redis key
            String key = NONCE_PREFIX + timestamp + ":" + nonce;
            
            // 使用Redis的原子操作检查并设置
            Cache cache = Redis.use();
            
            // 使用setEX方法原子性设置（如果不存在）
            // 先尝试获取
            String existingValue = cache.get(key);
            
            if (existingValue == null) {
                // 新的nonce，设置值和过期时间
                cache.setex(key, NONCE_EXPIRE, "1");
                LogKit.info("记录新的nonce: " + nonce);
                return true;
            } else {
                // nonce已存在，可能是重放攻击
                LogKit.warn("检测到重复的nonce，可能是重放攻击: " + nonce);
                return false;
            }
            
        } catch (Exception e) {
            LogKit.error("检查nonce时发生异常", e);
            // Redis异常时的降级处理
            if (isDevelopmentMode()) {
                LogKit.warn("开发模式下Redis异常，跳过nonce检查");
                return true;
            }
            return false;
        }
    }
    
    /**
     * 清理过期的nonce记录（可选，用于维护）
     * Redis会自动过期，这个方法用于手动清理
     */
    public static void cleanExpiredNonces() {
        try {
            // Redis的过期机制会自动处理，这里可以添加额外的清理逻辑
            LogKit.info("nonce记录将通过Redis过期机制自动清理");
        } catch (Exception e) {
            LogKit.error("清理过期nonce记录异常", e);
        }
    }
    
    /**
     * 判断是否为开发模式
     */
    private static boolean isDevelopmentMode() {
        // 从配置文件读取运行模式
        String mode = PropKit.get("app.mode", "production");
        return "development".equalsIgnoreCase(mode) || "dev".equalsIgnoreCase(mode);
    }
    
    /**
     * 生成签名（用于测试或主动调用微信API时）
     * 
     * @param method HTTP方法
     * @param url URL路径
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param body 请求体
     * @return 签名字符串
     */
    public static String generateSignature(String method, String url, String timestamp, 
                                          String nonce, String body) {
        try {
            // 构造签名串
            String message = method + "\n" + url + "\n" + timestamp + "\n" + nonce + "\n" + body + "\n";
            
            // TODO: 使用商户私钥签名
            // 这里需要加载商户私钥进行签名
            
            return "";
        } catch (Exception e) {
            LogKit.error("生成签名失败", e);
            return null;
        }
    }
}