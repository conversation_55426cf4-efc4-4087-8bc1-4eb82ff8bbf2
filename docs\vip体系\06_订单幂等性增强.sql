-- VIP订单幂等性增强SQL脚本
-- 目的：防止用户创建多个未支付订单，确保订单创建的幂等性

-- 1. 添加幂等键字段（可选方案1：基于业务幂等键）
-- ALTER TABLE `vip_order` 
-- ADD COLUMN `idempotent_key` VARCHAR(64) NULL COMMENT '幂等键，防重复创建' AFTER `order_no`;
-- CREATE UNIQUE INDEX `uk_idempotent_key` ON `vip_order` (`idempotent_key`);

-- 2. 添加用户+状态唯一约束（推荐方案2：业务约束）
-- 注意：这个约束只允许每个用户同时有一个未支付订单
-- 如果需要支持多个未支付订单，请使用方案1的幂等键方案

-- 首先检查是否有违反约束的数据
SELECT 
    user_id,
    COUNT(*) as unpaid_count,
    GROUP_CONCAT(order_no) as order_nos
FROM vip_order 
WHERE status = 'created' 
GROUP BY user_id 
HAVING COUNT(*) > 1;

-- 如果上述查询有结果，需要先清理重复数据
-- 保留最新的订单，关闭其他订单
UPDATE vip_order o1
SET status = 'closed', update_time = NOW()
WHERE status = 'created' 
AND EXISTS (
    SELECT 1 FROM (
        SELECT user_id, MAX(create_time) as latest_time
        FROM vip_order 
        WHERE status = 'created'
        GROUP BY user_id
    ) o2 
    WHERE o1.user_id = o2.user_id 
    AND o1.create_time < o2.latest_time
);

-- 添加唯一约束：每个用户只能有一个未支付订单
-- 使用函数索引，只对status='created'的记录建立唯一约束
CREATE UNIQUE INDEX `uk_user_unpaid_order` ON `vip_order` (`user_id`, `status`) 
WHERE `status` = 'created';

-- 如果MySQL版本不支持条件索引，使用以下替代方案：
-- 创建一个计算列来实现条件唯一约束
-- ALTER TABLE `vip_order` 
-- ADD COLUMN `user_status_key` VARCHAR(100) GENERATED ALWAYS AS (
--     CASE WHEN status = 'created' THEN CONCAT(user_id, '_', status) ELSE NULL END
-- ) STORED;
-- CREATE UNIQUE INDEX `uk_user_status_key` ON `vip_order` (`user_status_key`);

-- 3. 添加订单自动过期机制的辅助字段
ALTER TABLE `vip_order` 
ADD COLUMN `expire_time` DATETIME NULL COMMENT '订单过期时间' AFTER `create_time`;

-- 为未支付订单设置过期时间（30分钟后过期）
UPDATE vip_order 
SET expire_time = DATE_ADD(create_time, INTERVAL 30 MINUTE)
WHERE status = 'created' AND expire_time IS NULL;

-- 创建过期时间索引，便于定时任务查询
CREATE INDEX `idx_expire_time` ON `vip_order` (`expire_time`);

-- 4. 验证约束是否生效
-- 尝试为同一用户创建两个未支付订单，应该失败
-- INSERT INTO vip_order (order_no, user_id, plan, tier, amount, channel, status, create_time, update_time)
-- VALUES 
-- ('TEST001', 1, 'monthly', 'VIP_MONTHLY', 990, 'wechat', 'created', NOW(), NOW()),
-- ('TEST002', 1, 'monthly', 'VIP_MONTHLY', 990, 'wechat', 'created', NOW(), NOW());

-- 5. 创建订单清理存储过程（可选）
DELIMITER //
CREATE PROCEDURE CleanExpiredOrders()
BEGIN
    DECLARE affected_rows INT DEFAULT 0;
    
    -- 将过期的未支付订单标记为已关闭
    UPDATE vip_order 
    SET status = 'closed', update_time = NOW()
    WHERE status = 'created' 
    AND expire_time IS NOT NULL 
    AND expire_time < NOW();
    
    SET affected_rows = ROW_COUNT();
    
    -- 记录清理结果
    INSERT INTO vip_order_cleanup_log (cleanup_time, affected_orders, cleanup_type)
    VALUES (NOW(), affected_rows, 'auto_expire');
    
    SELECT CONCAT('已清理 ', affected_rows, ' 个过期订单') as result;
END //
DELIMITER ;

-- 6. 创建清理日志表（可选）
CREATE TABLE IF NOT EXISTS `vip_order_cleanup_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `cleanup_time` DATETIME NOT NULL,
  `affected_orders` INT NOT NULL DEFAULT 0,
  `cleanup_type` VARCHAR(20) NOT NULL COMMENT 'auto_expire/manual_clean',
  `notes` VARCHAR(255) NULL,
  PRIMARY KEY (`id`),
  KEY `idx_cleanup_time` (`cleanup_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单清理日志';

-- 使用说明：
-- 1. 执行此脚本前，请先备份数据库
-- 2. 如果MySQL版本不支持条件索引，请使用注释中的替代方案
-- 3. 建议在定时任务中定期调用 CleanExpiredOrders() 存储过程
-- 4. 可以根据业务需求调整订单过期时间（当前设置为30分钟）
