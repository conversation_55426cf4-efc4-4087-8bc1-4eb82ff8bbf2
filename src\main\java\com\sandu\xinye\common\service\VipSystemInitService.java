package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.sandu.xinye.common.kit.WechatPayCertificateManager;

/**
 * VIP系统初始化服务
 * 负责系统启动时的VIP相关初始化工作
 */
public class VipSystemInitService {
    
    public static final VipSystemInitService me = new VipSystemInitService();
    
    /**
     * 初始化VIP系统
     * 建议在应用启动时调用
     */
    public void init() {
        try {
            LogKit.info("开始初始化VIP系统...");

            // 1. 检查微信支付配置
            checkWechatPayConfig();

            // 2. 初始化微信支付服务
            initWechatPayService();

            // 3. 初始化微信支付平台证书管理器
            initWechatPayCertificateManager();

            // 4. 初始化默认的VIP功能规则
            VipFeatureRuleService.me.initDefaultRules();

            // 5. 处理一次过期VIP用户（启动时清理）
            VipDeliveryService.me.processExpiredVipUsers();

            LogKit.info("VIP系统初始化完成");

        } catch (Exception e) {
            LogKit.error("VIP系统初始化失败", e);
        }
    }

    /**
     * 检查微信支付配置
     */
    private void checkWechatPayConfig() {
        try {
            WechatPayConfig config = WechatPayConfig.me;
            config.printConfigStatus();

            if (!config.isConfigValid()) {
                LogKit.warn("微信支付配置不完整，VIP购买功能可能无法正常使用");
                LogKit.warn("请参考 docs/vip体系/wechat_pay_config_template.txt 完善配置");
            }

        } catch (Exception e) {
            LogKit.error("检查微信支付配置失败", e);
        }
    }

    /**
     * 初始化微信支付服务
     */
    private void initWechatPayService() {
        try {
            WechatPayService.me.init();

            if (WechatPayService.me.isAvailable()) {
                LogKit.info("微信支付服务初始化成功");
            } else {
                LogKit.warn("微信支付服务不可用，将使用模拟支付参数");
            }

        } catch (Exception e) {
            LogKit.error("初始化微信支付服务失败", e);
        }
    }

    /**
     * 初始化微信支付平台证书管理器
     */
    private void initWechatPayCertificateManager() {
        try {
            WechatPayCertificateManager.me.init();

            int certCount = WechatPayCertificateManager.me.getCertificateCount();
            if (certCount > 0) {
                LogKit.info("微信支付平台证书管理器初始化成功，已缓存 " + certCount + " 个证书");
            } else {
                LogKit.warn("微信支付平台证书管理器初始化完成，但未获取到证书");
            }

        } catch (Exception e) {
            LogKit.error("初始化微信支付平台证书管理器失败", e);
        }
    }
}
