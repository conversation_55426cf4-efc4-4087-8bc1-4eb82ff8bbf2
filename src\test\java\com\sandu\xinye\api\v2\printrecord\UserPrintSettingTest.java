package com.sandu.xinye.api.v2.printrecord;

import org.junit.Test;
import static org.junit.Assert.*;

import com.sandu.xinye.common.model.UserPrintSetting;

/**
 * 用户打印设置测试类
 * 用于验证 UserPrintSetting 的基本功能和 NPE 修复
 * 
 * <AUTHOR> Team
 */
public class UserPrintSettingTest {

    @Test
    public void testFindByUserIdWithNullUserId() {
        // 测试传入 null userId 的情况
        UserPrintSetting result = UserPrintSetting.findByUserId(null);
        assertNull("传入 null userId 应该返回 null", result);
    }

    @Test
    public void testIsUserRecordEnabledWithNullUserId() {
        // 测试传入 null userId 的情况
        boolean result = UserPrintSetting.isUserRecordEnabled(null);
        assertTrue("传入 null userId 应该返回默认值 true", result);
    }

    @Test
    public void testIsUserRecordEnabledWithNonExistentUser() {
        // 测试不存在的用户ID（使用一个很大的数字，假设不存在）
        Integer nonExistentUserId = 999999999;
        boolean result = UserPrintSetting.isUserRecordEnabled(nonExistentUserId);
        assertTrue("不存在的用户应该返回默认值 true", result);
    }

    @Test
    public void testDefaultRecordEnabledConstant() {
        // 测试常量值
        assertTrue("RECORD_ENABLED 常量应该为 true", UserPrintSetting.RECORD_ENABLED);
        assertFalse("RECORD_DISABLED 常量应该为 false", UserPrintSetting.RECORD_DISABLED);
    }

    @Test
    public void testInstanceIsRecordEnabledWithNullValue() {
        // 测试实例方法处理 null 值的情况
        UserPrintSetting setting = new UserPrintSetting();
        // 不设置 recordEnabled，应该为 null
        boolean result = setting.isRecordEnabled();
        assertTrue("当 recordEnabled 为 null 时，应该返回默认值 true", result);
    }

    @Test
    public void testInstanceIsRecordEnabledWithTrueValue() {
        // 测试实例方法处理 true 值的情况
        UserPrintSetting setting = new UserPrintSetting();
        setting.setRecordEnabled(true);
        boolean result = setting.isRecordEnabled();
        assertTrue("当 recordEnabled 为 true 时，应该返回 true", result);
    }

    @Test
    public void testInstanceIsRecordEnabledWithFalseValue() {
        // 测试实例方法处理 false 值的情况
        UserPrintSetting setting = new UserPrintSetting();
        setting.setRecordEnabled(false);
        boolean result = setting.isRecordEnabled();
        assertFalse("当 recordEnabled 为 false 时，应该返回 false", result);
    }
}
