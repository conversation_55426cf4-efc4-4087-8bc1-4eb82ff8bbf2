<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.sandu</groupId>
	<artifactId>xinye</artifactId>
	<name>xinye</name>
	<packaging>war</packaging>
	<version>0.0.1-SNAPSHOT</version>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<failOnMissingWebXml>false</failOnMissingWebXml>
	</properties>

	<!-- 使用阿里 maven 库 -->
	<repositories>
		<repository>
			<id>ali-maven</id>
			<url>http://maven.aliyun.com/nexus/content/groups/public</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
				<checksumPolicy>fail</checksumPolicy>
			</snapshots>
		</repository>
	</repositories>

	<dependencies>
		<!-- jfinal3.5 -->
		<dependency>
			<groupId>com.jfinal</groupId>
			<artifactId>jfinal</artifactId>
			<version>3.5</version>
		</dependency>
		<dependency>
			<groupId>com.jfinal</groupId>
			<artifactId>jetty-server</artifactId>
			<version>2019.3</version>
			<!-- dev mode set compile -->
			<!-- deploy mode set provided -->
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2</version>
		</dependency>
		<!-- 避免控制台输出如下提示信息： SLF4J: Failed to load class "org.slf4j.impl.StaticLoggerBinder".
			项目中实际上用不到这个 jar 包，本 jfinal demo 用不上这个依赖，在此仅为大家 在未来基于 jfinal demo 为模板做开发时做准备工作
			注意：eclipse 下可以将 scope 设置为 provided -->
<!--		<dependency>-->
<!--			<groupId>org.slf4j</groupId>-->
<!--			<artifactId>slf4j-nop</artifactId>-->
<!--			<version>1.7.25</version>-->
<!--			&lt;!&ndash; 打包前改成 provided，此处使用 compile 仅为支持 IDEA &ndash;&gt;-->
<!--			<scope>compile</scope>-->
<!--		</dependency>-->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.30</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>1.7.30</version>
		</dependency>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.19</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.alibaba/druid -->
		<dependency>
		    <groupId>com.alibaba</groupId>
		    <artifactId>druid</artifactId>
		    <version>1.1.21</version>
		</dependency>
		<dependency>
			<groupId>com.jfinal</groupId>
			<artifactId>cos</artifactId>
			<version>2017.5</version>
		</dependency>
		<!-- ehcahce缓存 -->
		<dependency>
			<groupId>net.sf.ehcache</groupId>
			<artifactId>ehcache</artifactId>
			<version>2.10.2</version>
		</dependency>
		<!-- 定时调度 -->
		<dependency>
			<groupId>it.sauronsoftware.cron4j</groupId>
			<artifactId>cron4j</artifactId>
			<version>2.2.5</version>
		</dependency>
		<dependency>
			<groupId>cn.dreampie</groupId>
			<artifactId>jfinal-quartz</artifactId>
			<version>0.2</version>
			<exclusions>
				<exclusion>
					<groupId>com.google.collections</groupId>
					<artifactId>google-collections</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--Hutool -->
		<dependency>
			<groupId>com.xiaoleilu</groupId>
			<artifactId>hutool-all</artifactId>
			<version>3.0.5</version>
		</dependency>
		<!-- 支付宝支付 -->
		<dependency>
			<groupId>com.alipay.sdk</groupId>
			<artifactId>alipay-sdk-java</artifactId>
			<version>3.1.0</version>
		</dependency>
		<!-- fast json -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>
		<!-- dom4j -->
		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>1.6.1</version>
		</dependency>
		<!-- 飞鸽传书短信网 http://www.feige.ee/dev/dev1 -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpasyncclient</artifactId>
			<version>4.1.2</version>
		</dependency>

		<!-- google Gson -->
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.5</version>
		</dependency>

		<!-- 阿里云oss -->
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>3.10.2</version>
		</dependency>

		<!-- 阿里云openapi -->
		<!-- pns -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>dypnsapi20170525</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>tea-openapi</artifactId>
			<version>0.1.1</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>tea</artifactId>
			<version>[1.1.13, 2.0.0)</version>
		</dependency>
		<!-- sms -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>dysmsapi20170525</artifactId>
			<version>2.0.5</version>
		</dependency>
		<!-- -->
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>javax.mail-api</artifactId>
			<version>1.6.2</version>
		</dependency>
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.6.2</version>
		</dependency>

		<!-- 单元测试 -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.2</version>
			<scope>test</scope>
		</dependency>

		<!-- Mockito 测试框架 -->
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>3.12.4</version>
			<scope>test</scope>
		</dependency>
		
		<!-- Mockito inline for static mocking -->
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<version>3.12.4</version>
			<scope>test</scope>
		</dependency>
		
		<!-- Commons Pool2 for Redis connection pooling -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.11.1</version>
		</dependency>
		
		<!-- Jedis for Redis client (JFinal compatible version) -->
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.9.0</version>
		</dependency>
		
		<!-- FST serialization (required by JFinal Redis plugin) -->
		<dependency>
			<groupId>de.ruedigermoeller</groupId>
			<artifactId>fst</artifactId>
			<version>2.57</version>
		</dependency>

		<!-- guava辅助工具 -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>20.0</version>
		</dependency>

		<!-- 微信开放平台api -->
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-open</artifactId>
			<version>4.4.0</version>
		</dependency>

		<!-- HTTP客户端 for TextIn API -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.13</version>
		</dependency>

		<!-- 条码解析库 -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.5.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.5.1</version>
		</dependency>

		<!-- 图片处理增强 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-imaging</artifactId>
			<version>1.0.0-alpha5</version>
		</dependency>
	</dependencies>

	<build>
		<defaultGoal>compile</defaultGoal>
		<finalName>ROOT</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<compilerArgument>-parameters</compilerArgument>
					<encoding>UTF-8</encoding>
					<!--必须添加compilerArgument配置，才能使用JFinal的Controller方法带参数的功能 -->
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.0.0-M7</version>
				<configuration>
					<argLine>-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.math=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/java.util.concurrent=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED</argLine>
					<systemPropertyVariables>
						<file.encoding>UTF-8</file.encoding>
						<console.encoding>UTF-8</console.encoding>
					</systemPropertyVariables>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
