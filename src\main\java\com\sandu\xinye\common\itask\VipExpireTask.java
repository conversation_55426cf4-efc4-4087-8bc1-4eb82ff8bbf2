package com.sandu.xinye.common.itask;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.cron4j.ITask;
import com.sandu.xinye.common.service.VipDeliveryService;

/**
 * VIP过期处理定时任务
 * 定期检查并处理过期的VIP用户
 * 每天凌晨2点执行
 */
public class VipExpireTask implements ITask {

    @Override
    public void run() {
        LogKit.info("——————————————————VIP过期处理定时任务开始—————————————————————");

        try {
            LogKit.info("开始执行VIP过期处理任务");

            // 处理过期VIP用户
            VipDeliveryService.me.processExpiredVipUsers();

            LogKit.info("VIP过期处理任务执行成功");

        } catch (Exception e) {
            LogKit.error("VIP过期处理任务执行失败: " + e.getMessage(), e);
        }

        LogKit.info("——————————————————VIP过期处理定时任务结束—————————————————————");
    }

    @Override
    public void stop() {
        LogKit.info("VIP过期处理定时任务已停止");
    }

    /**
     * 处理过期VIP用户（静态方法，供手动调用）
     * 建议每天执行一次
     */
    public static void processExpiredVipUsers() {
        try {
            LogKit.info("手动执行VIP过期处理任务");

            VipDeliveryService.me.processExpiredVipUsers();

            LogKit.info("手动VIP过期处理任务执行完成");

        } catch (Exception e) {
            LogKit.error("手动VIP过期处理任务执行失败", e);
        }
    }
}
