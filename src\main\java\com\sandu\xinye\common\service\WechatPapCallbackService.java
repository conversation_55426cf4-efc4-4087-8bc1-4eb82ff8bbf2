package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.kit.JsonKit;
import com.sandu.xinye.common.model.VipOrder;
import com.sandu.xinye.common.model.VipPayNotifyLog;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信 PAP 回调处理（签约/解约/代扣）骨架
 * 注意：当前未接入真实验签与解密，仅做流程串联与幂等保护
 */
public class WechatPapCallbackService {

    public static final WechatPapCallbackService me = new WechatPapCallbackService();

    /** 处理签约回调：落库/更新订阅（占位） */
    public boolean handleSignCallback(String requestBody) {
        try {
            // TODO: 验签与解密（占位）
            Map<String, Object> payload = safeParse(requestBody);

            String contractId = getStr(payload, "contract_id", getStr(payload, "contractId", null));
            // 推荐从 attach 中反查 userId 与 subscriptionType
            Map<String, Object> attach = getMap(payload, "attach");
            if (attach == null) {
                String attachStr = getStr(payload, "attach", null);
                attach = safeParse(attachStr);
            }
            Integer userId = getInt(attach, "user_id");
            String subscriptionType = getStr(attach, "subscription_type", null);

            if (StrKit.notBlank(contractId) && userId != null && StrKit.notBlank(subscriptionType)) {
                VipSubscriptionService.me.upsertOnSign(userId, subscriptionType, contractId);
            } else {
                LogKit.warn("签约回调缺少关键字段（占位允许）：contractId=" + contractId + ", userId=" + userId + ", subType=" + subscriptionType);
            }

            logCallback(null, requestBody, VipPayNotifyLog.VERIFY_SUCCESS, VipPayNotifyLog.HANDLE_PROCESSED, null);
            return true;
        } catch (Exception e) {
            LogKit.error("处理PAP签约回调失败", e);
            return false;
        } finally {
            // 可加指标
        }
    }

    /** 处理解约回调（占位） */
    public boolean handleTerminateCallback(String requestBody) {
        try {
            Map<String, Object> payload = safeParse(requestBody);
            String contractId = getStr(payload, "contract_id", getStr(payload, "contractId", null));
            if (StrKit.notBlank(contractId)) {
                // 仅记录日志，实际应根据 contractId 定位用户并置 auto_renew=0 / status=cancelled 或 paused
                LogKit.info("PAP解约通知(占位)，contractId=" + contractId);
            }
            logCallback(null, requestBody, VipPayNotifyLog.VERIFY_SUCCESS, VipPayNotifyLog.HANDLE_PROCESSED, null);
            return true;
        } catch (Exception e) {
            LogKit.error("处理PAP解约回调失败", e);
            return false;
        }
    }

    /** 处理代扣回调：订单状态更新 + 发货（幂等） */
    public boolean handleDeductCallback(String requestBody) {
        try {
            Map<String, Object> payload = safeParse(requestBody);
            String tradeState = getStr(payload, "trade_state", getStr(payload, "tradeState", null));
            String outTradeNo = getStr(payload, "out_trade_no", getStr(payload, "outTradeNo", null));
            String transactionId = getStr(payload, "transaction_id", getStr(payload, "transactionId", null));
            String successTime = getStr(payload, "success_time", getStr(payload, "successTime", null));

            Map<String, Object> amountMap = getMap(payload, "amount");
            Integer totalAmount = amountMap != null ? (Integer) amountMap.get("total") : null;

            if (!"SUCCESS".equals(tradeState)) {
                LogKit.info("PAP代扣状态非SUCCESS，忽略: " + tradeState);
                logCallback(outTradeNo, requestBody, VipPayNotifyLog.VERIFY_SUCCESS, VipPayNotifyLog.HANDLE_IGNORED, null);
                return true;
            }

            boolean result = Db.tx(() -> {
                VipOrder order = VipOrder.dao.findFirst("SELECT * FROM vip_order WHERE order_no = ? FOR UPDATE", outTradeNo);
                if (order == null) {
                    LogKit.error("PAP回调未找到订单: " + outTradeNo);
                    return false;
                }

                if (VipOrder.STATUS_PAID.equals(order.getStatus())) {
                    LogKit.info("订单已处理过（PAP幂等），跳过: " + outTradeNo);
                    return true;
                }

                // 可选：校验金额与渠道号
                if (totalAmount != null && order.getAmount() != null && !order.getAmount().equals(totalAmount)) {
                    LogKit.warn("PAP回调金额不一致: outTradeNo=" + outTradeNo);
                }

                Date now = new Date();
                order.setStatus(VipOrder.STATUS_PAID)
                     .setChannelTradeNo(transactionId)
                     .setPaidTime(parseSuccessTime(successTime))
                     .setNotifyTime(now)
                     .setUpdateTime(now);
                if (!order.update()) {
                    LogKit.error("PAP更新订单状态失败: " + outTradeNo);
                    return false;
                }

                boolean delivered = VipDeliveryService.me.deliverVip(order);
                if (!delivered) {
                    LogKit.error("PAP发货失败: " + outTradeNo);
                    return false;
                }
                return true;
            });

            logCallback(outTradeNo, requestBody, VipPayNotifyLog.VERIFY_SUCCESS,
                    result ? VipPayNotifyLog.HANDLE_PROCESSED : VipPayNotifyLog.HANDLE_ERROR,
                    result ? null : "process failed");
            return result;
        } catch (Exception e) {
            LogKit.error("处理PAP代扣回调失败", e);
            return false;
        }
    }

    // ---------- 工具与日志 ----------

    private Date parseSuccessTime(String successTime) {
        try {
            if (StrKit.isBlank(successTime)) return new Date();
            // TODO: 按微信返回的格式解析，如 2023-05-20T12:12:12+08:00
            return new Date();
        } catch (Exception e) {
            return new Date();
        }
    }

    private Map<String, Object> safeParse(String body) {
        try {
            if (StrKit.isBlank(body)) return new HashMap<>();
            return JsonKit.parse(body, Map.class);
        } catch (Exception e) {
            LogKit.warn("PAP回调JSON解析失败", e);
            return new HashMap<>();
        }
    }

    private void logCallback(String outTradeNo, String requestBody, String verifyResult, String handleStatus, String errorMsg) {
        try {
            VipPayNotifyLog log = new VipPayNotifyLog();
            log.setChannel(VipPayNotifyLog.CHANNEL_WECHAT_PAP)
               .setOrderNo(outTradeNo)
               .setNotifyTime(new Date())
               .setRawHeaders(null)
               .setRawBody(requestBody)
               .setVerifyResult(verifyResult)
               .setHandleStatus(handleStatus)
               .setErrorMsg(errorMsg)
               .setCreateTime(new Date())
               .save();
        } catch (Exception e) {
            LogKit.error("记录PAP回调日志失败", e);
        }
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> getMap(Map<String, Object> m, String key) {
        Object v = m != null ? m.get(key) : null;
        return (v instanceof Map) ? (Map<String, Object>) v : null;
    }

    private String getStr(Map<String, Object> m, String key, String defVal) {
        Object v = m != null ? m.get(key) : null;
        return v != null ? String.valueOf(v) : defVal;
    }

    private Integer getInt(Map<String, Object> m, String key) {
        Object v = m != null ? m.get(key) : null;
        if (v instanceof Number) return ((Number) v).intValue();
        if (v != null) {
            try { return Integer.parseInt(String.valueOf(v)); } catch (Exception ignore) {}
        }
        return null;
    }
}

