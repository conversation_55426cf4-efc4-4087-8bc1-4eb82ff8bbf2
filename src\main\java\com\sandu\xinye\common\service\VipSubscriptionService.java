package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.kit.RetKit;

import java.util.HashMap;
import java.util.Map;

/**
 * 订阅服务（签约代扣骨架 + 状态管理）
 * 策略固化：
 * - 取消订阅：到期后停止（保留当前周期权益，不再续费）；尝试解约（占位）。
 * - 关闭自动续费：仅关闭 auto_renew，不解约，保留合同，当前周期权益不变。
 */
public class VipSubscriptionService {

    public static final VipSubscriptionService me = new VipSubscriptionService();

    /** 发起签约（占位，后续接入微信委托代扣） */
    public RetKit signContract(Integer userId, String subscriptionType) {
        if (userId == null || StrKit.isBlank(subscriptionType)) {
            return RetKit.fail("参数不能为空");
        }
        LogKit.info("准备签约: user=" + userId + ", type=" + subscriptionType);
        return RetKit.ok("签约请求已创建（占位）").set("_mock", true);
    }

    /** 取消订阅（到期后停止；尝试解约，占位） */
    public RetKit cancel(Integer userId, String subscriptionType) {
        if (userId == null || StrKit.isBlank(subscriptionType)) {
            return RetKit.fail("参数不能为空");
        }
        // 策略：到期后停止 -> 我们只需停止自动续费、标记取消，权益到期后自然失效
        int n = Db.update("UPDATE vip_subscription SET status='cancelled', auto_renew=0, cancel_time=NOW(), update_time=NOW() WHERE user_id=? AND subscription_type=?", userId, subscriptionType);
        // TODO：若存在 contract_id，可调用 WechatPapService.terminate(contractId) 进行解约（占位）
        return n > 0 ? RetKit.ok("已取消订阅").set("affected", n) : RetKit.fail("未找到订阅或已取消");
    }

    /** 停止自动续费（不解约） */
    public RetKit disableAuto(Integer userId, String subscriptionType) {
        if (userId == null || StrKit.isBlank(subscriptionType)) return RetKit.fail("参数不能为空");
        int n = Db.update("UPDATE vip_subscription SET auto_renew=0, update_time=NOW() WHERE user_id=? AND subscription_type=? AND status='active'", userId, subscriptionType);
        return n > 0 ? RetKit.ok("已关闭自动续费").set("affected", n) : RetKit.fail("未找到有效订阅");
    }

    /** 开启自动续费（需已签约） */
    public RetKit enableAuto(Integer userId, String subscriptionType) {
        if (userId == null || StrKit.isBlank(subscriptionType)) return RetKit.fail("参数不能为空");
        int n = Db.update("UPDATE vip_subscription SET auto_renew=1, update_time=NOW() WHERE user_id=? AND subscription_type=? AND status='active' AND contract_id IS NOT NULL", userId, subscriptionType);
        return n > 0 ? RetKit.ok("已开启自动续费").set("affected", n) : RetKit.fail("未找到有效订阅或未签约");
    }

    /** 签约回调后：落库/更新订阅 */
    public RetKit upsertOnSign(Integer userId, String subscriptionType, String contractId) {
        if (userId == null || StrKit.isBlank(subscriptionType) || StrKit.isBlank(contractId)) return RetKit.fail("参数不能为空");
        Record exist = Db.findFirst("SELECT * FROM vip_subscription WHERE user_id=? AND subscription_type=?", userId, subscriptionType);
        int months = "yearly".equals(subscriptionType) ? 12 : 1;
        if (exist == null) {
            String sql = "INSERT INTO vip_subscription (user_id, subscription_type, contract_id, status, auto_renew, start_time, next_renew_time, fail_count, create_time, update_time) " +
                         "VALUES (?, ?, ?, 'active', 1, NOW(), DATE_ADD(NOW(), INTERVAL ? MONTH), 0, NOW(), NOW())";
            int n = Db.update(sql, userId, subscriptionType, contractId, months);
            return n > 0 ? RetKit.ok("订阅创建成功").set("created", true) : RetKit.fail("创建失败");
        } else {
            String sql = "UPDATE vip_subscription SET contract_id=?, status='active', auto_renew=1, update_time=NOW() WHERE id=?";
            int n = Db.update(sql, contractId, exist.getLong("id"));
            return n > 0 ? RetKit.ok("订阅更新成功").set("created", false) : RetKit.fail("更新失败");
        }
    }

    /** 查询订阅状态（单一类型） */
    public RetKit getStatus(Integer userId, String subscriptionType) {
        if (userId == null) return RetKit.fail("用户ID不能为空");
        if (StrKit.isBlank(subscriptionType)) return RetKit.fail("订阅类型不能为空");
        Record sub = Db.findFirst("SELECT * FROM vip_subscription WHERE user_id=? AND subscription_type=?", userId, subscriptionType);
        Map<String, Object> data = new HashMap<>();
        if (sub != null) {
            data.put("status", sub.getStr("status"));
            data.put("auto_renew", sub.getInt("auto_renew"));
            data.put("contract_id", sub.getStr("contract_id"));
            data.put("next_renew_time", sub.get("next_renew_time"));
            data.put("fail_count", sub.getInt("fail_count"));
        }
        return RetKit.ok("查询成功").set("subscriptionType", subscriptionType).set("data", data);
    }

    /** 查询订阅状态（返回月/年两类） */
    public RetKit getStatusAll(Integer userId) {
        if (userId == null) return RetKit.fail("用户ID不能为空");
        RetKit m = getStatus(userId, "monthly");
        RetKit y = getStatus(userId, "yearly");
        return RetKit.ok("查询成功").set("monthly", m.get("data")).set("yearly", y.get("data"));
    }
}

