-- VIP系统数据库索引优化脚本
-- 执行时间：2024-01-15
-- 目标：优化查询性能，减少全表扫描

-- ========================================
-- 1. vip_order 表索引优化
-- ========================================

-- 用户订单查询索引（高频查询）
CREATE INDEX idx_vip_order_user_status ON vip_order(user_id, status, create_time DESC);
COMMENT ON INDEX idx_vip_order_user_status IS '用户订单状态查询索引';

-- 订单号查询索引（唯一索引）
CREATE UNIQUE INDEX uk_vip_order_no ON vip_order(order_no);
COMMENT ON INDEX uk_vip_order_no IS '订单号唯一索引';

-- 支付回调查询索引
CREATE INDEX idx_vip_order_channel_trade ON vip_order(channel_trade_no);
COMMENT ON INDEX idx_vip_order_channel_trade IS '渠道交易号索引';

-- 订单过期清理索引
CREATE INDEX idx_vip_order_expire ON vip_order(status, create_time) 
WHERE status = 'created';
COMMENT ON INDEX idx_vip_order_expire IS '过期订单清理索引';

-- 幂等键索引（如果添加了该字段）
-- CREATE UNIQUE INDEX uk_vip_order_idempotent ON vip_order(idempotent_key) 
-- WHERE idempotent_key IS NOT NULL;

-- 分析表统计信息
ANALYZE vip_order;

-- ========================================
-- 2. vip_subscription 表索引优化
-- ========================================

-- 用户订阅查询索引
CREATE UNIQUE INDEX uk_vip_subscription_user ON vip_subscription(user_id, status)
WHERE status = 'active';
COMMENT ON INDEX uk_vip_subscription_user IS '活跃订阅唯一索引';

-- 自动续费扫描索引
CREATE INDEX idx_vip_subscription_renew ON vip_subscription(status, auto_renew, next_renew_time)
WHERE status = 'active' AND auto_renew = 1;
COMMENT ON INDEX idx_vip_subscription_renew IS '自动续费扫描索引';

-- 合约ID索引
CREATE INDEX idx_vip_subscription_contract ON vip_subscription(contract_id);
COMMENT ON INDEX idx_vip_subscription_contract IS '合约ID索引';

-- 分析表统计信息
ANALYZE vip_subscription;

-- ========================================
-- 3. vip_refund 表索引优化
-- ========================================

-- 退款单号索引
CREATE UNIQUE INDEX uk_vip_refund_no ON vip_refund(refund_no);
COMMENT ON INDEX uk_vip_refund_no IS '退款单号唯一索引';

-- 订单退款查询索引
CREATE INDEX idx_vip_refund_order ON vip_refund(order_no, status);
COMMENT ON INDEX idx_vip_refund_order IS '订单退款查询索引';

-- 用户退款列表索引
CREATE INDEX idx_vip_refund_user ON vip_refund(user_id, create_time DESC);
COMMENT ON INDEX idx_vip_refund_user IS '用户退款列表索引';

-- 待处理退款索引
CREATE INDEX idx_vip_refund_pending ON vip_refund(status, create_time)
WHERE status IN ('pending', 'approved', 'processing');
COMMENT ON INDEX idx_vip_refund_pending IS '待处理退款索引';

-- 分析表统计信息
ANALYZE vip_refund;

-- ========================================
-- 4. vip_pay_notify_log 表索引优化
-- ========================================

-- 交易ID查询索引
CREATE INDEX idx_vip_notify_transaction ON vip_pay_notify_log(transaction_id);
COMMENT ON INDEX idx_vip_notify_transaction IS '交易ID查询索引';

-- 订单号查询索引
CREATE INDEX idx_vip_notify_order ON vip_pay_notify_log(out_trade_no);
COMMENT ON INDEX idx_vip_notify_order IS '订单号查询索引';

-- 通知时间索引（用于日志清理）
CREATE INDEX idx_vip_notify_time ON vip_pay_notify_log(notify_time);
COMMENT ON INDEX idx_vip_notify_time IS '通知时间索引';

-- 分析表统计信息
ANALYZE vip_pay_notify_log;

-- ========================================
-- 5. user 表VIP相关索引优化
-- ========================================

-- VIP用户查询索引
CREATE INDEX idx_user_vip_type ON user(user_type, vip_expire_time)
WHERE user_type IN ('vip_monthly', 'vip_yearly');
COMMENT ON INDEX idx_user_vip_type IS 'VIP用户查询索引';

-- VIP过期扫描索引
CREATE INDEX idx_user_vip_expire ON user(vip_expire_time)
WHERE vip_expire_time IS NOT NULL;
COMMENT ON INDEX idx_user_vip_expire IS 'VIP过期扫描索引';

-- 推广码索引
CREATE INDEX idx_user_promotion ON user(promotion_code)
WHERE promotion_code IS NOT NULL;
COMMENT ON INDEX idx_user_promotion IS '推广码索引';

-- 分析表统计信息
ANALYZE user;

-- ========================================
-- 6. 复合查询优化
-- ========================================

-- 创建物化视图：用户VIP统计（可选）
CREATE MATERIALIZED VIEW mv_user_vip_stats AS
SELECT 
    u.user_id,
    u.nick_name,
    u.user_type,
    u.vip_expire_time,
    COUNT(DISTINCT vo.order_no) as total_orders,
    SUM(CASE WHEN vo.status = 'paid' THEN vo.amount ELSE 0 END) as total_paid_amount,
    MAX(vo.paid_time) as last_paid_time,
    vs.status as subscription_status,
    vs.auto_renew,
    vs.next_renew_time
FROM user u
LEFT JOIN vip_order vo ON u.user_id = vo.user_id
LEFT JOIN vip_subscription vs ON u.user_id = vs.user_id AND vs.status = 'active'
WHERE u.user_type IN ('vip_monthly', 'vip_yearly')
GROUP BY u.user_id, u.nick_name, u.user_type, u.vip_expire_time, 
         vs.status, vs.auto_renew, vs.next_renew_time;

-- 创建物化视图索引
CREATE UNIQUE INDEX idx_mv_vip_stats_user ON mv_user_vip_stats(user_id);
CREATE INDEX idx_mv_vip_stats_expire ON mv_user_vip_stats(vip_expire_time);

-- 定期刷新物化视图（可通过定时任务执行）
-- REFRESH MATERIALIZED VIEW CONCURRENTLY mv_user_vip_stats;

-- ========================================
-- 7. 查询性能分析
-- ========================================

-- 分析慢查询（MySQL）
-- SHOW VARIABLES LIKE 'slow_query_log';
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 1;

-- 查看索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- ========================================
-- 8. 索引维护建议
-- ========================================

-- 定期重建索引（月度维护）
-- REINDEX TABLE vip_order;
-- REINDEX TABLE vip_subscription;
-- REINDEX TABLE vip_refund;

-- 定期更新统计信息（每周执行）
-- VACUUM ANALYZE vip_order;
-- VACUUM ANALYZE vip_subscription;
-- VACUUM ANALYZE vip_refund;

-- ========================================
-- 9. 性能监控查询
-- ========================================

-- 查找缺失索引的查询
WITH missing_indexes AS (
    SELECT 
        schemaname,
        tablename,
        attname,
        n_distinct,
        correlation
    FROM pg_stats
    WHERE schemaname = 'public'
    AND n_distinct > 100
    AND correlation < 0.1
)
SELECT * FROM missing_indexes;

-- 查找未使用的索引（可考虑删除）
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
AND idx_scan = 0
AND indexrelid > 16384;

-- ========================================
-- 注意事项：
-- 1. 在生产环境执行前，请先在测试环境验证
-- 2. 创建索引时使用 CONCURRENTLY 选项避免锁表
-- 3. 定期监控索引使用情况，删除未使用的索引
-- 4. 根据实际查询模式调整索引策略
-- ========================================