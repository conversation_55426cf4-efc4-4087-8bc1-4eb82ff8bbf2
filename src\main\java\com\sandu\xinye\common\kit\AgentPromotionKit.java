package com.sandu.xinye.common.kit;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.sandu.xinye.common.service.PromotionAgentClient;

/**
 * 代理商推广码验证工具类
 * 用于调用代理商系统接口验证推广码是否存在
 *
 * 注意：此类已重构为委托给 PromotionAgentClient，保持向后兼容
 *
 * <AUTHOR> Team
 * @since 2024-08-06
 */
public class AgentPromotionKit {
    
    /**
     * 是否启用代理商验证
     */
    public static boolean isVerifyEnabled() {
        return PropKit.use("common_config.txt").getBoolean("promotion.agent.verify.enable", true);
    }

    /**
     * 验证失败时是否允许绑定
     */
    public static boolean isFailoverEnabled() {
        return PropKit.use("common_config.txt").getBoolean("promotion.agent.verify.failover", true);
    }

    /**
     * 验证推广码是否在代理商系统中存在
     *
     * @param userId 用户ID
     * @param promotionCode 推广码
     * @return 验证结果，true-存在，false-不存在，null-验证失败或功能未启用
     */
    public static Boolean verifyPromotionCode(Integer userId, String promotionCode) {
        // 委托给 PromotionAgentClient 统一处理
        return PromotionAgentClient.me.verifyPromotionCodeExists(userId, promotionCode);
    }

    /**
     * 检查代理商验证功能是否可用
     * 可以在系统初始化时调用此方法进行健康检查
     *
     * @return true-可用，false-不可用
     */
    public static boolean isAgentVerifyAvailable() {
        try {
            // 使用测试参数验证接口可用性
            Boolean result = verifyPromotionCode(999999, "TEST");
            // 只要能得到响应（不管是true还是false）就认为接口可用
            return result != null;
        } catch (Exception e) {
            LogKit.error("代理商验证接口健康检查失败", e);
            return false;
        }
    }

    /**
     * 获取签名算法描述（用于调试和文档）
     *
     * @return 签名算法说明
     */
    public static String getSignAlgorithmDescription() {
        return "SHA1(userId + secret + timestamp) - 统一使用 PromotionAgentClient 实现";
    }
}