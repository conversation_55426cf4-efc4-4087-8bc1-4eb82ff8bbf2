package com.sandu.xinye.common.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.HashKit;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.monitor.PromotionEventMonitor;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 代理商平台客户端
 * 负责向代理商平台上报推广事件
 */
public class PromotionAgentClient {
    
    public static final PromotionAgentClient me = new PromotionAgentClient();
    
    /**
     * 上报结果
     */
    public static class ReportResult {
        private boolean success;
        private int httpStatus;
        private int durationMs;
        private String rawResp;
        private String message;
        
        public ReportResult(boolean success, int httpStatus, int durationMs, String rawResp, String message) {
            this.success = success;
            this.httpStatus = httpStatus;
            this.durationMs = durationMs;
            this.rawResp = rawResp;
            this.message = message;
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public int getHttpStatus() { return httpStatus; }
        public int getDurationMs() { return durationMs; }
        public String getRawResp() { return rawResp; }
        public String getMessage() { return message; }
    }
    
    /**
     * 验证推广码是否在代理商系统中存在
     *
     * @param userId 用户ID
     * @param promotionCode 推广码
     * @return 验证结果：true-存在，false-不存在，null-验证失败或功能未启用
     */
    public Boolean verifyPromotionCodeExists(Integer userId, String promotionCode) {
        long startTime = System.currentTimeMillis();

        try {
            // 检查是否启用
            boolean enable = PropKit.use("common_config.txt").getBoolean("promotion.agent.verify.enable", false);
            if (!enable) {
                LogKit.info("推广码验证已禁用，跳过验证");
                return null;
            }

            // 读取配置
            String url = PropKit.use("common_config.txt").get("promotion.agent.verify.url");
            int timeout = PropKit.use("common_config.txt").getInt("promotion.agent.verify.timeout", 5000);
            String secret = PropKit.use("common_config.txt").get("promotion.agent.verify.secret");
            String project = PropKit.use("common_config.txt").get("promotion.agent.verify.project", "xprinter");

            if (StrKit.isBlank(url) || StrKit.isBlank(secret)) {
                LogKit.error("推广码验证配置不完整：url=" + url + ", secret=" + (StrKit.isBlank(secret) ? "空" : "已配置"));
                return null;
            }

            if (userId == null || StrKit.isBlank(promotionCode)) {
                LogKit.warn("推广码验证参数无效 - userId:" + userId + ", promotionCode:" + maskPromotionCode(promotionCode));
                return null;
            }

            // 构造请求参数（不包含 actionType，仅验证存在性）
            String user = String.valueOf(userId);
            String timestamp = String.valueOf(System.currentTimeMillis());
            String sign = HashKit.sha1(user + secret + timestamp);

            Map<String, Object> payload = new HashMap<>();
            payload.put("user", user);
            payload.put("timestamp", timestamp);
            payload.put("sign", sign);
            payload.put("promotionCode", promotionCode);
            payload.put("project", project);
            // 注意：验证存在性不需要 actionType

            // 发送请求
            String jsonPayload = JSON.toJSONString(payload);
            LogKit.info("推广码验证开始 - userId:" + userId + ", promotionCode:" + maskPromotionCode(promotionCode));

            ReportResult result = sendHttpRequest(url, jsonPayload, timeout);

            int durationMs = (int)(System.currentTimeMillis() - startTime);
            LogKit.info("推广码验证完成 - userId:" + userId + ", promotionCode:" + maskPromotionCode(promotionCode) +
                       ", httpStatus:" + result.getHttpStatus() + ", success:" + result.isSuccess() +
                       ", duration:" + durationMs + "ms");

            // 记录监控数据（使用特殊的 actionType=0 表示验证请求）
            PromotionEventMonitor.me.recordReport(result.isSuccess(), durationMs);

            return result.isSuccess();

        } catch (Exception e) {
            int durationMs = (int)(System.currentTimeMillis() - startTime);
            LogKit.error("推广码验证异常 - userId:" + userId + ", promotionCode:" + maskPromotionCode(promotionCode) +
                        ", error:" + e.getMessage());

            // 记录异常监控
            PromotionEventMonitor.me.recordException(e.getClass().getSimpleName(), e.getMessage());

            return null;
        }
    }

    /**
     * 上报推广事件到代理商平台
     *
     * @param userId 用户ID
     * @param promotionCode 推广码
     * @param actionType 事件类型：1-注册绑定，2-首次有效打印，3-VIP订阅
     * @return 上报结果
     */
    public ReportResult reportEvent(Integer userId, String promotionCode, Integer actionType) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 检查是否启用
            boolean enable = PropKit.use("common_config.txt").getBoolean("promotion.agent.verify.enable", false);
            if (!enable) {
                LogKit.info("推广事件上报已禁用，跳过上报");
                return new ReportResult(false, 0, 0, "{\"disabled\":true}", "推广事件上报已禁用");
            }
            
            // 读取配置
            String url = PropKit.use("common_config.txt").get("promotion.agent.report.url");
            int timeout = PropKit.use("common_config.txt").getInt("promotion.agent.verify.timeout", 5000);
            String secret = PropKit.use("common_config.txt").get("promotion.agent.verify.secret");
            String project = PropKit.use("common_config.txt").get("promotion.agent.verify.project", "xprinter");
            
            if (StrKit.isBlank(url) || StrKit.isBlank(secret)) {
                LogKit.error("推广事件上报配置不完整：url=" + url + ", secret=" + (StrKit.isBlank(secret) ? "空" : "已配置"));
                return new ReportResult(false, 0, 0, "{\"error\":\"config_missing\"}", "配置不完整");
            }
            
            // 构造请求参数
            String user = String.valueOf(userId);
            String timestamp = String.valueOf(System.currentTimeMillis());
            String sign = HashKit.sha1(user + secret + timestamp);
            
            Map<String, Object> payload = new HashMap<>();
            payload.put("user", user);
            payload.put("timestamp", timestamp);
            payload.put("sign", sign);
            payload.put("promotionCode", promotionCode);
            payload.put("project", project);
            payload.put("actionType", actionType);
            
            // 发送请求
            String jsonPayload = JSON.toJSONString(payload);
            LogKit.info("推广事件上报开始 - userId:" + userId + ", actionType:" + actionType + 
                       ", promotionCode:" + maskPromotionCode(promotionCode));
            
            ReportResult result = sendHttpRequest(url, jsonPayload, timeout);
            
            int durationMs = (int)(System.currentTimeMillis() - startTime);
            LogKit.info("推广事件上报完成 - userId:" + userId + ", actionType:" + actionType +
                       ", httpStatus:" + result.getHttpStatus() + ", success:" + result.isSuccess() +
                       ", duration:" + durationMs + "ms");

            // 记录监控数据
            PromotionEventMonitor.me.recordReport(result.isSuccess(), durationMs);

            return new ReportResult(result.isSuccess(), result.getHttpStatus(), durationMs,
                                  result.getRawResp(), result.getMessage());
            
        } catch (Exception e) {
            int durationMs = (int)(System.currentTimeMillis() - startTime);
            LogKit.error("推广事件上报异常 - userId:" + userId + ", actionType:" + actionType +
                        ", error:" + e.getMessage());

            // 记录异常监控
            PromotionEventMonitor.me.recordException(e.getClass().getSimpleName(), e.getMessage());

            String errorResp = "{\"error\":\"" + e.getClass().getSimpleName() + "\",\"message\":\"" +
                              e.getMessage() + "\"}";
            return new ReportResult(false, 0, durationMs, errorResp, "系统异常：" + e.getMessage());
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private ReportResult sendHttpRequest(String url, String jsonPayload, int timeout) {
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        
        try {
            // 配置超时
            RequestConfig config = RequestConfig.custom()
                    .setConnectTimeout(timeout)
                    .setSocketTimeout(timeout)
                    .setConnectionRequestTimeout(timeout)
                    .build();
            
            client = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            post.setConfig(config);
            post.setHeader("Content-Type", "application/json");
            post.setEntity(new StringEntity(jsonPayload, "UTF-8"));
            
            response = client.execute(post);
            int httpStatus = response.getStatusLine().getStatusCode();
            
            HttpEntity entity = response.getEntity();
            String responseBody = EntityUtils.toString(entity, "UTF-8");
            
            // 构造原始响应
            String rawResp = "{\"httpStatus\":" + httpStatus + ",\"body\":" + responseBody + "}";
            
            if (httpStatus == 200) {
                // 解析业务结果
                try {
                    JSONObject json = JSON.parseObject(responseBody);
                    Boolean data = json.getBoolean("data");
                    boolean success = data != null && data;
                    
                    return new ReportResult(success, httpStatus, 0, rawResp, 
                                          success ? "成功" : "业务失败：data=" + data);
                } catch (Exception e) {
                    LogKit.error("解析代理商平台响应失败：" + responseBody);
                    return new ReportResult(false, httpStatus, 0, rawResp, "响应解析失败");
                }
            } else {
                return new ReportResult(false, httpStatus, 0, rawResp, "HTTP错误：" + httpStatus);
            }
            
        } catch (Exception e) {
            String errorResp = "{\"error\":\"" + e.getClass().getSimpleName() + "\",\"message\":\"" + 
                              e.getMessage() + "\"}";
            return new ReportResult(false, 0, 0, errorResp, "网络异常：" + e.getMessage());
        } finally {
            try {
                if (response != null) response.close();
                if (client != null) client.close();
            } catch (Exception e) {
                LogKit.error("关闭HTTP连接异常：" + e.getMessage());
            }
        }
    }
    
    /**
     * 脱敏推广码（只显示前后各2位）
     */
    private String maskPromotionCode(String promotionCode) {
        if (StrKit.isBlank(promotionCode) || promotionCode.length() <= 4) {
            return promotionCode;
        }
        return promotionCode.substring(0, 2) + "****" + 
               promotionCode.substring(promotionCode.length() - 2);
    }
}
