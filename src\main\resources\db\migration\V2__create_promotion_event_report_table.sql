-- 创建推广事件上报记录表
-- 用于记录向代理商平台成功上报的事件，作为统计与审计的唯一口径

CREATE TABLE promotion_event_report (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id INT NOT NULL COMMENT '用户ID',
    action_type TINYINT NOT NULL COMMENT '事件类型：1-注册绑定，2-首次有效打印，3-VIP订阅',
    success TINYINT NOT NULL DEFAULT 1 COMMENT '是否成功：1-成功，0-失败',
    promotion_code VARCHAR(32) COMMENT '推广码（冗余记录）',
    biz_ref VARCHAR(128) COMMENT '业务引用：actionType=1为bind:code，2为print_record_id，3为订单号',
    event_time DATETIME COMMENT '业务事件发生时间',
    reported_at DATETIME NOT NULL COMMENT '实际上报时间',
    http_status INT COMMENT 'HTTP状态码',
    duration_ms INT COMMENT '调用耗时（毫秒）',
    third_party_raw_resp TEXT COMMENT '第三方响应原文或异常概览',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_action (user_id, action_type) COMMENT '用户事件类型索引，用于幂等查询',
    INDEX idx_reported_at (reported_at) COMMENT '上报时间索引，用于统计查询',
    INDEX idx_success (success) COMMENT '成功状态索引，用于统计查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广事件上报记录表';
