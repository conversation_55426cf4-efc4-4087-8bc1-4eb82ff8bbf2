package com.sandu.xinye.api.v2.vip;

import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AppUserInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;

import java.util.HashMap;
import java.util.Map;

/**
 * VIP用户状态控制器
 */
public class VipUserController extends AppController {
    
    /**
     * 获取用户VIP状态
     * GET /api/v2/vip/status
     */
    @Before({AppUserInterceptor.class})
    public void status() {
        try {
            User user = getUser();
            
            // 重新从数据库获取最新的用户信息
            User latestUser = User.dao.findById(user.getUserId());
            if (latestUser == null) {
                renderJson(RetKit.fail("用户不存在"));
                return;
            }
            
            // 构建VIP状态信息
            Map<String, Object> vipStatus = new HashMap<>();
            vipStatus.put("isVip", latestUser.isVip());
            vipStatus.put("userTier", latestUser.getUserTier());
            vipStatus.put("userTierDisplay", latestUser.getUserTierEnum().getDisplayName());
            vipStatus.put("vipExpireTime", latestUser.getVipExpireTime());
            vipStatus.put("isExpired", latestUser.isVipExpired());
            vipStatus.put("dataRecoveryPeriod", latestUser.getDataRecoveryPeriodDescription());
            
            // 如果不是VIP，添加升级信息
            if (!latestUser.isVip()) {
                Map<String, Object> upgradeInfo = new HashMap<>();
                upgradeInfo.put("monthlyPrice", 9.9);
                upgradeInfo.put("yearlyPrice", 99.0);
                upgradeInfo.put("features", getVipFeatures());
                vipStatus.put("upgradeInfo", upgradeInfo);
            }
            
            renderJson(RetKit.ok("查询成功").set("vipStatus", vipStatus));
            
        } catch (Exception e) {
            LogKit.error("查询VIP状态失败", e);
            renderJson(RetKit.fail("查询失败"));
        }
    }
    
    /**
     * 获取VIP套餐信息
     * GET /api/v2/vip/plans
     */
    public void plans() {
        try {
            Map<String, Object> plansInfo = new HashMap<>();

            // 从价格服务获取四档价格（单位：分）
            int mSub = com.sandu.xinye.common.service.VipPriceConfigService.me.getPlanPrice("monthly", "subscribe");
            int mOne = com.sandu.xinye.common.service.VipPriceConfigService.me.getPlanPrice("monthly", "one_time");
            int ySub = com.sandu.xinye.common.service.VipPriceConfigService.me.getPlanPrice("yearly", "subscribe");
            int yOne = com.sandu.xinye.common.service.VipPriceConfigService.me.getPlanPrice("yearly", "one_time");

            Map<String, Object> monthly = new HashMap<>();
            monthly.put("plan", "monthly");
            monthly.put("name", "VIP月度会员");
            monthly.put("subscribePrice", mSub);
            monthly.put("oneTimePrice", mOne);
            monthly.put("duration", "1个月");
            monthly.put("features", getVipFeatures());

            Map<String, Object> yearly = new HashMap<>();
            yearly.put("plan", "yearly");
            yearly.put("name", "VIP年度会员");
            yearly.put("subscribePrice", ySub);
            yearly.put("oneTimePrice", yOne);
            yearly.put("duration", "12个月");
            yearly.put("features", getVipFeatures());

            plansInfo.put("monthly", monthly);
            plansInfo.put("yearly", yearly);
            plansInfo.put("recommendedPlan", "yearly");

            renderJson(RetKit.ok("查询成功").set("plans", plansInfo));

        } catch (Exception e) {
            LogKit.error("获取VIP套餐信息失败", e);
            renderJson(RetKit.fail("查询失败"));
        }
    }

    /**
     * 获取VIP特权列表
     */
    private String[] getVipFeatures() {
        return new String[]{
            "识图新建功能",
            "6个月数据恢复期限",
            "无广告体验",
            "优先客服支持"
        };
    }
}
