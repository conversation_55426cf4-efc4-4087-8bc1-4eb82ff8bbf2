# API 接口设计（v1）

## 1. 下单接口
POST /api/v2/vip/order/create
- 鉴权：AppUserInterceptor
- 入参：
  - plan: string, 枚举 [monthly, yearly]
  - channel: string, 固定 "wechat"
- 出参：
  - orderNo: string
  - payParams: object（前端调起支付所需参数，按 trade_type 返回）

## 2. 订单查询
GET /api/v2/vip/order/get?orderNo=xxx
- 鉴权：AppUserInterceptor
- 出参：{ status: created|paid|closed|failed, userVip: {...} }

## 3. 支付回调（微信）
POST /api/v2/vip/pay/wechat/notify
- 鉴权：公网回调，无需登录
- 行为：验签 + 解密 + 查单 + 更新订单状态 + 发货（开通/续期 VIP）
- 返回：HTTP 200 with {"code":"SUCCESS","message":"成功"}

## 4. 获取用户 VIP 状态
GET /api/v2/vip/status
- 鉴权：AppUserInterceptor
- 出参：
  - isVip: boolean
  - userTier: FREE|VIP_MONTHLY|VIP_YEARLY
  - vipExpireTime: datetime|null

## 5. OCR 接口权限
- 在 OcrController.recognize 的开头增加：
  - if (!VipPermissionService.me.hasPermission(userId, FEATURE_OCR_CREATE)) return RetKit.fail("识图新建为VIP专享功能，前往开通")

---

## 新增API接口（修复后实现）

### 6. 监控指标接口（仅业务指标）
- **GET** `/api/v2/vip/metrics` - 获取所有VIP业务指标
- **GET** `/api/v2/vip/metrics/memory` - 获取内存指标
- **GET** `/api/v2/vip/metrics/database` - 获取数据库指标
- **POST** `/api/v2/vip/metrics/reset` - 重置内存计数器
- **GET** `/api/v2/vip/metrics/summary` - 获取指标摘要

### 7. 退款管理接口
- **POST** `/api/v2/vip/refund/apply` - 申请退款
- **GET** `/api/v2/vip/refund/status/{refundNo}` - 查询退款状态

管理员接口：
- **POST** `/admin/vip/refund/review` - 审核退款
- **GET** `/admin/vip/refund/list` - 获取退款列表

### 8. 价格配置接口
- **GET** `/api/v2/vip/price/configs` - 获取价格配置列表
- **GET** `/api/v2/vip/price/{plan}` - 获取套餐价格（含促销）

管理员接口：
- **POST** `/admin/vip/price/update` - 更新价格配置
- **POST** `/admin/vip/price/promotion` - 创建促销活动
- **GET** `/admin/vip/price/configs` - 获取价格配置（管理员版）
- **POST** `/admin/vip/price/cache/clear` - 清除价格缓存
- **GET** `/admin/vip/stats` - 获取VIP统计数据

