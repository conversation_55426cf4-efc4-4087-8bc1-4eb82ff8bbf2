package com.sandu.xinye.common.interceptor;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.kit.Kv;
import com.sandu.xinye.common.service.WechatPaySignatureUtil;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付签名验证拦截器
 * 用于验证微信支付回调请求的签名，防止伪造回调
 * 
 * 使用方法：
 * @Before(WechatSignatureInterceptor.class)
 * public void callback() {
 *     // 处理支付回调
 * }
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class WechatSignatureInterceptor implements Interceptor {
    
    /**
     * 拦截支付回调请求，验证签名
     */
    @Override
    public void intercept(Invocation inv) {
        Controller controller = inv.getController();
        HttpServletRequest request = controller.getRequest();
        
        try {
            // 1. 提取微信支付签名头
            String signature = request.getHeader("Wechatpay-Signature");
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");
            String serial = request.getHeader("Wechatpay-Serial");
            
            // 2. 验证必要参数
            if (StrKit.isBlank(signature) || StrKit.isBlank(timestamp) || 
                StrKit.isBlank(nonce) || StrKit.isBlank(serial)) {
                LogKit.error("微信支付回调缺少必要的签名参数");
                renderFail(controller, "MISSING_SIGNATURE", "缺少签名参数");
                return;
            }
            
            // 3. 验证时间戳（5分钟内有效）
            if (!WechatPaySignatureUtil.isTimestampValid(timestamp)) {
                LogKit.warn("微信支付回调时间戳过期: " + timestamp);
                renderFail(controller, "TIMESTAMP_EXPIRED", "请求已过期");
                return;
            }
            
            // 4. 防重放攻击检查
            if (!WechatPaySignatureUtil.checkNonceReplay(timestamp, nonce)) {
                LogKit.warn("检测到重放攻击，nonce: " + nonce);
                renderFail(controller, "REPLAY_ATTACK", "重复请求");
                return;
            }
            
            // 5. 获取请求体
            String body = controller.getRawData();
            if (StrKit.isBlank(body)) {
                LogKit.error("微信支付回调请求体为空");
                renderFail(controller, "EMPTY_BODY", "请求体为空");
                return;
            }
            
            // 6. 验证签名
            boolean valid = WechatPaySignatureUtil.verifySignature(
                signature, timestamp, nonce, body, serial
            );
            
            if (!valid) {
                LogKit.error("微信支付签名验证失败");
                LogKit.error("Signature: " + signature);
                LogKit.error("Serial: " + serial);
                LogKit.error("Timestamp: " + timestamp);
                LogKit.error("Nonce: " + nonce);
                renderFail(controller, "INVALID_SIGNATURE", "签名验证失败");
                return;
            }
            
            // 7. 签名验证通过，继续处理
            LogKit.info("微信支付签名验证通过，nonce: " + nonce);
            inv.invoke();
            
        } catch (Exception e) {
            LogKit.error("微信支付签名验证异常", e);
            renderFail(controller, "SYSTEM_ERROR", "系统异常");
        }
    }
    
    /**
     * 返回失败响应（微信支付要求的格式）
     */
    private void renderFail(Controller controller, String code, String message) {
        Map<String, String> response = new HashMap<>();
        response.put("code", "FAIL");
        response.put("message", message);
        
        controller.getResponse().setContentType("application/json; charset=utf-8");
        controller.renderJson(response);
        
        LogKit.warn("微信支付回调验证失败: " + code + " - " + message);
    }
}