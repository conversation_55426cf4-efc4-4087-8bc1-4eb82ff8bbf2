package com.sandu.xinye.common.monitor;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 推广事件监控
 * 提供统计和监控功能
 */
public class PromotionEventMonitor {
    
    public static final PromotionEventMonitor me = new PromotionEventMonitor();
    
    // 计数器
    private final AtomicLong totalReports = new AtomicLong(0);
    private final AtomicLong successReports = new AtomicLong(0);
    private final AtomicLong failedReports = new AtomicLong(0);
    private final AtomicLong totalDuration = new AtomicLong(0);
    
    /**
     * 记录上报事件
     */
    public void recordReport(boolean success, int durationMs) {
        totalReports.incrementAndGet();
        totalDuration.addAndGet(durationMs);
        
        if (success) {
            successReports.incrementAndGet();
        } else {
            failedReports.incrementAndGet();
        }
        
        // 每100次上报打印一次统计
        if (totalReports.get() % 100 == 0) {
            logStatistics();
        }
    }
    
    /**
     * 记录异常上报
     */
    public void recordException(String errorType, String message) {
        failedReports.incrementAndGet();
        LogKit.warn("推广事件上报异常 - type:" + errorType + ", message:" + message);
    }
    
    /**
     * 打印统计信息
     */
    public void logStatistics() {
        long total = totalReports.get();
        long success = successReports.get();
        long failed = failedReports.get();
        long avgDuration = total > 0 ? totalDuration.get() / total : 0;
        
        double successRate = total > 0 ? (double) success / total * 100 : 0;
        
        LogKit.info(String.format(
            "推广事件上报统计 - 总计:%d, 成功:%d, 失败:%d, 成功率:%.2f%%, 平均耗时:%dms",
            total, success, failed, successRate, avgDuration
        ));
    }
    
    /**
     * 获取数据库统计
     */
    public void logDatabaseStatistics() {
        try {
            // 按事件类型统计
            List<Record> typeStats = Db.find(
                "SELECT action_type, COUNT(*) as total, SUM(success) as success_count " +
                "FROM promotion_event_report GROUP BY action_type ORDER BY action_type"
            );
            
            LogKit.info("=== 推广事件数据库统计 ===");
            for (Record record : typeStats) {
                int actionType = record.getInt("action_type");
                int total = record.getInt("total");
                int successCount = record.getInt("success_count");
                double rate = total > 0 ? (double) successCount / total * 100 : 0;
                
                String typeName = getActionTypeName(actionType);
                LogKit.info(String.format(
                    "事件类型:%s, 总计:%d, 成功:%d, 成功率:%.2f%%",
                    typeName, total, successCount, rate
                ));
            }
            
            // 最近24小时统计
            Record recentStats = Db.findFirst(
                "SELECT COUNT(*) as total, SUM(success) as success_count " +
                "FROM promotion_event_report WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)"
            );
            
            if (recentStats != null) {
                int recentTotal = recentStats.getInt("total");
                int recentSuccess = recentStats.getInt("success_count");
                double recentRate = recentTotal > 0 ? (double) recentSuccess / recentTotal * 100 : 0;
                
                LogKit.info(String.format(
                    "最近24小时 - 总计:%d, 成功:%d, 成功率:%.2f%%",
                    recentTotal, recentSuccess, recentRate
                ));
            }
            
        } catch (Exception e) {
            LogKit.error("获取数据库统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取事件类型名称
     */
    private String getActionTypeName(int actionType) {
        switch (actionType) {
            case 1: return "注册绑定";
            case 2: return "首次打印";
            case 3: return "VIP订阅";
            default: return "未知(" + actionType + ")";
        }
    }
    
    /**
     * 重置计数器
     */
    public void resetCounters() {
        totalReports.set(0);
        successReports.set(0);
        failedReports.set(0);
        totalDuration.set(0);
        LogKit.info("推广事件监控计数器已重置");
    }
    
    /**
     * 获取当前统计
     */
    public String getCurrentStats() {
        long total = totalReports.get();
        long success = successReports.get();
        long failed = failedReports.get();
        long avgDuration = total > 0 ? totalDuration.get() / total : 0;
        double successRate = total > 0 ? (double) success / total * 100 : 0;
        
        return String.format(
            "总计:%d, 成功:%d, 失败:%d, 成功率:%.2f%%, 平均耗时:%dms",
            total, success, failed, successRate, avgDuration
        );
    }
}
