package com.sandu.xinye.api.v2.vip;

import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.controller.BaseController;
import com.sandu.xinye.common.interceptor.WechatSignatureInterceptor;
import com.sandu.xinye.common.service.WechatPayCallbackService;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * VIP支付回调控制器
 * 处理微信支付异步通知
 * 
 * 安全增强：添加签名验证拦截器
 */
public class VipPayCallbackController extends BaseController {
    
    /**
     * 微信支付回调接口
     * POST /api/v2/vip/pay/wechat/notify
     * 
     * 注意：此接口不需要登录验证，是微信服务器回调
     * 安全增强：添加签名验证拦截器，防止伪造回调
     */
    @Before(WechatSignatureInterceptor.class)
    public void wechatNotify() {
        try {
            HttpServletRequest request = getRequest();
            String requestBody = getRequestBody(request);
            
            LogKit.info("收到微信支付回调通知");
            LogKit.debug("回调请求体: " + requestBody);
            
            // 记录回调头部信息（用于调试）
            logCallbackHeaders(request);
            
            // 处理回调
            boolean success = WechatPayCallbackService.me.handleCallback(request, requestBody);
            
            if (success) {
                // 返回成功响应给微信
                renderWechatSuccess();
            } else {
                // 返回失败响应给微信
                renderWechatFail("处理失败");
            }
            
        } catch (Exception e) {
            LogKit.error("处理微信支付回调异常", e);
            renderWechatFail("系统异常");
        }
    }
    
    /**
     * 读取请求体
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            StringBuilder body = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            
            return body.toString();
            
        } catch (IOException e) {
            LogKit.error("读取微信支付回调请求体失败", e);
            return "";
        }
    }
    
    /**
     * 记录回调头部信息
     */
    private void logCallbackHeaders(HttpServletRequest request) {
        try {
            LogKit.debug("微信支付回调头部信息:");
            LogKit.debug("  Wechatpay-Timestamp: " + request.getHeader("Wechatpay-Timestamp"));
            LogKit.debug("  Wechatpay-Nonce: " + request.getHeader("Wechatpay-Nonce"));
            LogKit.debug("  Wechatpay-Signature: " + maskSensitive(request.getHeader("Wechatpay-Signature")));
            LogKit.debug("  Wechatpay-Serial: " + request.getHeader("Wechatpay-Serial"));
            LogKit.debug("  Content-Type: " + request.getContentType());
            LogKit.debug("  User-Agent: " + request.getHeader("User-Agent"));
            
        } catch (Exception e) {
            LogKit.warn("记录回调头部信息失败", e);
        }
    }
    
    /**
     * 返回成功响应给微信
     */
    private void renderWechatSuccess() {
        Map<String, String> response = new HashMap<>();
        response.put("code", "SUCCESS");
        response.put("message", "成功");
        
        getResponse().setContentType("application/json; charset=utf-8");
        renderJson(response);
        
        LogKit.info("微信支付回调处理成功，已返回SUCCESS");
    }
    
    /**
     * 返回失败响应给微信
     */
    private void renderWechatFail(String message) {
        Map<String, String> response = new HashMap<>();
        response.put("code", "FAIL");
        response.put("message", StrKit.isBlank(message) ? "失败" : message);
        
        getResponse().setContentType("application/json; charset=utf-8");
        renderJson(response);
        
        LogKit.warn("微信支付回调处理失败，已返回FAIL: " + message);
    }
    
    /**
     * 隐藏敏感信息
     */
    private String maskSensitive(String value) {
        if (StrKit.isBlank(value) || value.length() <= 8) {
            return value;
        }
        return value.substring(0, 4) + "****" + value.substring(value.length() - 4);
    }
}
