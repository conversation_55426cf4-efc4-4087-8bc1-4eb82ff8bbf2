# VIP购买业务流程与架构（v1）

## 核心流程
1. 前端发起下单：POST /api/v2/vip/order/create { plan: monthly|yearly, channel: wechat }
2. 后端创建订单（待支付）：生成订单号、金额、签名参数（根据渠道）返回给前端
3. 用户完成支付：微信支付调起并完成，异步通知（回调）到后端
4. 后端验签与查单确认成功：更新订单状态 paid，并触发“发货”逻辑：开通/续期 VIP
5. 返回结果：前端可轮询订单状态或通过回调/推送知晓

## 订单与发货（开通 VIP）
- 幂等约束：同一订单号只允许发货一次
- 续期规则：
  - 若当前用户非 VIP（FREE），则从当前时间开始计算有效期
  - 若当前用户是 VIP 且未过期，则在原到期时间基础上叠加
  - 月度：+30 天；年度：+365 天（或使用自然月/年，v1 采用固定天数）
- 用户模型更新：user.userTier、user.vipExpireTime（新增字段）

## 权限控制
- OCR 识图新建接口（OcrController.recognize）增加 VIP 功能检查：
  - 使用 VipPermissionService.hasPermission(userId, FEATURE_OCR_CREATE)
  - 如果无权限，返回 RetKit.fail("识图新建为VIP专享功能，前往开通") 并携带升级引导内容

## 系统架构
- Controller 层：VipOrderController（下单、查询）、VipPayCallbackController（回调）
- Service 层：VipOrderService（创建/查单）、VipPayService（聚合支付/微信签名）、VipDeliveryService（发货：开通VIP）
- DAO/Model：VipOrder（订单表）、User（扩展字段）

## 回调安全
- 使用微信支付 v3：
  - ✅ 验签：HTTP 头部 Wechatpay-Timestamp、Wechatpay-Nonce、Wechatpay-Signature、Wechatpay-Serial
  - ✅ 通知报文 resource 对称解密（API v3 key）
  - ✅ 二次查单确认（可选，提高安全性）
  - ✅ 平台证书自动管理和验证
  - ✅ 时间戳有效性检查（防重放攻击）
- ✅ 重试与幂等：回调可重复，多次调用发货时通过订单状态 + 唯一约束保证只发货一次

---

## 新增业务流程（修复后实现）

### 退款流程
```
用户申请退款 → 系统验证（7天内、已支付） → 管理员审核 →
调用微信退款API → 回收VIP权益 → 更新订单状态 → 通知用户
```

### 价格管理流程
```
管理员配置价格 → 系统缓存更新 → 用户下单时获取最新价格 →
检查促销活动 → 计算实际价格 → 生成订单
```

### 监控告警流程
```
业务操作 → 指标埋点 → 实时统计 → 阈值检查 →
告警触发 → 通知运维 → 问题处理
```

### 定时任务流程
```
VIP过期处理：每日2点 → 查询过期用户 → 降级为FREE → 记录日志
订单清理：每小时 → 查询过期未支付订单 → 关闭订单 → 记录日志
```

## 新增核心组件

### 支付安全层
- **WechatPaySignatureUtil** - 微信支付v3签名工具
- **WechatPayHttpClient** - 安全的HTTP客户端
- **WechatPayCertificateManager** - 平台证书管理器

### 业务监控层
- **VipMetricsService** - 业务指标收集服务
- **VipMetricsController** - 监控API接口

### 退款管理层
- **VipRefundService** - 退款业务服务
- **VipRefundController** - 退款管理API

### 价格配置层
- **VipPriceConfigService** - 动态价格配置服务
- **VipPriceController** - 价格管理API

### 定时任务层
- **VipExpireTask** - VIP过期处理任务
- **VipOrderCleanupTask** - 订单清理任务

## 总结

VIP 体系现已实现完整的生产级架构：
- ✅ 清晰的权限控制
- ✅ 灵活的套餐管理
- ✅ 可靠的支付集成
- ✅ 完整的业务闭环
- ✅ **安全的支付处理** - 符合微信支付v3标准
- ✅ **健壮的错误处理** - 完整的异常处理和恢复
- ✅ **全面的监控体系** - 实时业务和技术指标
- ✅ **用户友好的退款** - 完整的退款流程管理
- ✅ **灵活的价格策略** - 动态价格和促销支持

系统具备优秀的扩展性、可维护性和可观测性，可以安全稳定地为用户提供VIP服务。

