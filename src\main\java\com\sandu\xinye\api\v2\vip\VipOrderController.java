package com.sandu.xinye.api.v2.vip;

import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AppUserInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.IdempotentKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.service.VipOrderService;
import com.sandu.xinye.common.service.VipOrderIdempotentService;

/**
 * VIP订单控制器
 */
public class VipOrderController extends AppController {
    
    /**
     * 创建VIP订单（增强幂等性）
     * POST /api/v2/vip/order/create
     *
     * 参数:
     * - plan: monthly|yearly (必填)
     * - purchaseType: subscribe|one_time (可选，默认 one_time)
     * - channel: wechat (可选，默认微信)
     * - idempotentKey: 幂等键 (可选，客户端提供确保幂等性)
     */
    @Before({AppUserInterceptor.class})
    public void create() {
        try {
            User user = getUser();
            String plan = getPara("plan");
            String purchaseType = getPara("purchaseType", "one_time");
            String channel = getPara("channel", "wechat");
            String idempotentKey = getPara("idempotentKey");

            LogKit.info("用户创建VIP订单: " + user.getUserId() + ", 套餐: " + plan + 
                       ", 购买方式: " + purchaseType + ", 幂等键: " + idempotentKey);

            // 参数验证
            if (StrKit.isBlank(plan)) {
                renderJson(RetKit.fail("套餐类型不能为空"));
                return;
            }
            
            if (!"monthly".equals(plan) && !"yearly".equals(plan)) {
                renderJson(RetKit.fail("无效的套餐类型，支持: monthly, yearly"));
                return;
            }
            
            if (!"wechat".equals(channel)) {
                renderJson(RetKit.fail("当前仅支持微信支付"));
                return;
            }

            if (!"one_time".equals(purchaseType) && !"subscribe".equals(purchaseType)) {
                renderJson(RetKit.fail("无效的购买方式，支持: subscribe, one_time"));
                return;
            }
            
            // 使用增强的幂等性服务创建订单
            RetKit result = VipOrderIdempotentService.me.createOrderWithIdempotent(
                user.getUserId(), plan, purchaseType, idempotentKey
            );
            renderJson(result);
            
        } catch (Exception e) {
            LogKit.error("创建VIP订单失败", e);
            renderJson(RetKit.fail("创建订单失败"));
        }
    }
    
    /**
     * 查询订单详情
     * GET /api/v2/vip/order/get?orderNo=xxx
     */
    @Before({AppUserInterceptor.class})
    public void get() {
        try {
            User user = getUser();
            String orderNo = getPara("orderNo");
            
            if (StrKit.isBlank(orderNo)) {
                renderJson(RetKit.fail("订单号不能为空"));
                return;
            }
            
            RetKit result = VipOrderService.me.getOrder(user.getUserId(), orderNo);
            renderJson(result);
            
        } catch (Exception e) {
            LogKit.error("查询VIP订单失败", e);
            renderJson(RetKit.fail("查询订单失败"));
        }
    }
    
    /**
     * 查询用户订单列表
     * GET /api/v2/vip/order/list?page=1&pageSize=10
     */
    @Before({AppUserInterceptor.class})
    public void list() {
        try {
            User user = getUser();
            int page = getParaToInt("page", 1);
            int pageSize = getParaToInt("pageSize", 10);
            
            RetKit result = VipOrderService.me.getUserOrders(user.getUserId(), page, pageSize);
            renderJson(result);
            
        } catch (Exception e) {
            LogKit.error("查询用户订单列表失败", e);
            renderJson(RetKit.fail("查询失败"));
        }
    }
}
