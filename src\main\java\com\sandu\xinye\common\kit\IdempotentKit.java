package com.sandu.xinye.common.kit;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.redis.Cache;
import com.jfinal.plugin.redis.Redis;
import com.sandu.xinye.common.config.RedisConfig;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 幂等性工具类
 * 用于防止重复提交、重复下单等场景
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class IdempotentKit {
    
    // Redis key前缀
    private static final String KEY_PREFIX = "idempotent:";
    
    // 默认锁定时间（30分钟）
    private static final int DEFAULT_LOCK_TIME = 1800;
    
    // 处理中状态
    private static final String STATUS_PROCESSING = "processing";
    
    // 完成状态
    private static final String STATUS_COMPLETED = "completed";
    
    /**
     * 生成幂等键
     * 格式：业务类型:用户ID:业务参数MD5
     */
    public static String generateKey(String bizType, Integer userId, String... params) {
        StringBuilder sb = new StringBuilder();
        sb.append(bizType).append(":").append(userId);
        
        if (params != null && params.length > 0) {
            for (String param : params) {
                if (StrKit.notBlank(param)) {
                    sb.append(":").append(param);
                }
            }
        }
        
        // 对组合字符串进行MD5，避免key过长
        return HashKit.md5(sb.toString());
    }
    
    /**
     * 生成随机幂等键（用于客户端生成）
     */
    public static String generateRandomKey() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 尝试获取幂等锁
     * 
     * @param key 幂等键
     * @param value 业务值（如订单号）
     * @param expireSeconds 过期时间（秒）
     * @return 是否成功获取锁
     */
    public static boolean tryLock(String key, String value, int expireSeconds) {
        if (!RedisConfig.isAvailable()) {
            LogKit.warn("Redis不可用，幂等性检查跳过");
            return true;
        }
        
        try {
            String redisKey = KEY_PREFIX + key;
            Cache cache = Redis.use();
            
            // 先检查是否已存在
            String existing = cache.get(redisKey);
            if (existing != null) {
                // 如果是相同的值，表示是重复请求但允许通过（幂等）
                if (existing.equals(value)) {
                    LogKit.info("幂等键已存在且值相同，允许通过: " + key);
                    return true;
                }
                
                // 如果是处理中状态，拒绝
                if (STATUS_PROCESSING.equals(existing)) {
                    LogKit.warn("请求正在处理中，拒绝重复请求: " + key);
                    return false;
                }
                
                // 其他情况，表示已经处理完成
                LogKit.info("幂等键已存在，返回已处理: " + key);
                return false;
            }
            
            // 设置处理中状态
            cache.setex(redisKey, expireSeconds, STATUS_PROCESSING);
            LogKit.info("成功获取幂等锁: " + key);
            return true;
            
        } catch (Exception e) {
            LogKit.error("幂等性检查异常", e);
            // 异常时默认允许通过，避免影响业务
            return true;
        }
    }
    
    /**
     * 尝试获取幂等锁（使用默认过期时间）
     */
    public static boolean tryLock(String key, String value) {
        return tryLock(key, value, DEFAULT_LOCK_TIME);
    }
    
    /**
     * 标记处理完成
     * 
     * @param key 幂等键
     * @param result 处理结果（如订单号）
     * @param keepSeconds 保留时间（秒）
     */
    public static void markCompleted(String key, String result, int keepSeconds) {
        if (!RedisConfig.isAvailable()) {
            return;
        }
        
        try {
            String redisKey = KEY_PREFIX + key;
            Cache cache = Redis.use();
            
            // 更新为完成状态，保存结果
            cache.setex(redisKey, keepSeconds, result);
            LogKit.info("标记幂等处理完成: " + key + " -> " + result);
            
        } catch (Exception e) {
            LogKit.error("标记幂等完成失败", e);
        }
    }
    
    /**
     * 标记处理完成（默认保留30分钟）
     */
    public static void markCompleted(String key, String result) {
        markCompleted(key, result, DEFAULT_LOCK_TIME);
    }
    
    /**
     * 释放幂等锁
     * 
     * @param key 幂等键
     */
    public static void release(String key) {
        if (!RedisConfig.isAvailable()) {
            return;
        }
        
        try {
            String redisKey = KEY_PREFIX + key;
            Cache cache = Redis.use();
            cache.del(redisKey);
            LogKit.info("释放幂等锁: " + key);
            
        } catch (Exception e) {
            LogKit.error("释放幂等锁失败", e);
        }
    }
    
    /**
     * 获取幂等键的当前值
     * 
     * @param key 幂等键
     * @return 当前值，如果不存在返回null
     */
    public static String getValue(String key) {
        if (!RedisConfig.isAvailable()) {
            return null;
        }
        
        try {
            String redisKey = KEY_PREFIX + key;
            Cache cache = Redis.use();
            return cache.get(redisKey);
            
        } catch (Exception e) {
            LogKit.error("获取幂等值失败", e);
            return null;
        }
    }
    
    /**
     * 检查是否正在处理中
     * 
     * @param key 幂等键
     * @return 是否正在处理
     */
    public static boolean isProcessing(String key) {
        String value = getValue(key);
        return STATUS_PROCESSING.equals(value);
    }
    
    /**
     * 检查是否已完成
     * 
     * @param key 幂等键
     * @return 是否已完成
     */
    public static boolean isCompleted(String key) {
        String value = getValue(key);
        return value != null && !STATUS_PROCESSING.equals(value);
    }
    
    /**
     * 用于订单创建的幂等性检查
     * 
     * @param userId 用户ID
     * @param plan 套餐类型
     * @param purchaseType 购买类型
     * @return 幂等键
     */
    public static String generateOrderKey(Integer userId, String plan, String purchaseType) {
        // 生成基于业务参数的幂等键
        // 同一用户、同一套餐、同一购买类型在短时间内只能创建一个订单
        String timestamp = String.valueOf(System.currentTimeMillis() / 60000); // 按分钟
        return generateKey("order", userId, plan, purchaseType, timestamp);
    }
    
    /**
     * 用于支付回调的幂等性检查
     * 
     * @param transactionId 交易ID
     * @return 幂等键
     */
    public static String generateCallbackKey(String transactionId) {
        return "callback:" + transactionId;
    }
}