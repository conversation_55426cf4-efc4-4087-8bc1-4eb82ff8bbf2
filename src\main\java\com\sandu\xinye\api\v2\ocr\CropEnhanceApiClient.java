package com.sandu.xinye.api.v2.ocr;

import com.alibaba.fastjson.JSON;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.sandu.xinye.api.v2.ocr.dto.CropEnhanceResponse;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

/**
 * TextIn文档切边矫正API客户端
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public class CropEnhanceApiClient {
    
    public static final CropEnhanceApiClient me = new CropEnhanceApiClient();
    
    // TextIn切边矫正API配置
    private static final String CROP_ENHANCE_URL = PropKit.get("textin.crop.api.url", 
        "https://api.textin.com/ai/service/v1/crop_enhance_image");
    private static final String TEXTIN_API_KEY = PropKit.get("textin.api.key", "");
    private static final String TEXTIN_APP_ID = PropKit.get("textin.app.id", "");
    
    // 请求超时配置
    private static final int CONNECT_TIMEOUT = 30000; // 30秒
    private static final int SOCKET_TIMEOUT = 60000;  // 60秒
    
    /**
     * 文档矫正和角点检测
     *
     * @param imageFile 图片文件
     * @param enhanceMode 增强模式，默认-1（禁用增强）
     * @param cropImage 是否执行切边，默认0（不切边）
     * @param sizeAndPosition 自定义切边区域，格式："width,height,x1,y1,x2,y2,x3,y3,x4,y4"
     * @return 矫正结果
     * @throws Exception 调用异常
     */
    /**
     * 文档矫正和角点检测（向后兼容方法）
     *
     * @param imageFile 图片文件
     * @param enhanceMode 增强模式，默认-1（禁用增强）
     * @return 矫正结果
     * @throws Exception 调用异常
     */
    public CropEnhanceResponse correctAndDetect(File imageFile, Integer enhanceMode) throws Exception {
        return correctAndDetect(imageFile, enhanceMode, 0, null); // 默认不切边，无自定义区域
    }

    /**
     * 文档矫正和角点检测（向后兼容方法）
     *
     * @param imageFile 图片文件
     * @param enhanceMode 增强模式，默认-1（禁用增强）
     * @param cropImage 是否执行切边，默认1（切边）
     * @return 矫正结果
     * @throws Exception 调用异常
     */
    public CropEnhanceResponse correctAndDetect(File imageFile, Integer enhanceMode, Integer cropImage) throws Exception {
        return correctAndDetect(imageFile, enhanceMode, cropImage, null); // 无自定义区域
    }

    public CropEnhanceResponse correctAndDetect(File imageFile, Integer enhanceMode, Integer cropImage, String sizeAndPosition) throws Exception {
        LogKit.info("开始调用TextIn切边矫正API");
        
        // 检查配置
        if (TEXTIN_API_KEY.isEmpty() || TEXTIN_APP_ID.isEmpty()) {
            throw new IllegalStateException("TextIn API配置不完整，请检查textin.api.key和textin.app.id配置");
        }
        
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        
        try {
            // 创建HTTP客户端
            httpClient = createHttpClient();
            
            // 构建请求URL和参数
            StringBuilder urlBuilder = new StringBuilder(CROP_ENHANCE_URL);
            urlBuilder.append("?enhance_mode=").append(enhanceMode != null ? enhanceMode : -1)
                     .append("&crop_image=").append(cropImage != null ? cropImage : 1)        // 是否执行切边操作
                     .append("&dewarp_image=1")      // 执行矫正操作
                     .append("&only_position=0")     // 返回角点坐标 + 矫正后图片
                     .append("&correct_direction=0"); // 校正图片方向

            // 如果提供了自定义切边区域参数
            if (sizeAndPosition != null && !sizeAndPosition.trim().isEmpty()) {
                urlBuilder.append("&size_and_positon=").append(sizeAndPosition.trim());
                LogKit.info("使用自定义切边区域: " + sizeAndPosition);
            }

            String urlWithParams = urlBuilder.toString();
            
            // 创建POST请求
            HttpPost httpPost = new HttpPost(urlWithParams);
            
            // 设置请求头
            httpPost.setHeader("x-ti-app-id", TEXTIN_APP_ID);
            httpPost.setHeader("x-ti-secret-code", TEXTIN_API_KEY);
            httpPost.setHeader("Content-Type", "application/octet-stream");
            
            // 读取文件内容并设置为请求体
            byte[] fileBytes = Files.readAllBytes(imageFile.toPath());
            ByteArrayEntity entity = new ByteArrayEntity(fileBytes);
            httpPost.setEntity(entity);
            
            LogKit.info("发送切边矫正请求，文件大小: " + fileBytes.length + " bytes");
            
            // 执行请求
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            
            LogKit.info("TextIn切边矫正API响应状态码: " + statusCode);
            
            if (statusCode != 200) {
                throw new RuntimeException("TextIn切边矫正API调用失败，状态码: " + statusCode);
            }
            
            // 读取响应内容
            HttpEntity responseEntity = response.getEntity();
            String responseContent = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
            
            LogKit.info("TextIn切边矫正API响应内容长度: " + responseContent.length());
            LogKit.debug("TextIn切边矫正API响应内容: " + responseContent);
            
            // 解析JSON响应
            CropEnhanceResponse cropResponse = parseResponse(responseContent);
            
            LogKit.info("TextIn切边矫正API调用成功");
            return cropResponse;
            
        } finally {
            // 关闭资源
            if (response != null) {
                try {
                    response.close();
                } catch (Exception e) {
                    LogKit.warn("关闭HTTP响应失败: " + e.getMessage());
                }
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (Exception e) {
                    LogKit.warn("关闭HTTP客户端失败: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 创建HTTP客户端
     */
    private CloseableHttpClient createHttpClient() {
        RequestConfig config = RequestConfig.custom()
            .setConnectTimeout(CONNECT_TIMEOUT)
            .setSocketTimeout(SOCKET_TIMEOUT)
            .build();
            
        return HttpClients.custom()
            .setDefaultRequestConfig(config)
            .build();
    }
    
    /**
     * 解析TextIn API响应
     */
    private CropEnhanceResponse parseResponse(String responseContent) throws Exception {
        try {
            CropEnhanceResponse response = JSON.parseObject(responseContent, CropEnhanceResponse.class);
            
            // 检查响应状态
            if (response.getCode() != null && response.getCode() != 200) {
                throw new RuntimeException("TextIn切边矫正API返回错误: " + response.getMessage());
            }
            
            return response;
            
        } catch (Exception e) {
            LogKit.error("解析TextIn切边矫正API响应失败: " + e.getMessage(), e);
            throw new Exception("解析TextIn切边矫正API响应失败: " + e.getMessage(), e);
        }
    }
}
