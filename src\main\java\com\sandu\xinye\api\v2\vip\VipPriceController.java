package com.sandu.xinye.api.v2.vip;

import com.jfinal.core.ActionKey;
import com.jfinal.core.Controller;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.service.VipPriceConfigService;



/**
 * VIP价格配置控制器
 * 路由前缀：/api/v2/vip/price
 */
public class VipPriceController extends Controller {

    /**
     * GET /api/v2/vip/price/configs
     */
    public void configs() {
        renderJson(VipPriceConfigService.me.getPriceConfigs());
    }

    /**
     * GET /api/v2/vip/price/{plan}
     */
    public void price() {
        try {
            String plan = getPara(0);
            Integer userId = getParaToInt("userId");

            if (StrKit.isBlank(plan)) { renderJson(RetKit.fail("套餐类型不能为空")); return; }

            int originalPrice = VipPriceConfigService.me.getPlanPrice(plan);
            int actualPrice   = VipPriceConfigService.me.calculateActualPrice(plan, userId);
            int discount      = Math.max(0, originalPrice - actualPrice);

            renderJson(RetKit.ok("查询成功")
                .set("plan", plan)
                .set("originalPrice", originalPrice)
                .set("actualPrice", actualPrice)
                .set("discountAmount", discount)
                .set("hasDiscount", discount > 0));
        } catch (Exception e) {
            renderJson(RetKit.fail("获取套餐价格失败: " + e.getMessage()));
        }
    }


}

