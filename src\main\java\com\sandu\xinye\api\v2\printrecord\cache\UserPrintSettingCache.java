package com.sandu.xinye.api.v2.printrecord.cache;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 用户打印设置缓存管理器
 * 职责：管理用户打印设置的缓存，提高查询性能
 * 
 * <AUTHOR> Team
 */
public class UserPrintSettingCache {
    
    private final ConcurrentHashMap<Integer, CacheEntry> cache = new ConcurrentHashMap<>();
    private static final long CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存
    
    private static class CacheEntry {
        final boolean enabled;
        final long timestamp;
        
        CacheEntry(boolean enabled) {
            this.enabled = enabled;
            this.timestamp = System.currentTimeMillis();
        }
        
        boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_TTL;
        }
    }
    
    /**
     * 获取缓存的设置
     * @param userId 用户ID
     * @return 缓存的设置，如果不存在或已过期返回null
     */
    public Boolean getCachedSetting(Integer userId) {
        if (userId == null) {
            return null;
        }
        
        CacheEntry entry = cache.get(userId);
        if (entry != null && !entry.isExpired()) {
            return entry.enabled;
        }
        
        // 清理过期缓存
        if (entry != null) {
            cache.remove(userId);
        }
        
        return null;
    }
    
    /**
     * 更新缓存
     * @param userId 用户ID
     * @param enabled 是否开启
     */
    public void updateCache(Integer userId, boolean enabled) {
        if (userId != null) {
            cache.put(userId, new CacheEntry(enabled));
        }
    }
    
    /**
     * 清除指定用户的缓存
     * @param userId 用户ID
     */
    public void evictUser(Integer userId) {
        if (userId != null) {
            cache.remove(userId);
        }
    }
    
    /**
     * 清除所有缓存
     */
    public void evictAll() {
        cache.clear();
    }
    
    /**
     * 获取缓存大小
     * @return 缓存条目数量
     */
    public int size() {
        return cache.size();
    }
}
