package com.sandu.xinye.common.service;

import com.jfinal.kit.JsonKit;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.ehcache.CacheKit;
import com.jfinal.plugin.redis.Cache;
import com.jfinal.plugin.redis.Redis;
import com.sandu.xinye.common.config.RedisConfig;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.VipOrder;
import com.sandu.xinye.common.model.VipSubscription;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * VIP缓存服务
 * 实现多级缓存策略：本地缓存 -> EhCache -> Redis -> 数据库
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class VipCacheService {
    
    public static final VipCacheService me = new VipCacheService();
    
    // 缓存名称
    private static final String CACHE_NAME = "vip";
    
    // 缓存键前缀
    private static final String USER_VIP_PREFIX = "user:vip:";
    private static final String ORDER_PREFIX = "order:";
    private static final String SUBSCRIPTION_PREFIX = "subscription:";
    private static final String PRICE_PREFIX = "price:";
    private static final String CONFIG_PREFIX = "config:";
    
    // 缓存时间（秒）
    private static final int SHORT_CACHE_TIME = 60;        // 1分钟
    private static final int MEDIUM_CACHE_TIME = 300;      // 5分钟
    private static final int LONG_CACHE_TIME = 1800;       // 30分钟
    private static final int VERY_LONG_CACHE_TIME = 3600;  // 1小时
    
    // 本地缓存（一级缓存）
    private final ConcurrentHashMap<String, CacheEntry> localCache = new ConcurrentHashMap<>();
    
    // 缓存统计
    private final CacheStats stats = new CacheStats();
    
    /**
     * 获取用户VIP信息（多级缓存）
     */
    public Map<String, Object> getUserVipInfo(Integer userId) {
        if (userId == null) {
            return null;
        }
        
        String key = USER_VIP_PREFIX + userId;
        
        // 1. 本地缓存
        CacheEntry local = getFromLocalCache(key);
        if (local != null) {
            stats.recordHit("local");
            return (Map<String, Object>) local.value;
        }
        
        // 2. EhCache
        Map<String, Object> ehcache = CacheKit.get(CACHE_NAME, key);
        if (ehcache != null) {
            stats.recordHit("ehcache");
            putToLocalCache(key, ehcache, SHORT_CACHE_TIME);
            return ehcache;
        }
        
        // 3. Redis
        if (RedisConfig.isAvailable()) {
            String redisValue = getFromRedis(key);
            if (StrKit.notBlank(redisValue)) {
                Map<String, Object> value = JsonKit.parse(redisValue, Map.class);
                stats.recordHit("redis");
                
                // 回填缓存
                CacheKit.put(CACHE_NAME, key, value);
                putToLocalCache(key, value, SHORT_CACHE_TIME);
                return value;
            }
        }
        
        // 4. 数据库
        stats.recordMiss();
        Map<String, Object> vipInfo = loadUserVipInfo(userId);
        
        if (vipInfo != null) {
            // 写入多级缓存
            putToAllCache(key, vipInfo, MEDIUM_CACHE_TIME);
        }
        
        return vipInfo;
    }
    
    /**
     * 获取订单信息（带缓存）
     */
    public VipOrder getOrder(String orderNo) {
        if (StrKit.isBlank(orderNo)) {
            return null;
        }
        
        String key = ORDER_PREFIX + orderNo;
        
        // 先查缓存
        VipOrder cached = CacheKit.get(CACHE_NAME, key);
        if (cached != null) {
            stats.recordHit("ehcache");
            return cached;
        }
        
        // 查询数据库
        stats.recordMiss();
        VipOrder order = VipOrder.dao.findFirst(
            "SELECT * FROM vip_order WHERE order_no = ?", orderNo
        );
        
        if (order != null) {
            // 已支付的订单缓存时间长一些
            int cacheTime = VipOrder.STATUS_PAID.equals(order.getStatus()) ? 
                           VERY_LONG_CACHE_TIME : MEDIUM_CACHE_TIME;
            CacheKit.put(CACHE_NAME, key, order);
            
            if (RedisConfig.isAvailable()) {
                putToRedis(key, JsonKit.toJson(order.toRecord().getColumns()), cacheTime);
            }
        }
        
        return order;
    }
    
    /**
     * 获取用户订阅信息（带缓存）
     */
    public VipSubscription getUserSubscription(Integer userId) {
        if (userId == null) {
            return null;
        }
        
        String key = SUBSCRIPTION_PREFIX + userId;
        
        // 查询缓存
        VipSubscription cached = CacheKit.get(CACHE_NAME, key);
        if (cached != null) {
            stats.recordHit("ehcache");
            return cached;
        }
        
        // 查询数据库
        stats.recordMiss();
        VipSubscription subscription = VipSubscription.dao.findFirst(
            "SELECT * FROM vip_subscription WHERE user_id = ? AND status = 'active'",
            userId
        );
        
        if (subscription != null) {
            CacheKit.put(CACHE_NAME, key, subscription);
            
            if (RedisConfig.isAvailable()) {
                putToRedis(key, JsonKit.toJson(subscription.toRecord().getColumns()), MEDIUM_CACHE_TIME);
            }
        }
        
        return subscription;
    }
    
    /**
     * 获取套餐价格（长缓存）
     */
    public Integer getPlanPrice(String plan) {
        String key = PRICE_PREFIX + plan;
        
        // 价格信息缓存时间较长
        Integer price = CacheKit.get(CACHE_NAME, key);
        if (price != null) {
            stats.recordHit("ehcache");
            return price;
        }
        
        // 从配置或数据库加载
        stats.recordMiss();
        price = VipPriceConfigService.me.getPlanPrice(plan);
        
        if (price != null) {
            CacheKit.put(CACHE_NAME, key, price);
            
            if (RedisConfig.isAvailable()) {
                putToRedis(key, String.valueOf(price), VERY_LONG_CACHE_TIME);
            }
        }
        
        return price;
    }
    
    /**
     * 清除用户相关缓存
     */
    public void clearUserCache(Integer userId) {
        if (userId == null) {
            return;
        }
        
        LogKit.info("清除用户VIP缓存: " + userId);
        
        // 清除各级缓存
        String[] keys = {
            USER_VIP_PREFIX + userId,
            SUBSCRIPTION_PREFIX + userId
        };
        
        for (String key : keys) {
            // 本地缓存
            localCache.remove(key);
            
            // EhCache
            CacheKit.remove(CACHE_NAME, key);
            
            // Redis
            if (RedisConfig.isAvailable()) {
                removeFromRedis(key);
            }
        }
    }
    
    /**
     * 清除订单缓存
     */
    public void clearOrderCache(String orderNo) {
        if (StrKit.isBlank(orderNo)) {
            return;
        }
        
        String key = ORDER_PREFIX + orderNo;
        
        localCache.remove(key);
        CacheKit.remove(CACHE_NAME, key);
        
        if (RedisConfig.isAvailable()) {
            removeFromRedis(key);
        }
    }
    
    /**
     * 预热缓存
     */
    public void warmupCache() {
        LogKit.info("开始预热VIP缓存");
        
        try {
            // 预热价格配置
            String[] plans = {"monthly", "yearly"};
            for (String plan : plans) {
                getPlanPrice(plan);
            }
            
            // 预热活跃VIP用户（可选）
            // List<User> vipUsers = User.dao.find(
            //     "SELECT * FROM user WHERE user_type IN ('vip_monthly', 'vip_yearly') " +
            //     "AND vip_expire_time > NOW() LIMIT 100"
            // );
            
            LogKit.info("VIP缓存预热完成");
            
        } catch (Exception e) {
            LogKit.error("缓存预热失败", e);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        return stats.toMap();
    }
    
    /**
     * 清理过期的本地缓存
     */
    public void cleanupLocalCache() {
        long now = System.currentTimeMillis();
        int removed = 0;
        
        for (Map.Entry<String, CacheEntry> entry : localCache.entrySet()) {
            if (entry.getValue().isExpired(now)) {
                localCache.remove(entry.getKey());
                removed++;
            }
        }
        
        if (removed > 0) {
            LogKit.debug("清理过期本地缓存: " + removed + "个");
        }
    }
    
    // ========== 私有辅助方法 ==========
    
    /**
     * 从本地缓存获取
     */
    private CacheEntry getFromLocalCache(String key) {
        CacheEntry entry = localCache.get(key);
        if (entry != null && !entry.isExpired()) {
            return entry;
        }
        if (entry != null) {
            localCache.remove(key);
        }
        return null;
    }
    
    /**
     * 放入本地缓存
     */
    private void putToLocalCache(String key, Object value, int seconds) {
        localCache.put(key, new CacheEntry(value, seconds));
    }
    
    /**
     * 从Redis获取
     */
    private String getFromRedis(String key) {
        try {
            Cache cache = Redis.use();
            return cache.get(CACHE_NAME + ":" + key);
        } catch (Exception e) {
            LogKit.warn("Redis获取失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 放入Redis
     */
    private void putToRedis(String key, String value, int seconds) {
        try {
            Cache cache = Redis.use();
            cache.setex(CACHE_NAME + ":" + key, seconds, value);
        } catch (Exception e) {
            LogKit.warn("Redis写入失败: " + e.getMessage());
        }
    }
    
    /**
     * 从Redis删除
     */
    private void removeFromRedis(String key) {
        try {
            Cache cache = Redis.use();
            cache.del(CACHE_NAME + ":" + key);
        } catch (Exception e) {
            LogKit.warn("Redis删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 写入所有缓存层
     */
    private void putToAllCache(String key, Object value, int seconds) {
        // 本地缓存
        putToLocalCache(key, value, Math.min(seconds, SHORT_CACHE_TIME));
        
        // EhCache
        CacheKit.put(CACHE_NAME, key, value);
        
        // Redis
        if (RedisConfig.isAvailable()) {
            putToRedis(key, JsonKit.toJson(value), seconds);
        }
    }
    
    /**
     * 从数据库加载用户VIP信息
     */
    private Map<String, Object> loadUserVipInfo(Integer userId) {
        try {
            User user = User.dao.findById(userId);
            if (user == null) {
                return null;
            }
            
            Map<String, Object> info = new HashMap<>();
            info.put("userId", userId);
            info.put("userType", user.getUserType());
            info.put("vipExpireTime", user.getVipExpireTime());
            info.put("isVip", user.isVip());
            
            // 查询订阅信息
            VipSubscription subscription = getUserSubscription(userId);
            if (subscription != null) {
                info.put("autoRenew", subscription.getAutoRenew());
                info.put("nextRenewTime", subscription.getNextRenewTime());
            }
            
            return info;
            
        } catch (Exception e) {
            LogKit.error("加载用户VIP信息失败", e);
            return null;
        }
    }
    
    /**
     * 缓存条目
     */
    private static class CacheEntry {
        final Object value;
        final long expireTime;
        
        CacheEntry(Object value, int seconds) {
            this.value = value;
            this.expireTime = System.currentTimeMillis() + seconds * 1000L;
        }
        
        boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
        
        boolean isExpired(long now) {
            return now > expireTime;
        }
    }
    
    /**
     * 缓存统计
     */
    private static class CacheStats {
        private long totalRequests = 0;
        private long localHits = 0;
        private long ehcacheHits = 0;
        private long redisHits = 0;
        private long misses = 0;
        
        synchronized void recordHit(String level) {
            totalRequests++;
            switch (level) {
                case "local":
                    localHits++;
                    break;
                case "ehcache":
                    ehcacheHits++;
                    break;
                case "redis":
                    redisHits++;
                    break;
            }
        }
        
        synchronized void recordMiss() {
            totalRequests++;
            misses++;
        }
        
        Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("totalRequests", totalRequests);
            map.put("localHits", localHits);
            map.put("ehcacheHits", ehcacheHits);
            map.put("redisHits", redisHits);
            map.put("misses", misses);
            
            if (totalRequests > 0) {
                double hitRate = (totalRequests - misses) * 100.0 / totalRequests;
                map.put("hitRate", String.format("%.2f%%", hitRate));
            }
            
            return map;
        }
    }
}