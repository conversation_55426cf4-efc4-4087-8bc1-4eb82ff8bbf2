package com.sandu.xinye.common.task;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.plugin.activerecord.Db;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.service.VipAutoRenewService;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * VIP自动续费定时任务
 * 定期扫描需要续费的订阅并执行自动扣款
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class VipAutoRenewTask implements Runnable {
    
    // 任务运行标志，防止重复执行
    private static final AtomicBoolean isRunning = new AtomicBoolean(false);
    
    // 默认批处理大小
    private static final int DEFAULT_BATCH_SIZE = 50;
    
    // 统计信息
    private static long totalExecutions = 0;
    private static long totalSuccess = 0;
    private static long totalFailed = 0;
    private static Date lastExecutionTime = null;
    
    @Override
    public void run() {
        // 检查是否已在运行
        if (!isRunning.compareAndSet(false, true)) {
            LogKit.warn("VIP自动续费任务正在执行中，跳过本次调度");
            return;
        }
        
        try {
            execute();
        } finally {
            isRunning.set(false);
        }
    }
    
    /**
     * 执行自动续费任务
     */
    public void execute() {
        long startTime = System.currentTimeMillis();
        String taskId = generateTaskId();
        
        LogKit.info("========== VIP自动续费任务开始 [" + taskId + "] ==========");
        
        try {
            // 检查是否启用自动续费
            if (!isAutoRenewEnabled()) {
                LogKit.info("VIP自动续费功能未启用，任务跳过");
                return;
            }
            
            // 获取配置
            int batchSize = PropKit.getInt("vip.autorenew.batch.size", DEFAULT_BATCH_SIZE);
            int maxRetries = PropKit.getInt("vip.autorenew.max.retries", 3);
            
            LogKit.info("开始扫描需要续费的订阅，批处理大小: " + batchSize);
            
            // 执行续费
            int totalProcessed = 0;
            int successCount = 0;
            int failCount = 0;
            
            // 多批次处理，避免一次处理过多
            for (int batch = 0; batch < maxRetries; batch++) {
                int processed = VipAutoRenewService.me.scanAndRenew(batchSize);
                
                if (processed == 0) {
                    LogKit.info("没有更多需要续费的订阅");
                    break;
                }
                
                totalProcessed += processed;
                successCount += processed;
                
                LogKit.info(String.format("第%d批次处理完成，本批成功: %d", batch + 1, processed));
                
                // 批次间短暂休眠，避免压力过大
                if (batch < maxRetries - 1 && processed == batchSize) {
                    Thread.sleep(1000);
                }
            }
            
            // 更新统计信息
            totalExecutions++;
            totalSuccess += successCount;
            totalFailed += failCount;
            lastExecutionTime = new Date();
            
            // 记录执行结果
            long costTime = System.currentTimeMillis() - startTime;
            LogKit.info(String.format(
                "VIP自动续费任务完成 [%s] - 总处理: %d, 成功: %d, 失败: %d, 耗时: %dms",
                taskId, totalProcessed, successCount, failCount, costTime
            ));
            
            // 记录到数据库（可选）
            recordTaskExecution(taskId, totalProcessed, successCount, failCount, costTime);
            
        } catch (Exception e) {
            LogKit.error("VIP自动续费任务执行异常 [" + taskId + "]", e);
            recordTaskError(taskId, e);
            
        } finally {
            LogKit.info("========== VIP自动续费任务结束 [" + taskId + "] ==========");
        }
    }
    
    /**
     * 手动触发执行（用于测试或管理员操作）
     */
    public static RetKit triggerManually(String operator) {
        if (isRunning.get()) {
            return RetKit.fail("任务正在执行中，请稍后再试");
        }
        
        LogKit.info("管理员手动触发VIP自动续费任务，操作人: " + operator);
        
        VipAutoRenewTask task = new VipAutoRenewTask();
        
        // 异步执行
        new Thread(() -> {
            task.execute();
        }, "VipAutoRenew-Manual").start();
        
        return RetKit.ok("任务已触发，请查看日志了解执行情况");
    }
    
    /**
     * 获取任务状态信息
     */
    public static Map<String, Object> getTaskStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("isRunning", isRunning.get());
        status.put("totalExecutions", totalExecutions);
        status.put("totalSuccess", totalSuccess);
        status.put("totalFailed", totalFailed);
        status.put("lastExecutionTime", lastExecutionTime);
        status.put("enabled", isAutoRenewEnabled());
        
        if (totalExecutions > 0) {
            status.put("successRate", String.format("%.2f%%", 
                (double) totalSuccess / (totalSuccess + totalFailed) * 100));
        }
        
        return status;
    }
    
    /**
     * 检查是否启用自动续费
     */
    private static boolean isAutoRenewEnabled() {
        return PropKit.getBoolean("vip.autorenew.enabled", false);
    }
    
    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(new Date()) + "_" + (int)(Math.random() * 1000);
    }
    
    /**
     * 记录任务执行结果
     */
    private void recordTaskExecution(String taskId, int processed, int success, int failed, long costTime) {
        try {
            // 可以记录到数据库的任务执行历史表
            String sql = "INSERT INTO vip_task_history (task_id, task_type, processed_count, " +
                        "success_count, failed_count, cost_time, execute_time) VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            Db.update(sql, taskId, "AUTO_RENEW", processed, success, failed, costTime, new Date());
            
        } catch (Exception e) {
            LogKit.warn("记录任务执行结果失败", e);
        }
    }
    
    /**
     * 记录任务异常
     */
    private void recordTaskError(String taskId, Exception error) {
        try {
            String errorMsg = error.getMessage();
            if (errorMsg != null && errorMsg.length() > 500) {
                errorMsg = errorMsg.substring(0, 500);
            }
            
            String sql = "INSERT INTO vip_task_error (task_id, task_type, error_message, error_time) " +
                        "VALUES (?, ?, ?, ?)";
            
            Db.update(sql, taskId, "AUTO_RENEW", errorMsg, new Date());
            
        } catch (Exception e) {
            LogKit.warn("记录任务异常失败", e);
        }
    }
}