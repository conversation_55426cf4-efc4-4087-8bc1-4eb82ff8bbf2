package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * VIP业务监控指标服务
 * 收集和统计VIP相关的业务指标
 */
public class VipMetricsService {
    
    public static final VipMetricsService me = new VipMetricsService();
    
    // 内存计数器
    private final AtomicLong orderCreateCount = new AtomicLong(0);
    private final AtomicLong orderCreateSuccessCount = new AtomicLong(0);
    private final AtomicLong orderCreateFailCount = new AtomicLong(0);
    
    private final AtomicLong paymentCallbackCount = new AtomicLong(0);
    private final AtomicLong paymentCallbackSuccessCount = new AtomicLong(0);
    private final AtomicLong paymentCallbackFailCount = new AtomicLong(0);
    
    private final AtomicLong signatureVerifyCount = new AtomicLong(0);
    private final AtomicLong signatureVerifyFailCount = new AtomicLong(0);
    
    private final AtomicLong vipDeliveryCount = new AtomicLong(0);
    private final AtomicLong vipDeliverySuccessCount = new AtomicLong(0);
    private final AtomicLong vipDeliveryFailCount = new AtomicLong(0);
    
    private final AtomicLong vipExpireProcessCount = new AtomicLong(0);
    
    // 响应时间统计（简化实现，实际可以使用更复杂的统计）
    private final Map<String, AtomicLong> responseTimes = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> responseTimeCounts = new ConcurrentHashMap<>();
    
    /**
     * 记录订单创建指标
     */
    public void recordOrderCreate(boolean success, long responseTimeMs) {
        orderCreateCount.incrementAndGet();
        
        if (success) {
            orderCreateSuccessCount.incrementAndGet();
        } else {
            orderCreateFailCount.incrementAndGet();
        }
        
        recordResponseTime("order_create", responseTimeMs);
        
        LogKit.debug("订单创建指标: 总数=" + orderCreateCount.get() + 
                    ", 成功=" + orderCreateSuccessCount.get() + 
                    ", 失败=" + orderCreateFailCount.get() + 
                    ", 响应时间=" + responseTimeMs + "ms");
    }
    
    /**
     * 记录支付回调指标
     */
    public void recordPaymentCallback(boolean success, long responseTimeMs) {
        paymentCallbackCount.incrementAndGet();
        
        if (success) {
            paymentCallbackSuccessCount.incrementAndGet();
        } else {
            paymentCallbackFailCount.incrementAndGet();
        }
        
        recordResponseTime("payment_callback", responseTimeMs);
        
        LogKit.debug("支付回调指标: 总数=" + paymentCallbackCount.get() + 
                    ", 成功=" + paymentCallbackSuccessCount.get() + 
                    ", 失败=" + paymentCallbackFailCount.get() + 
                    ", 响应时间=" + responseTimeMs + "ms");
    }
    
    /**
     * 记录签名验证指标
     */
    public void recordSignatureVerify(boolean success) {
        signatureVerifyCount.incrementAndGet();
        
        if (!success) {
            signatureVerifyFailCount.incrementAndGet();
        }
        
        LogKit.debug("签名验证指标: 总数=" + signatureVerifyCount.get() + 
                    ", 失败=" + signatureVerifyFailCount.get());
    }
    
    /**
     * 记录VIP发货指标
     */
    public void recordVipDelivery(boolean success, long responseTimeMs) {
        vipDeliveryCount.incrementAndGet();
        
        if (success) {
            vipDeliverySuccessCount.incrementAndGet();
        } else {
            vipDeliveryFailCount.incrementAndGet();
        }
        
        recordResponseTime("vip_delivery", responseTimeMs);
        
        LogKit.debug("VIP发货指标: 总数=" + vipDeliveryCount.get() + 
                    ", 成功=" + vipDeliverySuccessCount.get() + 
                    ", 失败=" + vipDeliveryFailCount.get() + 
                    ", 响应时间=" + responseTimeMs + "ms");
    }
    
    /**
     * 记录VIP过期处理指标
     */
    public void recordVipExpireProcess(int processedCount) {
        vipExpireProcessCount.addAndGet(processedCount);
        
        LogKit.debug("VIP过期处理指标: 处理数量=" + processedCount + 
                    ", 累计=" + vipExpireProcessCount.get());
    }
    
    /**
     * 记录响应时间
     */
    private void recordResponseTime(String operation, long responseTimeMs) {
        responseTimes.computeIfAbsent(operation, k -> new AtomicLong(0)).addAndGet(responseTimeMs);
        responseTimeCounts.computeIfAbsent(operation, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    /**
     * 获取所有指标
     */
    public Map<String, Object> getAllMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        // 订单指标
        metrics.put("order_create_total", orderCreateCount.get());
        metrics.put("order_create_success", orderCreateSuccessCount.get());
        metrics.put("order_create_fail", orderCreateFailCount.get());
        metrics.put("order_create_success_rate", calculateSuccessRate(orderCreateSuccessCount.get(), orderCreateCount.get()));
        
        // 支付回调指标
        metrics.put("payment_callback_total", paymentCallbackCount.get());
        metrics.put("payment_callback_success", paymentCallbackSuccessCount.get());
        metrics.put("payment_callback_fail", paymentCallbackFailCount.get());
        metrics.put("payment_callback_success_rate", calculateSuccessRate(paymentCallbackSuccessCount.get(), paymentCallbackCount.get()));
        
        // 签名验证指标
        metrics.put("signature_verify_total", signatureVerifyCount.get());
        metrics.put("signature_verify_fail", signatureVerifyFailCount.get());
        metrics.put("signature_verify_success_rate", calculateSuccessRate(signatureVerifyCount.get() - signatureVerifyFailCount.get(), signatureVerifyCount.get()));
        
        // VIP发货指标
        metrics.put("vip_delivery_total", vipDeliveryCount.get());
        metrics.put("vip_delivery_success", vipDeliverySuccessCount.get());
        metrics.put("vip_delivery_fail", vipDeliveryFailCount.get());
        metrics.put("vip_delivery_success_rate", calculateSuccessRate(vipDeliverySuccessCount.get(), vipDeliveryCount.get()));
        
        // VIP过期处理指标
        metrics.put("vip_expire_process_total", vipExpireProcessCount.get());
        
        // 响应时间指标
        for (Map.Entry<String, AtomicLong> entry : responseTimes.entrySet()) {
            String operation = entry.getKey();
            long totalTime = entry.getValue().get();
            long count = responseTimeCounts.get(operation).get();
            
            if (count > 0) {
                metrics.put(operation + "_avg_response_time", totalTime / count);
                metrics.put(operation + "_total_response_time", totalTime);
                metrics.put(operation + "_response_count", count);
            }
        }
        
        return metrics;
    }
    
    /**
     * 获取数据库统计指标
     */
    public Map<String, Object> getDatabaseMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        try {
            // VIP用户统计
            Record vipStats = Db.queryFirst("SELECT " +
                "COUNT(CASE WHEN userTier = 'VIP_MONTHLY' THEN 1 END) as monthly_vip_count, " +
                "COUNT(CASE WHEN userTier = 'VIP_YEARLY' THEN 1 END) as yearly_vip_count, " +
                "COUNT(CASE WHEN userTier = 'FREE' THEN 1 END) as free_user_count, " +
                "COUNT(*) as total_user_count " +
                "FROM user");
            
            if (vipStats != null) {
                metrics.put("monthly_vip_users", vipStats.getInt("monthly_vip_count"));
                metrics.put("yearly_vip_users", vipStats.getInt("yearly_vip_count"));
                metrics.put("free_users", vipStats.getInt("free_user_count"));
                metrics.put("total_users", vipStats.getInt("total_user_count"));
            }
            
            // 订单统计
            Record orderStats = Db.queryFirst("SELECT " +
                "COUNT(CASE WHEN status = 'created' THEN 1 END) as unpaid_orders, " +
                "COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_orders, " +
                "COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_orders, " +
                "COUNT(*) as total_orders, " +
                "SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as total_revenue " +
                "FROM vip_order");
            
            if (orderStats != null) {
                metrics.put("unpaid_orders", orderStats.getInt("unpaid_orders"));
                metrics.put("paid_orders", orderStats.getInt("paid_orders"));
                metrics.put("closed_orders", orderStats.getInt("closed_orders"));
                metrics.put("total_orders", orderStats.getInt("total_orders"));
                metrics.put("total_revenue_cents", orderStats.getLong("total_revenue"));
                metrics.put("total_revenue_yuan", orderStats.getLong("total_revenue") / 100.0);
            }
            
            // 今日统计
            Record todayStats = Db.queryFirst("SELECT " +
                "COUNT(CASE WHEN status = 'paid' AND DATE(create_time) = CURDATE() THEN 1 END) as today_paid_orders, " +
                "SUM(CASE WHEN status = 'paid' AND DATE(create_time) = CURDATE() THEN amount ELSE 0 END) as today_revenue " +
                "FROM vip_order");
            
            if (todayStats != null) {
                metrics.put("today_paid_orders", todayStats.getInt("today_paid_orders"));
                metrics.put("today_revenue_cents", todayStats.getLong("today_revenue"));
                metrics.put("today_revenue_yuan", todayStats.getLong("today_revenue") / 100.0);
            }
            
        } catch (Exception e) {
            LogKit.error("获取数据库指标失败", e);
        }
        
        return metrics;
    }
    
    /**
     * 计算成功率
     */
    private double calculateSuccessRate(long successCount, long totalCount) {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100.0;
    }
    
    /**
     * 重置所有计数器
     */
    public void resetCounters() {
        orderCreateCount.set(0);
        orderCreateSuccessCount.set(0);
        orderCreateFailCount.set(0);
        
        paymentCallbackCount.set(0);
        paymentCallbackSuccessCount.set(0);
        paymentCallbackFailCount.set(0);
        
        signatureVerifyCount.set(0);
        signatureVerifyFailCount.set(0);
        
        vipDeliveryCount.set(0);
        vipDeliverySuccessCount.set(0);
        vipDeliveryFailCount.set(0);
        
        vipExpireProcessCount.set(0);
        
        responseTimes.clear();
        responseTimeCounts.clear();
        
        LogKit.info("VIP监控指标计数器已重置");
    }
    
    /**
     * 输出指标摘要日志
     */
    public void logMetricsSummary() {
        Map<String, Object> metrics = getAllMetrics();
        
        LogKit.info("=== VIP业务指标摘要 ===");
        LogKit.info("订单创建: 总数=" + metrics.get("order_create_total") + 
                   ", 成功率=" + String.format("%.2f%%", metrics.get("order_create_success_rate")));
        LogKit.info("支付回调: 总数=" + metrics.get("payment_callback_total") + 
                   ", 成功率=" + String.format("%.2f%%", metrics.get("payment_callback_success_rate")));
        LogKit.info("VIP发货: 总数=" + metrics.get("vip_delivery_total") + 
                   ", 成功率=" + String.format("%.2f%%", metrics.get("vip_delivery_success_rate")));
        LogKit.info("签名验证: 总数=" + metrics.get("signature_verify_total") + 
                   ", 成功率=" + String.format("%.2f%%", metrics.get("signature_verify_success_rate")));
        LogKit.info("========================");
    }
}
