package com.sandu.xinye.common.itask;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.cron4j.ITask;

/**
 * VIP订单清理定时任务
 * 定期清理过期的未支付订单
 * 每小时执行一次
 */
public class VipOrderCleanupTask implements ITask {
    
    @Override
    public void run() {
        LogKit.info("——————————————————VIP订单清理定时任务开始—————————————————————");
        
        try {
            LogKit.info("开始执行VIP订单清理任务");
            
            // 清理过期的未支付订单（创建时间超过30分钟）
            int affectedRows = cleanupExpiredOrders();
            
            LogKit.info("VIP订单清理任务执行成功，清理了 " + affectedRows + " 个过期订单");
            
        } catch (Exception e) {
            LogKit.error("VIP订单清理任务执行失败: " + e.getMessage(), e);
        }
        
        LogKit.info("——————————————————VIP订单清理定时任务结束—————————————————————");
    }
    
    @Override
    public void stop() {
        LogKit.info("VIP订单清理定时任务已停止");
    }
    
    /**
     * 清理过期的未支付订单
     * @return 清理的订单数量
     */
    private int cleanupExpiredOrders() {
        try {
            // 将创建时间超过30分钟的未支付订单标记为已关闭
            String sql = "UPDATE vip_order SET status = 'closed', update_time = NOW() " +
                        "WHERE status = 'created' " +
                        "AND create_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE)";
            
            int affectedRows = Db.update(sql);
            
            if (affectedRows > 0) {
                LogKit.info("清理了 " + affectedRows + " 个过期的未支付订单");
                
                // 记录清理日志（如果有清理日志表的话）
                recordCleanupLog(affectedRows, "auto_expire");
            }
            
            return affectedRows;
            
        } catch (Exception e) {
            LogKit.error("清理过期订单失败", e);
            return 0;
        }
    }
    
    /**
     * 记录清理日志
     */
    private void recordCleanupLog(int affectedOrders, String cleanupType) {
        try {
            // 检查清理日志表是否存在
            boolean tableExists = Db.queryFirst("SHOW TABLES LIKE 'vip_order_cleanup_log'") != null;
            
            if (tableExists) {
                String sql = "INSERT INTO vip_order_cleanup_log (cleanup_time, affected_orders, cleanup_type) " +
                           "VALUES (NOW(), ?, ?)";
                Db.update(sql, affectedOrders, cleanupType);
                LogKit.debug("清理日志已记录");
            }
            
        } catch (Exception e) {
            LogKit.warn("记录清理日志失败", e);
        }
    }
    
    /**
     * 手动清理过期订单（静态方法，供手动调用）
     */
    public static int manualCleanupExpiredOrders() {
        try {
            LogKit.info("手动执行VIP订单清理任务");
            
            VipOrderCleanupTask task = new VipOrderCleanupTask();
            int affectedRows = task.cleanupExpiredOrders();
            
            LogKit.info("手动VIP订单清理任务执行完成，清理了 " + affectedRows + " 个过期订单");
            return affectedRows;
            
        } catch (Exception e) {
            LogKit.error("手动VIP订单清理任务执行失败", e);
            return 0;
        }
    }
}
