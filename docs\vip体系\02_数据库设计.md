# 数据库设计（v1）

## 1. user 表扩展
- 新增字段：vipExpireTime DATETIME NULL COMMENT 'VIP到期时间'
- 已有字段：userTier VARCHAR(20) - FREE/VIP_MONTHLY/VIP_YEARLY

变更 SQL（MySQL）：
```sql
ALTER TABLE `user`
  ADD COLUMN `vipExpireTime` DATETIME NULL COMMENT 'VIP到期时间' AFTER `userTier`,
  ADD INDEX `idx_user_vip_expire`(`vipExpireTime`);
```

## 2. vip_order 表
```sql
CREATE TABLE `vip_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单号',
  `user_id` INT NOT NULL,
  `plan` VARCHAR(20) NOT NULL COMMENT '套餐: monthly/yearly',
  `tier` VARCHAR(20) NOT NULL COMMENT '用户等级: VIP_MONTHLY/VIP_YEARLY',
  `amount` INT NOT NULL COMMENT '金额(分)',
  `channel` VARCHAR(20) NOT NULL COMMENT '支付渠道: wechat',
  `status` VARCHAR(20) NOT NULL COMMENT '状态: created/paid/closed/failed',
  `extra` JSON NULL COMMENT '渠道返回的扩展字段',
  `create_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP订单';

-- 审计增强字段（建议）
ALTER TABLE `vip_order`
  ADD COLUMN `channel_trade_no` VARCHAR(64) NULL COMMENT '微信transaction_id' AFTER `status`,
  ADD COLUMN `paid_time` DATETIME NULL COMMENT '支付成功时间' AFTER `channel_trade_no`,
  ADD COLUMN `notify_time` DATETIME NULL COMMENT '回调到达时间' AFTER `paid_time`;
CREATE INDEX `idx_vip_order_paid_time` ON `vip_order` (`paid_time`);
CREATE INDEX `idx_vip_order_channel_trade_no` ON `vip_order` (`channel_trade_no`);

```

## 3. vip_delivery_log 表（可选）
```sql
CREATE TABLE `vip_delivery_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `order_no` VARCHAR(64) NOT NULL,
  `user_id` INT NOT NULL,
  `action` VARCHAR(32) NOT NULL COMMENT 'open/extend',
  `from_tier` VARCHAR(20) NOT NULL,
  `to_tier` VARCHAR(20) NOT NULL,
  `from_expire` DATETIME NULL,
  `to_expire` DATETIME NULL,
  `create_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP开通/续期日志';
```

## 4. vip_feature_rule（接口-权限映射，可选但推荐）
```sql
CREATE TABLE `vip_feature_rule` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `method` VARCHAR(10) NOT NULL COMMENT 'HTTP 方法，如 GET/POST',
  `pattern` VARCHAR(255) NOT NULL COMMENT '路径模式，支持前缀匹配或通配符，如 /api/v2/ocr/recognize',
  `permission` VARCHAR(64) NOT NULL COMMENT 'PermissionType 代码，如 ocr_create',
  `enabled` TINYINT(1) NOT NULL DEFAULT 1,
  `create_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_method_pattern` (`method`, `pattern`(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP 功能拦截规则';
```

---

## 新增数据表（修复后实现）

### 5. VIP退款表 (vip_refund)
```sql
CREATE TABLE `vip_refund` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `refund_no` VARCHAR(32) NOT NULL COMMENT '退款单号',
  `order_no` VARCHAR(32) NOT NULL COMMENT '原订单号',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `refund_amount` INT NOT NULL COMMENT '退款金额(分)',
  `reason` VARCHAR(50) NOT NULL COMMENT '退款原因',
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending',
  `create_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refund_no` (`refund_no`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`)
);
```

### 6. VIP价格配置表 (vip_price_config)
```sql
CREATE TABLE `vip_price_config` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `plan` VARCHAR(20) NOT NULL COMMENT '套餐类型',
  `price` INT NOT NULL COMMENT '当前价格(分)',
  `original_price` INT NOT NULL COMMENT '原价(分)',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1,
  `create_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_plan` (`plan`)
);
```

### 7. VIP促销活动表 (vip_promotion)
```sql
CREATE TABLE `vip_promotion` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `promotion_id` VARCHAR(32) NOT NULL,
  `name` VARCHAR(100) NOT NULL COMMENT '促销活动名称',
  `plan` VARCHAR(20) NOT NULL COMMENT '适用套餐类型',
  `discount_type` VARCHAR(20) NOT NULL COMMENT '折扣类型',
  `discount_value` INT NOT NULL COMMENT '折扣值',
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NOT NULL,
  `is_active` TINYINT(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_promotion_id` (`promotion_id`)
);
```

## 表结构增强

### vip_order表新增字段
```sql
ALTER TABLE `vip_order`
ADD COLUMN `promotion_id` VARCHAR(32) NULL COMMENT '使用的促销活动ID',
ADD COLUMN `original_amount` INT NULL COMMENT '原价金额(分)',
ADD COLUMN `discount_amount` INT NULL COMMENT '折扣金额(分)',
ADD COLUMN `refund_status` VARCHAR(20) NULL COMMENT '退款状态',
ADD COLUMN `refund_amount` INT NULL COMMENT '已退款金额(分)',
ADD COLUMN `refund_time` DATETIME NULL COMMENT '退款时间';
```

## 总结

数据库设计现已支持：
- ✅ VIP 订单管理
- ✅ 微信支付集成（含证书管理）
- ✅ 用户权限控制
- ✅ 支付通知日志
- ✅ 系统配置管理
- ✅ **退款管理** - 完整的退款流程和状态跟踪
- ✅ **价格配置** - 动态价格和促销活动管理
- ✅ **监控日志** - 业务操作和系统状态记录
- ✅ **幂等性保护** - 防重复操作的数据约束

所有表都包含了必要的索引、触发器和存储过程以确保数据一致性和查询性能。


