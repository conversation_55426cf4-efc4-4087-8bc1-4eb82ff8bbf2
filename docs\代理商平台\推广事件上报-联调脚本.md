# 推广事件上报 - 联调脚本

说明：业务已改为由系统在业务流程中自动触发上报，以下接口仅用于测试/补上报。

## Postman 测试集合

### 1. 注册绑定事件 (actionType=1)

#### 1.1 通过业务接口触发（推荐）
```http
POST {{baseUrl}}/api/v2/user/bindPromotionCode
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "promotionCode": "DU2UBA"
}
```

#### 1.2 兼容旧调试接口
```http
POST {{baseUrl}}/api/v2/promotion/report
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "actionType": 1,
  "promotionCode": "DU2UBA"
}
```


### 2. 首次有效打印事件 (actionType=2)

#### 2.1 已绑定用户首次上报
```http
POST {{baseUrl}}/api/v2/promotion/report
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "actionType": 2
}
```

#### 2.2 重复上报（应返回已上报）
```http
POST {{baseUrl}}/api/v2/promotion/report
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "actionType": 2
}
```

#### 2.3 未绑定用户上报（应失败）
```http
POST {{baseUrl}}/api/v2/promotion/report
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "actionType": 2
}
```

### 3. VIP订阅事件 (actionType=3)

说明：支付订阅功能尚未开发。待支付成功回调开发完成后，在回调中由系统触发上报。

#### 3.1 兼容旧调试接口（临时）
```http
POST {{baseUrl}}/api/v2/promotion/report
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "actionType": 3
}
```

#### 3.2 多次订阅（续费，应允许）
```http
POST {{baseUrl}}/api/v2/promotion/report
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "actionType": 3
}
```

### 4. 参数校验测试

#### 4.1 无效的 actionType
```http
POST {{baseUrl}}/api/v2/promotion/report
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "actionType": 4
}
```

#### 4.2 缺少 actionType
```http
POST {{baseUrl}}/api/v2/promotion/report
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{}
```

#### 4.3 未登录用户
```http
POST {{baseUrl}}/api/v2/promotion/report
Content-Type: application/json

{
  "actionType": 1,
  "promotionCode": "DU2UBA"
}
```

## HTTPie 脚本

### 环境变量设置
```bash
export BASE_URL="http://localhost:8090"
export ACCESS_TOKEN="your_access_token_here"
```

### 1. 注册绑定事件
```bash
# 首次绑定
http POST $BASE_URL/api/v2/promotion/report \
  Authorization:"Bearer $ACCESS_TOKEN" \
  actionType:=1 \
  promotionCode="DU2UBA"

# 已绑定用户重复上报
http POST $BASE_URL/api/v2/promotion/report \
  Authorization:"Bearer $ACCESS_TOKEN" \
  actionType:=1
```

### 2. 激活事件（满足近30天5个打印日）
```bash
# 通过业务接口多次产生打印（示例为模板打印）
http POST $BASE_URL/api/v2/printRecord/saveTemplate \
  Authorization:"Bearer $ACCESS_TOKEN" \
  userId:=123 templateId:=1 templateName="示例模板" \
  printWidth:=40 printHeight:=30 printCopies:=1 printPlatform:=1 templateData='{}'

# 达标后系统自动上报 actionType=2，无需手动调用 report 接口
```

### 3. VIP订阅事件
```bash
# 首次订阅
http POST $BASE_URL/api/v2/promotion/report \
  Authorization:"Bearer $ACCESS_TOKEN" \
  actionType:=3

# 续费订阅
http POST $BASE_URL/api/v2/promotion/report \
  Authorization:"Bearer $ACCESS_TOKEN" \
  actionType:=3
```

### 4. 错误场景测试
```bash
# 无效 actionType
http POST $BASE_URL/api/v2/promotion/report \
  Authorization:"Bearer $ACCESS_TOKEN" \
  actionType:=4

# 未登录
http POST $BASE_URL/api/v2/promotion/report \
  actionType:=1 \
  promotionCode="DU2UBA"
```

## 预期响应

### 成功响应
```json
{
  "success": true
}
```

### 失败响应
```json
{
  "success": false,
  "message": "错误描述"
}
```

## 测试流程建议

1. **准备测试用户**
   - 创建一个测试用户并获取 accessToken
   - 确保用户初始状态未绑定推广码

2. **按顺序测试**
   - 先测试 actionType=1 绑定流程
   - 再测试 actionType=2 首次打印
   - 最后测试 actionType=3 VIP订阅

3. **验证数据库状态**
   - 检查 user 表的 promotion_code 和 promotion_bind_time
   - 检查 promotion_event_report 表的记录

4. **验证第三方调用**
   - 查看应用日志确认第三方调用
   - 验证签名计算是否正确

## 数据库验证 SQL

### 查看用户绑定状态
```sql
SELECT userId, userNickName, promotion_code, promotion_bind_time 
FROM user 
WHERE userId = ?;
```

### 查看上报记录
```sql
SELECT * FROM promotion_event_report 
WHERE user_id = ? 
ORDER BY created_at DESC;
```

### 统计各类事件上报数量
```sql
SELECT
  action_type,
  COUNT(*) as total_reports,
  SUM(success) as success_reports
FROM promotion_event_report
GROUP BY action_type;
```

## 统计接口测试

### 获取统计信息
```http
GET {{baseUrl}}/api/v2/promotion-stats/stats
Authorization: Bearer {{accessToken}}
```

### 重置运行时统计
```http
POST {{baseUrl}}/api/v2/promotion-stats/reset-stats
Authorization: Bearer {{accessToken}}
```

### 输出统计到日志
```http
POST {{baseUrl}}/api/v2/promotion-stats/log-stats
Authorization: Bearer {{accessToken}}
```

### HTTPie 统计接口
```bash
# 获取统计
http GET $BASE_URL/api/v2/promotion-stats/stats \
  Authorization:"Bearer $ACCESS_TOKEN"

# 重置统计
http POST $BASE_URL/api/v2/promotion-stats/reset-stats \
  Authorization:"Bearer $ACCESS_TOKEN"

# 输出日志
http POST $BASE_URL/api/v2/promotion-stats/log-stats \
  Authorization:"Bearer $ACCESS_TOKEN"
```
