package com.sandu.xinye.api.v2.ocr.dto;

import java.util.List;
import java.util.Map;

/**
 * TextIn切边矫正API响应DTO
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public class CropEnhanceResponse {
    
    private Integer code;
    private String message;
    private String version;
    private Long duration;
    private Result result;
    
    public static class Result {
        private Integer originWidth;
        private Integer originHeight;
        private List<ImageInfo> imageList;
        
        public Integer getOriginWidth() {
            return originWidth;
        }
        
        public void setOriginWidth(Integer originWidth) {
            this.originWidth = originWidth;
        }
        
        public Integer getOriginHeight() {
            return originHeight;
        }
        
        public void setOriginHeight(Integer originHeight) {
            this.originHeight = originHeight;
        }
        
        public List<ImageInfo> getImageList() {
            return imageList;
        }
        
        public void setImageList(List<ImageInfo> imageList) {
            this.imageList = imageList;
        }
    }
    
    public static class ImageInfo {
        private Integer croppedWidth;
        private Integer croppedHeight;
        private String image;  // base64格式的图片
        private int[] position;  // 8个元素的数组：[x1,y1,x2,y2,x3,y3,x4,y4]
        private Integer angle;
        
        public Integer getCroppedWidth() {
            return croppedWidth;
        }
        
        public void setCroppedWidth(Integer croppedWidth) {
            this.croppedWidth = croppedWidth;
        }
        
        public Integer getCroppedHeight() {
            return croppedHeight;
        }
        
        public void setCroppedHeight(Integer croppedHeight) {
            this.croppedHeight = croppedHeight;
        }
        
        public String getImage() {
            return image;
        }
        
        public void setImage(String image) {
            this.image = image;
        }
        
        public int[] getPosition() {
            return position;
        }
        
        public void setPosition(int[] position) {
            this.position = position;
        }
        
        public Integer getAngle() {
            return angle;
        }
        
        public void setAngle(Integer angle) {
            this.angle = angle;
        }
    }
    
    // Getters and Setters
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public Long getDuration() {
        return duration;
    }
    
    public void setDuration(Long duration) {
        this.duration = duration;
    }
    
    public Result getResult() {
        return result;
    }
    
    public void setResult(Result result) {
        this.result = result;
    }
}
