openapi: 3.0.3
info:
  title: XPrinter 识图新建 API
  description: |
    XPrinter 识图新建标签功能 API 文档
    
    ## 功能概述
    识图新建标签功能允许用户通过拍照上传标签/模板纸图片，后端调用TextIn API进行图像识别，将识别结果转换为APP端可用的JSON格式数据。
    
    ## 支持的元素类型
    - **文本元素** (elementType: "1"): 识别文字内容、位置、字体样式
    - **条形码** (elementType: "2"): 识别一维码内容和类型
    - **二维码** (elementType: "7"): 识别二维码内容
    - **图片元素** (elementType: "8"): 识别图片、logo、图标、印章等图形内容
    - **表格** (elementType: "10"): 识别表格结构和单元格内容
    
    ## 技术架构
    ```
    用户拍照 → APP上传 → 后端接收 → TextIn识别 → 数据转换 → 返回结果 → APP渲染
    ```
  version: 1.0.0
  contact:
    name: XPrinter Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8090
    description: 本地开发环境
  - url: https://api.xprinter.com
    description: 生产环境

security:
  - BearerAuth: []

paths:
  /api/v2/ocr/recognize:
    post:
      tags:
        - OCR识图
      summary: 图片识别接口
      description: |
        上传图片进行OCR识别，返回识别到的文本、条码、二维码、图片和表格等元素信息。
        
        ### 处理流程
        1. 验证图片文件格式和大小
        2. 调用TextIn API进行图像识别
        3. 转换识别结果为XPrinter格式
        4. 返回结构化的元素数据
        
        ### 性能指标
        - 响应时间: 通常5-15秒（取决于图片大小和复杂度）
        - 文件大小限制: 最大10MB
        - 并发限制: 建议不超过10个并发请求
      operationId: recognizeImage
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
                  description: |
                    图片文件，支持的格式：
                    - jpg/jpeg
                    - png
                    - webp
                    - bmp
                    
                    要求：
                    - 最大文件大小: 10MB
                    - 图片质量: 清晰、光线充足、无严重变形
                imageWidth:
                  type: integer
                  description: 图片宽度(px)，可选参数，不传则从文件中自动获取
                  example: 1080
                  minimum: 1
                  maximum: 10000
                imageHeight:
                  type: integer
                  description: 图片高度(px)，可选参数，不传则从文件中自动获取
                  example: 1920
                  minimum: 1
                  maximum: 10000
            encoding:
              file:
                contentType: image/jpeg, image/png, image/webp, image/bmp
      responses:
        '200':
          description: 识别成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OcrSuccessResponse'
              examples:
                comprehensive_example:
                  summary: 综合识别结果示例
                  description: 包含文本、条码、二维码、图片和表格的完整识别结果
                  value:
                    success: true
                    data:
                      imageInfo:
                        width: 1080
                        height: 1920
                        format: "jpeg"
                      elements:
                        - elementType: "1"
                          x: 100
                          y: 200
                          width: 300
                          height: 50
                          content: "产品标题"
                          charWidth: 25.0
                          bold: true
                          italic: false
                          rotationAngle: 0.0
                          textDirection: "horizontal"
                          confidence: 0.99
                          isHandwritten: false
                        - elementType: "2"
                          x: 50
                          y: 300
                          width: 400
                          height: 80
                          content: "6975370866829"
                          barcodeType: "CODE_128"
                        - elementType: "7"
                          x: 200
                          y: 400
                          width: 100
                          height: 100
                          content: "https://example.com"
                        - elementType: "8"
                          x: 450
                          y: 50
                          width: 80
                          height: 60
                          imageUrl: "https://web-api.textin.com/ocr_image/external/logo_123.png"
                          imageType: "logo"
                          confidence: 0.92
                          isEmbedded: true
                    message: "识别成功"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                no_file:
                  summary: 未上传文件
                  value:
                    success: false
                    msg: "请上传图片文件"
                file_too_large:
                  summary: 文件过大
                  value:
                    success: false
                    msg: "图片文件过大，最大支持10MB"
                invalid_format:
                  summary: 格式不支持
                  value:
                    success: false
                    msg: "不支持的图片格式，请使用jpg、png、webp或bmp格式"
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                msg: "用户未登录或token已过期"
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                textin_api_error:
                  summary: TextIn API调用失败
                  value:
                    success: false
                    msg: "TextIn API调用失败，请稍后重试"
                processing_error:
                  summary: 图片处理异常
                  value:
                    success: false
                    msg: "图片识别失败: 无法读取图片文件，请检查图片格式"

  /api/v2/ocr/health:
    get:
      tags:
        - OCR识图
      summary: 健康检查接口
      description: 检查OCR服务是否正常运行
      operationId: healthCheck
      responses:
        '200':
          description: 服务正常
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              example:
                success: true
                msg: "OCR服务运行正常"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        用户认证Token，通过登录接口获取。
        
        使用方式：
        ```
        Authorization: Bearer <your_token>
        ```

  schemas:
    OcrSuccessResponse:
      type: object
      required:
        - success
        - data
        - message
      properties:
        success:
          type: boolean
          example: true
          description: 请求是否成功
        data:
          $ref: '#/components/schemas/OcrData'
        message:
          type: string
          example: "识别成功"
          description: 响应消息

    OcrData:
      type: object
      required:
        - imageInfo
        - elements
      properties:
        imageInfo:
          $ref: '#/components/schemas/ImageInfo'
        elements:
          type: array
          items:
            $ref: '#/components/schemas/Element'
          description: 识别到的元素列表

    ImageInfo:
      type: object
      required:
        - width
        - height
        - format
      properties:
        width:
          type: integer
          example: 1080
          description: 图片宽度(px)
        height:
          type: integer
          example: 1920
          description: 图片高度(px)
        format:
          type: string
          example: "jpeg"
          enum: ["jpeg", "png", "webp", "bmp"]
          description: 图片格式

    Element:
      type: object
      required:
        - elementType
        - x
        - y
        - width
        - height
      properties:
        elementType:
          type: string
          enum: ["1", "2", "7", "8", "10"]
          description: |
            元素类型：
            - "1": 文本元素
            - "2": 条形码
            - "7": 二维码
            - "8": 图片元素
            - "10": 表格
        x:
          type: integer
          description: 元素左上角X坐标(px)
          example: 100
        y:
          type: integer
          description: 元素左上角Y坐标(px)
          example: 200
        width:
          type: integer
          description: 元素宽度(px)
          example: 300
        height:
          type: integer
          description: 元素高度(px)
          example: 50
      discriminator:
        propertyName: elementType
        mapping:
          "1": '#/components/schemas/TextElement'
          "2": '#/components/schemas/BarcodeElement'
          "7": '#/components/schemas/QRCodeElement'
          "8": '#/components/schemas/ImageElement'
          "10": '#/components/schemas/TableElement'

    TextElement:
      allOf:
        - $ref: '#/components/schemas/Element'
        - type: object
          required:
            - content
          properties:
            content:
              type: string
              description: 识别到的文本内容
              example: "产品标题"
            charWidth:
              type: number
              format: float
              description: 智能计算的平均字符宽度(px)
              example: 25.0
            bold:
              type: boolean
              description: 是否粗体（基于TextIn的sub_type和outline_level智能判断）
              example: true
            italic:
              type: boolean
              description: 是否斜体（基于char_positions几何分析智能判断）
              example: false
            rotationAngle:
              type: number
              format: float
              description: 旋转角度(度)
              example: 0.0
            textDirection:
              type: string
              enum: ["horizontal", "vertical"]
              description: 文字方向
              example: "horizontal"
            confidence:
              type: number
              format: float
              minimum: 0
              maximum: 1
              description: 识别置信度(0-1)
              example: 0.99
            isHandwritten:
              type: boolean
              description: 是否手写体
              example: false

    BarcodeElement:
      allOf:
        - $ref: '#/components/schemas/Element'
        - type: object
          required:
            - content
            - barcodeType
          properties:
            content:
              type: string
              description: 条码内容
              example: "6975370866829"
            barcodeType:
              type: string
              description: 标准条码类型名称
              example: "CODE_128"
              enum: ["CODE_39", "CODE_128", "EAN_13", "EAN_8", "UPC_A", "UPC_E"]

    QRCodeElement:
      allOf:
        - $ref: '#/components/schemas/Element'
        - type: object
          required:
            - content
          properties:
            content:
              type: string
              description: 二维码内容
              example: "https://example.com"

    ImageElement:
      allOf:
        - $ref: '#/components/schemas/Element'
        - type: object
          required:
            - imageUrl
            - imageType
          properties:
            imageUrl:
              type: string
              description: 图片链接或base64数据
              example: "https://web-api.textin.com/ocr_image/external/logo_123.png"
            imageType:
              type: string
              enum: ["logo", "stamp", "chart", "icon", "graphic", "generic"]
              description: 图片子类型
              example: "logo"
            confidence:
              type: number
              format: float
              minimum: 0
              maximum: 1
              description: 识别置信度(0-1)
              example: 0.92
            isEmbedded:
              type: boolean
              description: 是否为嵌入式图片
              example: true

    TableElement:
      allOf:
        - $ref: '#/components/schemas/Element'
        - type: object
          required:
            - rowCount
            - colCount
            - cells
          properties:
            rowCount:
              type: integer
              description: 表格行数
              example: 3
            colCount:
              type: integer
              description: 表格列数
              example: 2
            cells:
              type: array
              items:
                $ref: '#/components/schemas/TableCell'
              description: 表格单元格数组

    TableCell:
      type: object
      required:
        - row
        - col
        - content
      properties:
        row:
          type: integer
          description: 单元格行位置(从0开始)
          example: 0
        col:
          type: integer
          description: 单元格列位置(从0开始)
          example: 0
        content:
          type: string
          description: 单元格内容
          example: "商品名称"
        rowSpan:
          type: integer
          description: 跨行数(可选)
          example: 1
        colSpan:
          type: integer
          description: 跨列数(可选)
          example: 2

    ErrorResponse:
      type: object
      required:
        - success
        - msg
      properties:
        success:
          type: boolean
          example: false
          description: 请求是否成功
        msg:
          type: string
          description: 错误信息
          example: "请上传图片文件"

    HealthResponse:
      type: object
      required:
        - success
        - msg
      properties:
        success:
          type: boolean
          example: true
          description: 服务是否正常
        msg:
          type: string
          example: "OCR服务运行正常"
          description: 健康状态信息

tags:
  - name: OCR识图
    description: |
      图像识别相关接口
      
      ### 功能特性
      - 支持多种图片格式(jpg、png、webp、bmp)
      - 智能识别文本、条码、二维码、图片和表格
      - 提供详细的元素位置和样式信息
      - 基于TextIn API的高精度识别
      
      ### 使用建议
      - 使用清晰、光线充足的图片以获得最佳识别效果
      - 避免图片过度倾斜或变形
      - 复杂表格识别结果可能需要人工校验
      - 建议控制并发请求数量以确保服务稳定性
