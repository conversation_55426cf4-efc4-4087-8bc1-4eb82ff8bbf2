package com.sandu.xinye.common.model;

import com.jfinal.plugin.activerecord.Model;

/**
 * VIP订阅管理模型（简化版）
 * 对应表：vip_subscription
 */
@SuppressWarnings("serial")
public class VipSubscription extends Model<VipSubscription> {
    public static final VipSubscription dao = new VipSubscription().dao();
    
    // 状态常量
    public static final String STATUS_ACTIVE = "active";
    public static final String STATUS_INACTIVE = "inactive";
    
    /**
     * 获取自动续费状态
     */
    public Boolean getAutoRenew() {
        Integer value = getInt("auto_renew");
        return value != null && value == 1;
    }
    
    /**
     * 设置自动续费状态
     */
    public VipSubscription setAutoRenew(Boolean autoRenew) {
        set("auto_renew", autoRenew != null && autoRenew ? 1 : 0);
        return this;
    }
    
    /**
     * 获取下次续费时间
     */
    public java.util.Date getNextRenewTime() {
        return get("next_renew_time");
    }
    
    /**
     * 设置下次续费时间
     */
    public VipSubscription setNextRenewTime(java.util.Date nextRenewTime) {
        set("next_renew_time", nextRenewTime);
        return this;
    }
    
    /**
     * 获取用户ID
     */
    public Integer getUserId() {
        return getInt("user_id");
    }
    
    /**
     * 设置用户ID
     */
    public VipSubscription setUserId(Integer userId) {
        set("user_id", userId);
        return this;
    }
    
    /**
     * 获取状态
     */
    public String getStatus() {
        return getStr("status");
    }
    
    /**
     * 设置状态
     */
    public VipSubscription setStatus(String status) {
        set("status", status);
        return this;
    }
}

