package com.sandu.xinye.common.service;

import org.junit.Test;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;

import static org.junit.Assert.*;

/**
 * 微信支付签名工具测试
 */
public class WechatPaySignatureUtilTest {
    
    @Test
    public void testGenerateNonce() {
        // 测试随机字符串生成
        String nonce1 = WechatPaySignatureUtil.generateNonce();
        String nonce2 = WechatPaySignatureUtil.generateNonce();
        
        // 验证结果
        assertNotNull(nonce1);
        assertNotNull(nonce2);
        assertEquals(32, nonce1.length());
        assertEquals(32, nonce2.length());
        assertNotEquals(nonce1, nonce2); // 应该是不同的随机字符串
        
        // 验证只包含字母和数字
        assertTrue(nonce1.matches("[a-zA-Z0-9]+"));
        assertTrue(nonce2.matches("[a-zA-Z0-9]+"));
    }
    
    @Test
    public void testGetCurrentTimestamp() {
        // 测试时间戳生成
        long timestamp1 = WechatPaySignatureUtil.getCurrentTimestamp();
        
        // 等待一小段时间
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long timestamp2 = WechatPaySignatureUtil.getCurrentTimestamp();
        
        // 验证结果
        assertTrue(timestamp1 > 0);
        assertTrue(timestamp2 > timestamp1);
        assertTrue(timestamp2 - timestamp1 >= 1); // 至少相差1秒
    }
    
    @Test
    public void testBuildSignatureString() {
        // 测试签名串构造
        String method = "POST";
        String url = "/v3/pay/transactions/app";
        long timestamp = 1234567890L;
        String nonce = "test_nonce_123456";
        String body = "{\"appid\":\"test\"}";
        
        String signatureString = WechatPaySignatureUtil.buildSignatureString(method, url, timestamp, nonce, body);
        
        // 验证结果
        assertNotNull(signatureString);
        String expected = "POST\n/v3/pay/transactions/app\n1234567890\ntest_nonce_123456\n{\"appid\":\"test\"}\n";
        assertEquals(expected, signatureString);
    }
    
    @Test
    public void testBuildSignatureString_EmptyBody() {
        // 测试空请求体的签名串构造
        String method = "GET";
        String url = "/v3/certificates";
        long timestamp = 1234567890L;
        String nonce = "test_nonce_123456";
        String body = null;
        
        String signatureString = WechatPaySignatureUtil.buildSignatureString(method, url, timestamp, nonce, body);
        
        // 验证结果
        assertNotNull(signatureString);
        String expected = "GET\n/v3/certificates\n1234567890\ntest_nonce_123456\n\n";
        assertEquals(expected, signatureString);
    }
    
    @Test
    public void testBuildAuthorizationHeader() {
        // 测试Authorization header构造
        String mchId = "1234567890";
        String serialNo = "test_serial_123";
        long timestamp = 1234567890L;
        String nonce = "test_nonce_123456";
        String signature = "test_signature_base64";
        
        String authHeader = WechatPaySignatureUtil.buildAuthorizationHeader(mchId, serialNo, timestamp, nonce, signature);
        
        // 验证结果
        assertNotNull(authHeader);
        assertTrue(authHeader.startsWith("WECHATPAY2-SHA256-RSA2048"));
        assertTrue(authHeader.contains("mchid=\"1234567890\""));
        assertTrue(authHeader.contains("nonce_str=\"test_nonce_123456\""));
        assertTrue(authHeader.contains("timestamp=\"1234567890\""));
        assertTrue(authHeader.contains("serial_no=\"test_serial_123\""));
        assertTrue(authHeader.contains("signature=\"test_signature_base64\""));
    }
    
    @Test
    public void testIsTimestampValid_ValidTimestamp() {
        // 测试有效时间戳
        long currentTime = WechatPaySignatureUtil.getCurrentTimestamp();
        String validTimestamp = String.valueOf(currentTime);
        
        boolean isValid = WechatPaySignatureUtil.isTimestampValid(validTimestamp);
        
        // 验证结果
        assertTrue(isValid);
    }
    
    @Test
    public void testIsTimestampValid_ExpiredTimestamp() {
        // 测试过期时间戳（6分钟前）
        long expiredTime = WechatPaySignatureUtil.getCurrentTimestamp() - 360;
        String expiredTimestamp = String.valueOf(expiredTime);
        
        boolean isValid = WechatPaySignatureUtil.isTimestampValid(expiredTimestamp);
        
        // 验证结果
        assertFalse(isValid);
    }
    
    @Test
    public void testIsTimestampValid_FutureTimestamp() {
        // 测试未来时间戳（6分钟后）
        long futureTime = WechatPaySignatureUtil.getCurrentTimestamp() + 360;
        String futureTimestamp = String.valueOf(futureTime);
        
        boolean isValid = WechatPaySignatureUtil.isTimestampValid(futureTimestamp);
        
        // 验证结果
        assertFalse(isValid);
    }
    
    @Test
    public void testIsTimestampValid_InvalidFormat() {
        // 测试无效格式的时间戳
        String invalidTimestamp = "invalid_timestamp";
        
        boolean isValid = WechatPaySignatureUtil.isTimestampValid(invalidTimestamp);
        
        // 验证结果
        assertFalse(isValid);
    }
    
    @Test
    public void testSign_WithValidKey() throws Exception {
        // 生成测试用的RSA密钥对
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
        keyGen.initialize(2048);
        KeyPair keyPair = keyGen.generateKeyPair();
        PrivateKey privateKey = keyPair.getPrivate();
        
        // 测试签名
        String signatureString = "POST\n/v3/pay/transactions/app\n1234567890\ntest_nonce\n{\"test\":\"data\"}\n";
        
        String signature = WechatPaySignatureUtil.sign(signatureString, privateKey);
        
        // 验证结果
        assertNotNull(signature);
        assertFalse(signature.isEmpty());
        
        // 验证是Base64格式
        try {
            java.util.Base64.getDecoder().decode(signature);
        } catch (IllegalArgumentException e) {
            fail("签名不是有效的Base64格式");
        }
    }
    
    @Test
    public void testSign_WithNullKey() {
        // 测试空私钥
        String signatureString = "test_signature_string";
        
        try {
            WechatPaySignatureUtil.sign(signatureString, null);
            fail("应该抛出异常");
        } catch (RuntimeException e) {
            assertTrue(e.getMessage().contains("签名失败"));
        }
    }
    
    @Test
    public void testVerifyCallback_Integration() throws Exception {
        // 这是一个集成测试，需要真实的证书和签名
        // 由于测试环境限制，这里只做基本的参数验证
        
        String timestamp = String.valueOf(WechatPaySignatureUtil.getCurrentTimestamp());
        String nonce = WechatPaySignatureUtil.generateNonce();
        String body = "{\"test\":\"callback_data\"}";
        String signature = "test_signature";
        
        // 由于没有真实的证书，这里只验证方法不会抛出异常
        try {
            boolean result = WechatPaySignatureUtil.verifyCallback(timestamp, nonce, body, signature, null);
            assertFalse(result); // 预期返回false，因为证书为null
        } catch (Exception e) {
            // 预期会有异常，因为证书为null
            assertNotNull(e);
        }
    }
}
