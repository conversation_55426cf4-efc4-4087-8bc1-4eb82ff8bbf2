package com.sandu.xinye.api.v2.vip;

import com.jfinal.core.Controller;
import com.sandu.xinye.common.service.VipMetricsService;

import java.util.HashMap;
import java.util.Map;

/**
 * VIP监控指标控制器（业务指标）
 * 路由前缀：/api/v2/vip/metrics
 */
public class VipMetricsController extends Controller {

    /**
     * GET /api/v2/vip/metrics
     * 返回业务相关的监控指标（内存计数 + 可选的数据库聚合）
     */
    public void index() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("metrics", VipMetricsService.me.getAllMetrics());
            result.put("timestamp", System.currentTimeMillis());
            result.put("timestamp_readable", new java.util.Date().toString());
            renderJson(result);
        } catch (Exception e) {
            Map<String, Object> err = new HashMap<>();
            err.put("error", "获取监控指标失败");
            err.put("message", e.getMessage());
            err.put("timestamp", System.currentTimeMillis());
            renderJson(err);
        }
    }

    /**
     * GET /api/v2/vip/metrics/memory
     */
    public void memory() {
        try {
            Map<String, Object> res = new HashMap<>();
            res.put("metrics", VipMetricsService.me.getAllMetrics());
            res.put("timestamp", System.currentTimeMillis());
            renderJson(res);
        } catch (Exception e) {
            Map<String, Object> err = new HashMap<>();
            err.put("error", "获取内存指标失败");
            err.put("message", e.getMessage());
            renderJson(err);
        }
    }

    /**
     * GET /api/v2/vip/metrics/database
     */
    public void database() {
        try {
            Map<String, Object> res = new HashMap<>();
            res.put("metrics", VipMetricsService.me.getDatabaseMetrics());
            res.put("timestamp", System.currentTimeMillis());
            renderJson(res);
        } catch (Exception e) {
            Map<String, Object> err = new HashMap<>();
            err.put("error", "获取数据库指标失败");
            err.put("message", e.getMessage());
            renderJson(err);
        }
    }

    /**
     * POST /api/v2/vip/metrics/reset
     */
    public void reset() {
        try {
            VipMetricsService.me.resetCounters();
            Map<String, Object> ok = new HashMap<>();
            ok.put("success", true);
            ok.put("message", "监控指标计数器已重置");
            ok.put("timestamp", System.currentTimeMillis());
            renderJson(ok);
        } catch (Exception e) {
            Map<String, Object> err = new HashMap<>();
            err.put("success", false);
            err.put("error", "重置监控指标失败");
            err.put("message", e.getMessage());
            renderJson(err);
        }
    }

    /**
     * GET /api/v2/vip/metrics/summary
     */
    public void summary() {
        try {
            Map<String, Object> memoryMetrics = VipMetricsService.me.getAllMetrics();
            Map<String, Object> summary = new HashMap<>();
            summary.put("order_create_total", memoryMetrics.get("order_create_total"));
            summary.put("order_create_success_rate", memoryMetrics.get("order_create_success_rate"));
            summary.put("payment_callback_total", memoryMetrics.get("payment_callback_total"));
            summary.put("payment_callback_success_rate", memoryMetrics.get("payment_callback_success_rate"));
            summary.put("signature_verify_success_rate", memoryMetrics.get("signature_verify_success_rate"));
            summary.put("vip_delivery_total", memoryMetrics.get("vip_delivery_total"));
            summary.put("vip_delivery_success_rate", memoryMetrics.get("vip_delivery_success_rate"));
            summary.put("vip_expire_process_total", memoryMetrics.get("vip_expire_process_total"));

            Map<String, Object> res = new HashMap<>();
            res.put("summary", summary);
            res.put("timestamp", System.currentTimeMillis());
            renderJson(res);
        } catch (Exception e) {
            Map<String, Object> err = new HashMap<>();
            err.put("error", "获取指标摘要失败");
            err.put("message", e.getMessage());
            renderJson(err);
        }
    }

    /**
     * POST /api/v2/vip/metrics/log
     */
    public void log() {
        try {
            VipMetricsService.me.logMetricsSummary();
            Map<String, Object> ok = new HashMap<>();
            ok.put("success", true);
            ok.put("message", "指标摘要已输出到日志");
            ok.put("timestamp", System.currentTimeMillis());
            renderJson(ok);
        } catch (Exception e) {
            Map<String, Object> err = new HashMap<>();
            err.put("success", false);
            err.put("error", "输出指标摘要失败");
            err.put("message", e.getMessage());
            renderJson(err);
        }
    }
}

