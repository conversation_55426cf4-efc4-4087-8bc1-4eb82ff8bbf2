package com.sandu.xinye.api.v2.vip;

import com.jfinal.core.Controller;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.service.VipRefundService;

/**
 * VIP退款控制器
 * 路由前缀：/api/v2/vip/refund
 */
public class VipRefundController extends Controller {

    /**
     * POST /api/v2/vip/refund/apply
     */
    public void apply() {
        try {
            String orderNo = getPara("orderNo");
            String reason = getPara("reason");
            String description = getPara("description");

            if (StrKit.isBlank(orderNo)) { renderJson(RetKit.fail("订单号不能为空")); return; }
            if (StrKit.isBlank(reason))  { renderJson(RetKit.fail("退款原因不能为空")); return; }

            renderJson(VipRefundService.me.applyRefund(orderNo, reason, description));
        } catch (Exception e) {
            renderJson(RetKit.fail("申请退款失败: " + e.getMessage()));
        }
    }

    /**
     * GET /api/v2/vip/refund/status/{refundNo}
     */
    public void status() {
        try {
            String refundNo = getPara(0);
            if (StrKit.isBlank(refundNo)) { renderJson(RetKit.fail("退款单号不能为空")); return; }
            renderJson(RetKit.ok("查询成功").set("refundNo", refundNo).set("status", "pending"));
        } catch (Exception e) {
            renderJson(RetKit.fail("查询退款状态失败: " + e.getMessage()));
        }
    }


}

