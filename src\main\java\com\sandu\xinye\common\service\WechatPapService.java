package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.kit.RetKit;

import java.util.Map;

/**
 * 微信委托代扣(PAP)骨架（占位实现，不发起真实请求）
 */
public class WechatPapService {

    public static final WechatPapService me = new WechatPapService();

    /**
     * 发起签约：返回拉起签约所需参数（APP端自行拉起）
     * 约定 attach 结构（JSON 字符串）：
     * { "user_id":<int>, "subscription_type":"monthly|yearly", "ts":<long>, "trace":"..." }
     */
    public RetKit signParams(Integer userId, String subscriptionType) {
        if (userId == null || StrKit.isBlank(subscriptionType)) {
            return RetKit.fail("参数不能为空");
        }
        long ts = System.currentTimeMillis();
        String attachJson = com.jfinal.kit.JsonKit.toJson(new java.util.HashMap<String, Object>() {{
            put("user_id", userId);
            put("subscription_type", subscriptionType);
            put("ts", ts);
            put("trace", "sign-" + ts);
        }});
        // TODO: 真实场景返回微信签约所需字段，当前占位包含 attach 方便回调映射
        java.util.Map<String, Object> params = new java.util.HashMap<>();
        params.put("attach", attachJson);
        params.put("contractDisplayNo", "CONTRACT-MOCK-" + ts);
        params.put("_mock", true);
        return RetKit.ok("签约参数(占位)").set("signParams", params);
    }

    /**
     * 解约：调用微信解约（占位）
     */
    public RetKit terminate(String contractId) {
        if (StrKit.isBlank(contractId)) return RetKit.fail("contractId不能为空");
        // TODO: 调用微信解约接口
        LogKit.info("解约成功(占位): " + contractId);
        return RetKit.ok("解约成功(占位)");
    }

    /**
     * 代扣：以 contractId 扣款（占位）
     */
    public RetKit deduct(String contractId, int amountFen, String outTradeNo, Map<String, Object> attach) {
        if (StrKit.isBlank(contractId)) return RetKit.fail("contractId不能为空");
        // TODO: 调用微信代扣下单
        LogKit.info("代扣成功(占位): contract=" + contractId + ", amount=" + amountFen + ", outTradeNo=" + outTradeNo);
        return RetKit.ok("代扣成功(占位)").set("prepayId", "wx_prepay_mock");
    }

    /**
     * 签约回调验签与解析（占位）
     */
    public Ret handleSignNotify(String body) {
        // TODO: 验签解密
        LogKit.info("收到签约回调(占位): " + body);
        return Ret.ok().set("contractId", "CONTRACT-MOCK");
    }

    /**
     * 解约回调
     */
    public Ret handleTerminateNotify(String body) {
        LogKit.info("收到解约回调(占位): " + body);
        return Ret.ok();
    }

    /**
     * 代扣支付回调
     */
    public Ret handleDeductNotify(String body) {
        LogKit.info("收到代扣支付回调(占位): " + body);
        return Ret.ok().set("tradeState", "SUCCESS");
    }
}

