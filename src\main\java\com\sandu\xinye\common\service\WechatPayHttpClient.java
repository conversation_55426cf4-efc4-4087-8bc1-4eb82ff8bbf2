package com.sandu.xinye.common.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.LogKit;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付HTTP客户端
 * 封装微信支付API v3的HTTP请求逻辑
 */
public class WechatPayHttpClient {
    
    private final WechatPayConfig config;
    private final PrivateKey privateKey;
    
    public WechatPayHttpClient(WechatPayConfig config, PrivateKey privateKey) {
        this.config = config;
        this.privateKey = privateKey;
    }
    
    /**
     * 发送POST请求到微信支付API
     * 
     * @param url 请求URL
     * @param requestBody 请求体JSON字符串
     * @return 响应结果
     */
    public WechatPayResponse post(String url, String requestBody) {
        try {
            LogKit.info("发送微信支付POST请求: " + url);
            LogKit.debug("请求体: " + requestBody);
            
            // 创建HTTP客户端
            HttpClient httpClient = createHttpClient();
            HttpPost httpPost = new HttpPost(url);
            
            // 生成签名参数
            long timestamp = WechatPaySignatureUtil.getCurrentTimestamp();
            String nonce = WechatPaySignatureUtil.generateNonce();
            String urlPath = extractUrlPath(url);
            
            // 构造签名串并签名
            String signatureString = WechatPaySignatureUtil.buildSignatureString(
                "POST", urlPath, timestamp, nonce, requestBody);
            String signature = WechatPaySignatureUtil.sign(signatureString, privateKey);
            
            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("User-Agent", "XPrinter-VIP/1.0");
            httpPost.setHeader("Authorization", WechatPaySignatureUtil.buildAuthorizationHeader(
                config.getMchId(), config.getSerialNo(), timestamp, nonce, signature));
            
            // 设置请求体
            if (requestBody != null && !requestBody.isEmpty()) {
                httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));
            }
            
            // 执行请求
            HttpResponse response = httpClient.execute(httpPost);
            
            // 解析响应
            return parseResponse(response);
            
        } catch (Exception e) {
            LogKit.error("微信支付POST请求失败: " + url, e);
            return WechatPayResponse.error("HTTP请求失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送GET请求到微信支付API
     * 
     * @param url 请求URL
     * @return 响应结果
     */
    public WechatPayResponse get(String url) {
        try {
            LogKit.info("发送微信支付GET请求: " + url);
            
            // 创建HTTP客户端
            HttpClient httpClient = createHttpClient();
            HttpGet httpGet = new HttpGet(url);
            
            // 生成签名参数
            long timestamp = WechatPaySignatureUtil.getCurrentTimestamp();
            String nonce = WechatPaySignatureUtil.generateNonce();
            String urlPath = extractUrlPath(url);
            
            // 构造签名串并签名（GET请求body为空字符串）
            String signatureString = WechatPaySignatureUtil.buildSignatureString(
                "GET", urlPath, timestamp, nonce, "");
            String signature = WechatPaySignatureUtil.sign(signatureString, privateKey);
            
            // 设置请求头
            httpGet.setHeader("Accept", "application/json");
            httpGet.setHeader("User-Agent", "XPrinter-VIP/1.0");
            httpGet.setHeader("Authorization", WechatPaySignatureUtil.buildAuthorizationHeader(
                config.getMchId(), config.getSerialNo(), timestamp, nonce, signature));
            
            // 执行请求
            HttpResponse response = httpClient.execute(httpGet);
            
            // 解析响应
            return parseResponse(response);
            
        } catch (Exception e) {
            LogKit.error("微信支付GET请求失败: " + url, e);
            return WechatPayResponse.error("HTTP请求失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建HTTP客户端（支持HTTPS）
     */
    private HttpClient createHttpClient() {
        HttpClient httpClient = new DefaultHttpClient();
        
        // 配置SSL（信任所有证书，生产环境建议使用正确的证书验证）
        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{new X509TrustManager() {
                public X509Certificate[] getAcceptedIssuers() { return null; }
                public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                public void checkServerTrusted(X509Certificate[] certs, String authType) {}
            }}, null);
            
        } catch (Exception e) {
            LogKit.warn("SSL配置失败，使用默认配置", e);
        }
        
        return httpClient;
    }
    
    /**
     * 从完整URL中提取路径部分
     */
    private String extractUrlPath(String url) {
        try {
            // 移除协议和域名，保留路径和查询参数
            int pathStart = url.indexOf('/', 8); // 跳过 "https://"
            return pathStart > 0 ? url.substring(pathStart) : "/";
        } catch (Exception e) {
            LogKit.warn("提取URL路径失败: " + url, e);
            return "/";
        }
    }
    
    /**
     * 解析HTTP响应
     */
    private WechatPayResponse parseResponse(HttpResponse response) throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();
        
        HttpEntity entity = response.getEntity();
        String responseBody = entity != null ? EntityUtils.toString(entity, StandardCharsets.UTF_8) : "";
        
        LogKit.info("微信支付响应状态: " + statusCode);
        LogKit.debug("微信支付响应体: " + responseBody);
        
        // 解析响应头（用于验签）
        Map<String, String> headers = new HashMap<>();
        if (response.getAllHeaders() != null) {
            for (org.apache.http.Header header : response.getAllHeaders()) {
                headers.put(header.getName(), header.getValue());
            }
        }
        
        if (statusCode >= 200 && statusCode < 300) {
            // 成功响应
            JSONObject jsonResponse = null;
            if (!responseBody.isEmpty()) {
                try {
                    jsonResponse = JSON.parseObject(responseBody);
                } catch (Exception e) {
                    LogKit.warn("解析响应JSON失败: " + responseBody, e);
                }
            }
            return WechatPayResponse.success(jsonResponse, headers);
            
        } else {
            // 错误响应
            JSONObject errorJson = null;
            try {
                errorJson = JSON.parseObject(responseBody);
            } catch (Exception e) {
                LogKit.warn("解析错误响应JSON失败: " + responseBody, e);
            }
            
            String errorMessage = "HTTP " + statusCode;
            if (errorJson != null && errorJson.containsKey("message")) {
                errorMessage += ": " + errorJson.getString("message");
            }
            
            return WechatPayResponse.error(errorMessage, errorJson);
        }
    }
}
