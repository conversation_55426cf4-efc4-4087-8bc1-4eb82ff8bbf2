package com.sandu.xinye.common.job;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;

import java.util.Calendar;
import java.util.Date;

/**
 * 定时清理 user_print_day 表中过期数据，保留最近 365 天
 */
public class UserPrintDay<PERSON>leanupJob implements Runnable {

    private final int retainDays;

    public UserPrintDayCleanupJob() {
        this(365);
    }

    public UserPrintDayCleanupJob(int retainDays) {
        this.retainDays = retainDays;
    }

    @Override
    public void run() {
        try {
            Date threshold = minusDays(new Date(), retainDays);
            int affected = Db.update("DELETE FROM user_print_day WHERE print_date < DATE(?)", threshold);
            LogKit.info("UserPrintDayCleanupJob 清理完成，删除行数: " + affected + ", 保留天数: " + retainDays);
        } catch (Exception e) {
            LogKit.error("UserPrintDayCleanupJob 执行失败: " + e.getMessage(), e);
        }
    }

    private Date minusDays(Date base, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(base);
        cal.add(Calendar.DAY_OF_MONTH, -days);
        return cal.getTime();
    }
}

