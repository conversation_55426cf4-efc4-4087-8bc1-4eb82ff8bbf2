openapi: 3.0.3
info:
  title: 推广事件上报接口
  description: 代理商平台推广事件上报相关接口
  version: 1.0.0
  contact:
    name: 开发团队
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: 本地开发环境
  - url: https://api.example.com
    description: 生产环境

paths:
  /api/v2/promotion/report:
    post:
      tags:
        - 推广事件上报
      summary: 推广事件上报
      description: |
        向代理商平台上报推广事件，支持三种事件类型：
        - 1: 注册绑定事件
        - 2: 首次有效打印事件  
        - 3: VIP订阅事件
      operationId: reportPromotionEvent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - actionType
              properties:
                actionType:
                  type: integer
                  enum: [1, 2, 3]
                  description: |
                    事件类型：
                    - 1: 注册绑定
                    - 2: 首次有效打印
                    - 3: VIP订阅
                  example: 1
                promotionCode:
                  type: string
                  maxLength: 32
                  description: 推广码（actionType=1且用户未绑定时必填）
                  example: "PROMO123"
            examples:
              register_bind:
                summary: 注册绑定事件
                value:
                  actionType: 1
                  promotionCode: "PROMO123"
              first_print:
                summary: 首次有效打印事件
                value:
                  actionType: 2
              vip_subscribe:
                summary: VIP订阅事件
                value:
                  actionType: 3
      responses:
        '200':
          description: 上报成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                success:
                  summary: 成功响应
                  value:
                    code: "200"
                    msg: "操作成功"
                    success: true
        '400':
          description: 参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_action_type:
                  summary: 无效的事件类型
                  value:
                    code: "501"
                    msg: "actionType必须为1、2或3"
                    success: false
        '401':
          description: 用户未登录
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                not_logged_in:
                  summary: 用户未登录
                  value:
                    code: "501"
                    msg: "用户未登录"
                    success: false
        '500':
          description: 系统异常
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                system_error:
                  summary: 系统异常
                  value:
                    code: "501"
                    msg: "系统异常，请稍后重试"
                    success: false
      security:
        - bearerAuth: []

  /api/v2/promotion/stats:
    get:
      tags:
        - 推广事件统计
      summary: 获取推广事件统计信息
      description: 获取推广事件的实时统计信息，包括运行时统计和数据库统计（仅供内部管理使用）
      operationId: getPromotionStats
      responses:
        '200':
          description: 获取统计信息成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: string
                    example: "200"
                  msg:
                    type: string
                    example: "操作成功"
                  success:
                    type: boolean
                    example: true
                  runtime_stats:
                    type: object
                    description: 运行时统计信息
                    additionalProperties: true
                  database_stats:
                    type: object
                    properties:
                      by_type:
                        type: object
                        properties:
                          register_bind:
                            $ref: '#/components/schemas/EventTypeStats'
                          first_print:
                            $ref: '#/components/schemas/EventTypeStats'
                          vip_subscribe:
                            $ref: '#/components/schemas/EventTypeStats'
                      recent_24h:
                        $ref: '#/components/schemas/EventTypeStats'
                      overall:
                        $ref: '#/components/schemas/EventTypeStats'
        '500':
          description: 获取统计信息失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
      security:
        - bearerAuth: []

  /api/v2/promotion/reset-stats:
    post:
      tags:
        - 推广事件统计
      summary: 重置运行时统计计数器
      description: 重置运行时统计计数器（仅供内部管理使用）
      operationId: resetPromotionStats
      responses:
        '200':
          description: 重置成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                success:
                  summary: 重置成功
                  value:
                    code: "200"
                    msg: "统计计数器已重置"
                    success: true
        '500':
          description: 重置失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
      security:
        - bearerAuth: []

  /api/v2/promotion/log-stats:
    post:
      tags:
        - 推广事件统计
      summary: 输出统计信息到日志
      description: 将详细的统计信息输出到系统日志（仅供内部管理使用）
      operationId: logPromotionStats
      responses:
        '200':
          description: 输出成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                success:
                  summary: 输出成功
                  value:
                    code: "200"
                    msg: "统计信息已输出到日志"
                    success: true
        '500':
          description: 输出失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
      security:
        - bearerAuth: []

components:
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: string
          description: 响应状态码，200为成功，501为失败
          example: "200"
        msg:
          type: string
          description: 响应消息
          example: "操作成功"
        success:
          type: boolean
          description: 操作是否成功
          example: true
      required:
        - code
        - msg
        - success

    EventTypeStats:
      type: object
      properties:
        total:
          type: integer
          description: 总数量
          example: 1000
        success:
          type: integer
          description: 成功数量
          example: 950
        failed:
          type: integer
          description: 失败数量
          example: 50
        success_rate:
          type: number
          format: double
          description: 成功率（百分比）
          example: 95.0
      required:
        - total
        - success
        - failed
        - success_rate

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: 用户认证Token

tags:
  - name: 推广事件上报
    description: 推广事件上报相关接口
  - name: 推广事件统计
    description: 推广事件统计相关接口（内部管理）