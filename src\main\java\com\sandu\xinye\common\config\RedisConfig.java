package com.sandu.xinye.common.config;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.plugin.redis.RedisPlugin;
import com.jfinal.plugin.redis.Redis;

/**
 * Redis配置管理类
 * 负责Redis连接的初始化和管理
 * 
 * <AUTHOR>  
 * @date 2024-01-15
 */
public class RedisConfig {
    
    private static RedisPlugin redisPlugin;
    private static boolean initialized = false;
    
    /**
     * 初始化Redis配置
     */
    public static void init() {
        if (initialized) {
            return;
        }
        
        try {
            // 读取配置
            String host = PropKit.get("redis.host", "localhost");
            int port = PropKit.getInt("redis.port", 6379);  // 使用标准端口
            String password = PropKit.get("redis.password", "");
            int database = PropKit.getInt("redis.database", 0);
            int timeout = PropKit.getInt("redis.timeout", 2000);
            
            // 打印配置信息（敏感信息脱敏）
            LogKit.info("Redis配置: host=" + host + 
                       ", port=" + port + 
                       ", database=" + database + 
                       ", timeout=" + timeout + "ms" +
                       ", hasPassword=" + (password != null && !password.trim().isEmpty()));
            
            // 创建Redis插件
            if (password != null && !password.trim().isEmpty()) {
                redisPlugin = new RedisPlugin("main", host, port, timeout, password, database);
            } else {
                redisPlugin = new RedisPlugin("main", host, port, timeout, null, database);
            }
            
            // 启动插件
            redisPlugin.start();
            initialized = true;
            
            LogKit.info("Redis连接成功: " + host + ":" + port);
            
            // 测试连接
            testConnection();
            
        } catch (Exception e) {
            // 打印详细参数信息，便于快速定位环境问题
            LogKit.error("Redis初始化失败，参数信息：" +
                        " host=" + host +
                        ", port=" + port +
                        ", database=" + database +
                        ", timeout=" + timeout +
                        ", hasPassword=" + (password != null && !password.trim().isEmpty()), e);
            initialized = false;
        }
    }
    
    /**
     * 测试Redis连接
     */
    private static void testConnection() {
        try {
            Redis.use().set("test:connection", "ok");
            String value = Redis.use().get("test:connection");
            if ("ok".equals(value)) {
                LogKit.info("Redis连接测试成功");
                Redis.use().del("test:connection");
            }
        } catch (Exception e) {
            LogKit.warn("Redis连接测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查Redis是否可用
     */
    public static boolean isAvailable() {
        if (!initialized) {
            init();
        }
        
        if (!initialized) {
            return false;
        }
        
        try {
            // 简单的ping测试
            Redis.use().set("test:ping", "pong");
            Redis.use().del("test:ping");
            return true;
        } catch (Exception e) {
            LogKit.debug("Redis不可用: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 关闭Redis连接
     */
    public static void stop() {
        shutdown();
    }
    
    /**
     * 关闭Redis连接
     */
    public static void shutdown() {
        if (redisPlugin != null) {
            redisPlugin.stop();
            initialized = false;
            LogKit.info("Redis连接已关闭");
        }
    }
    
    /**
     * 获取Redis插件实例
     */
    public static RedisPlugin getPlugin() {
        if (!initialized) {
            init();
        }
        return redisPlugin;
    }
    
    /**
     * 是否为开发模式
     * 开发模式下某些安全检查可能会被跳过
     */
    public static boolean isDevelopmentMode() {
        String mode = PropKit.get("app.mode", "production");
        return "development".equalsIgnoreCase(mode) || "dev".equalsIgnoreCase(mode);
    }
}