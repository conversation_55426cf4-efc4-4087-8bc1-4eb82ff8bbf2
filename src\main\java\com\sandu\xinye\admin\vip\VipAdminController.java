package com.sandu.xinye.admin.vip;

import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.service.VipRefundService;
import com.sandu.xinye.common.service.VipPriceConfigService;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 管理员 - VIP管理控制器
 * 路由前缀：/admin/vip
 */
public class VipAdminController extends AdminController {

    /**
     * POST /admin/vip/refund/review
     * 审核退款申请
     */
    public void refundReview() {
        try {
            String refundNo = getPara("refundNo");
            Boolean approve = getParaToBoolean("approve");
            String adminRemark = getPara("adminRemark");

            if (StrKit.isBlank(refundNo)) {
                renderJson(RetKit.fail("退款单号不能为空"));
                return;
            }
            if (approve == null) {
                renderJson(RetKit.fail("审核结果不能为空"));
                return;
            }

            RetKit result = VipRefundService.me.processRefund(refundNo, approve, adminRemark);
            renderJson(result);

        } catch (Exception e) {
            renderJson(RetKit.fail("审核退款失败: " + e.getMessage()));
        }
    }

    /**
     * POST /admin/vip/price/update
     * 更新价格配置
     */
    public void priceUpdate() {
        try {
            String plan = getPara("plan");
            Integer price = getParaToInt("price");
            String description = getPara("description");

            RetKit result = VipPriceConfigService.me.updatePriceConfig(plan, price, description);
            renderJson(result);

        } catch (Exception e) {
            renderJson(RetKit.fail("更新价格配置失败: " + e.getMessage()));
        }
    }

    /**
     * POST /admin/vip/price/promotion
     * 创建促销活动
     */
    public void pricePromotion() {
        try {
            String name = getPara("name");
            String plan = getPara("plan");
            String discountType = getPara("discountType");
            Integer discountValue = getParaToInt("discountValue");
            String startTimeStr = getPara("startTime");
            String endTimeStr = getPara("endTime");
            Integer maxUses = getParaToInt("maxUses");
            String description = getPara("description");

            if (StrKit.isBlank(name) || StrKit.isBlank(plan) || StrKit.isBlank(discountType)) {
                renderJson(RetKit.fail("必填参数不能为空"));
                return;
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date startTime = sdf.parse(startTimeStr);
            Date endTime = sdf.parse(endTimeStr);

            RetKit result = VipPriceConfigService.me.createPromotion(
                name, plan, discountType, discountValue, startTime, endTime, maxUses, description);
            renderJson(result);

        } catch (Exception e) {
            renderJson(RetKit.fail("创建促销活动失败: " + e.getMessage()));
        }
    }

    /**
     * GET /admin/vip/refund/list
     * 获取退款列表
     */
    public void refundList() {
        try {
            String status = getPara("status");
            String startDate = getPara("startDate");
            String endDate = getPara("endDate");
            Integer page = getParaToInt("page", 1);
            Integer pageSize = getParaToInt("pageSize", 20);

            // TODO: 实现退款列表查询
            renderJson(RetKit.ok("查询成功")
                .set("page", page)
                .set("pageSize", pageSize)
                .set("total", 0)
                .set("list", new java.util.ArrayList<>()));

        } catch (Exception e) {
            renderJson(RetKit.fail("查询退款列表失败: " + e.getMessage()));
        }
    }

    /**
     * GET /admin/vip/price/configs
     * 获取价格配置列表（管理员版本，含更多信息）
     */
    public void priceConfigs() {
        try {
            RetKit result = VipPriceConfigService.me.getPriceConfigs();
            renderJson(result);

        } catch (Exception e) {
            renderJson(RetKit.fail("获取价格配置失败: " + e.getMessage()));
        }
    }

    /**
     * POST /admin/vip/price/cache/clear
     * 清除价格缓存
     */
    public void priceCacheClear() {
        try {
            VipPriceConfigService.me.clearCache();
            VipPriceConfigService.me.clearPromotionCache();

            renderJson(RetKit.ok("缓存清除成功"));

        } catch (Exception e) {
            renderJson(RetKit.fail("清除缓存失败: " + e.getMessage()));
        }
    }

    /**
     * GET /admin/vip/stats
     * 获取VIP统计数据
     */
    public void stats() {
        try {
            // TODO: 实现VIP统计查询
            java.util.Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("total_orders", 0);
            stats.put("total_revenue", 0.0);
            stats.put("total_vip_users", 0);
            stats.put("monthly_vip_users", 0);
            stats.put("yearly_vip_users", 0);

            renderJson(RetKit.ok("查询成功").set("stats", stats));

        } catch (Exception e) {
            renderJson(RetKit.fail("获取VIP统计失败: " + e.getMessage()));
        }
    }
}
