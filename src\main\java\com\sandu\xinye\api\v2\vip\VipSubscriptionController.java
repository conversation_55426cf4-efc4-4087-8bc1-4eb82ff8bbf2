package com.sandu.xinye.api.v2.vip;

import com.jfinal.aop.Before;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AppUserInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.service.VipSubscriptionService;

/**
 * 订阅管理接口（签约/取消/状态/自动续费开关）
 * 前缀：/api/v2/vip/subscription
 */
public class VipSubscriptionController extends AppController {

    /** POST /api/v2/vip/subscription/sign （到期后停止策略已固化） */
    @Before({AppUserInterceptor.class})
    public void sign() {
        Integer userId = getUser().getUserId();
        String subscriptionType = getPara("subscriptionType"); // monthly | yearly
        renderJson(VipSubscriptionService.me.signContract(userId, subscriptionType));
    }

    /** POST /api/v2/vip/subscription/cancel */
    @Before({AppUserInterceptor.class})
    public void cancel() {
        Integer userId = getUser().getUserId();
        String subscriptionType = getPara("subscriptionType");
        renderJson(VipSubscriptionService.me.cancel(userId, subscriptionType));
    }

    /** POST /api/v2/vip/subscription/disableAuto */
    @Before({AppUserInterceptor.class})
    public void disableAuto() {
        Integer userId = getUser().getUserId();
        String subscriptionType = getPara("subscriptionType");
        renderJson(VipSubscriptionService.me.disableAuto(userId, subscriptionType));
    }

    /** POST /api/v2/vip/subscription/enableAuto */
    @Before({AppUserInterceptor.class})
    public void enableAuto() {
        Integer userId = getUser().getUserId();
        String subscriptionType = getPara("subscriptionType");
        renderJson(VipSubscriptionService.me.enableAuto(userId, subscriptionType));
    }

    /** GET /api/v2/vip/subscription/status?subscriptionType=monthly|yearly */
    @Before({AppUserInterceptor.class})
    public void status() {
        Integer userId = getUser().getUserId();
        String subscriptionType = getPara("subscriptionType");
        renderJson(VipSubscriptionService.me.getStatus(userId, subscriptionType));
    }

    /** GET /api/v2/vip/subscription/statusAll */
    @Before({AppUserInterceptor.class})
    public void statusAll() {
        Integer userId = getUser().getUserId();
        renderJson(VipSubscriptionService.me.getStatusAll(userId));
    }
}

