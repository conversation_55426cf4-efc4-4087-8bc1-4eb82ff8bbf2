package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.enums.UserTier;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.VipOrder;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 订阅自动续费服务（占位：使用 PAP 骨架进行代扣调用）
 */
public class VipAutoRenewService {

    public static final VipAutoRenewService me = new VipAutoRenewService();

    /**
     * 扫描并续费
     * @param batchSize 每批处理数量
     * @return 成功续费数量
     */
    public int scanAndRenew(int batchSize) {
        int success = 0;
        try {
            String sql = "SELECT * FROM vip_subscription WHERE status='active' AND auto_renew=1 " +
                    "AND next_renew_time IS NOT NULL AND next_renew_time <= NOW() ORDER BY next_renew_time ASC LIMIT ?";
            List<Record> subs = Db.find(sql, batchSize);
            if (subs == null || subs.isEmpty()) {
                return 0;
            }
            for (Record sub : subs) {
                try {
                    boolean renewed = renewOne(sub);
                    if (renewed) success++;
                } catch (Exception e) {
                    LogKit.error("续费失败: user=" + sub.getInt("user_id"), e);
                }
            }
        } catch (Exception e) {
            LogKit.error("扫描续费失败", e);
        }
        return success;
    }

    private boolean renewOne(Record sub) {
        Integer userId = sub.getInt("user_id");
        String subscriptionType = sub.getStr("subscription_type"); // monthly|yearly
        String contractId = sub.getStr("contract_id");
        if (userId == null || StrKit.isBlank(subscriptionType) || StrKit.isBlank(contractId)) {
            markFail(sub, "invalid subscription record");
            return false;
        }
        final String plan = "yearly".equals(subscriptionType) ? VipOrder.PLAN_YEARLY : VipOrder.PLAN_MONTHLY;
        final int amount = VipPriceConfigService.me.getPlanPrice(plan, "subscribe");
        final String outTradeNo = generateRenewOrderNo();

        // 调用 PAP 代扣（占位）
        RetKit ret = WechatPapService.me.deduct(contractId, amount, outTradeNo, null);
        if (!ret.success()) {
            markFail(sub, "deduct failed");
            return false;
        }

        // 创建订单并发货
        boolean ok = Db.tx(() -> {
            VipOrder order = new VipOrder();
            Date now = new Date();
            order.setOrderNo(outTradeNo)
                 .setUserId(userId)
                 .setPlan(plan)
                 .setTier("yearly".equals(plan) ? UserTier.VIP_YEARLY.name() : UserTier.VIP_MONTHLY.name())
                 .setAmount(amount)
                 .setChannel(VipOrder.CHANNEL_WECHAT)
                 .setStatus(VipOrder.STATUS_PAID)
                 .setPaidTime(now)
                 .setCreateTime(now)
                 .setUpdateTime(now)
                 .setSubscriptionType(subscriptionType)
                 .setAutoRenew(true)
                 .setContractId(contractId);
            if (!order.save()) {
                LogKit.error("保存续费订单失败: " + outTradeNo);
                return false;
            }

            // 发货
            boolean delivered = VipDeliveryService.me.deliverVip(order);
            if (!delivered) {
                LogKit.error("续费发货失败: order=" + outTradeNo);
                return false;
            }

            // 更新订阅：记录订单号、推进 next_renew_time、清零失败次数
            int months = "yearly".equals(subscriptionType) ? 12 : 1;
            int updated = Db.update(
                "UPDATE vip_subscription SET last_renew_order_no=?, fail_count=0, last_fail_reason=NULL, " +
                "next_renew_time = DATE_ADD(IFNULL(next_renew_time, NOW()), INTERVAL ? MONTH), update_time=NOW() WHERE id=?",
                outTradeNo, months, sub.getLong("id")
            );
            return updated > 0;
        });
        if (!ok) {
            markFail(sub, "tx failed");
        }
        return ok;
    }

    private void markFail(Record sub, String reason) {
        try {
            Db.update("UPDATE vip_subscription SET fail_count = fail_count + 1, last_fail_reason=?, update_time=NOW() WHERE id=?",
                    reason, sub.getLong("id"));
        } catch (Exception e) {
            LogKit.warn("标记续费失败异常", e);
        }
    }

    private String generateRenewOrderNo() {
        int rnd = ThreadLocalRandom.current().nextInt(1000, 9999);
        return "VIPRENEW" + System.currentTimeMillis() + rnd;
    }
}

