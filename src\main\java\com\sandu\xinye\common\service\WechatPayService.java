package com.sandu.xinye.common.service;

import com.jfinal.kit.JsonKit;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.kit.WechatPaySignKit;
import com.sandu.xinye.common.model.VipOrder;

import java.security.interfaces.RSAPrivateKey;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付服务
 * 提供APP场景下单、签名生成等功能
 */
public class WechatPayService {

    public static final WechatPayService me = new WechatPayService();

    private RSAPrivateKey privateKey;
    private WechatPayHttpClient httpClient;
    
    /**
     * 初始化私钥和HTTP客户端
     */
    public void init() {
        try {
            WechatPayConfig config = WechatPayConfig.me;
            if (!config.isConfigValid()) {
                LogKit.warn("微信支付配置不完整，跳过初始化");
                return;
            }

            String privateKeyPath = config.getPrivateKeyPath();
            if (StrKit.notBlank(privateKeyPath)) {
                this.privateKey = WechatPaySignKit.loadPrivateKey(privateKeyPath);
                LogKit.info("微信支付私钥加载成功");

                // 初始化HTTP客户端
                this.httpClient = new WechatPayHttpClient(config, privateKey);
                LogKit.info("微信支付HTTP客户端初始化成功");
            }

        } catch (Exception e) {
            LogKit.error("微信支付服务初始化失败", e);
        }
    }
    
    /**
     * 创建APP支付订单
     */
    public Map<String, Object> createAppOrder(VipOrder vipOrder) {
        try {
            WechatPayConfig config = WechatPayConfig.me;
            
            // 检查配置
            if (!config.isConfigValid() || privateKey == null || httpClient == null) {
                throw new RuntimeException("微信支付配置不完整或服务未初始化");
            }

            // 构建下单请求体
            Map<String, Object> orderRequest = buildAppOrderRequest(vipOrder, config);
            String requestBody = JsonKit.toJson(orderRequest);

            // 发送HTTP请求到微信支付API
            WechatPayResponse response = httpClient.post(config.getAppOrderUrl(), requestBody);

            if (!response.isSuccess()) {
                throw new RuntimeException("微信支付下单失败: " + response.getErrorMessage());
            }

            // 获取prepay_id
            String prepayId = response.getString("prepay_id");
            if (StrKit.isBlank(prepayId)) {
                throw new RuntimeException("微信支付下单失败，未获取到prepay_id");
            }
            
            // 生成APP调起支付参数
            return buildAppPayParams(config.getAppId(), prepayId);
            
        } catch (Exception e) {
            LogKit.error("创建微信支付APP订单失败", e);
            throw new RuntimeException("创建微信支付订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建APP下单请求体
     */
    private Map<String, Object> buildAppOrderRequest(VipOrder vipOrder, WechatPayConfig config) {
        Map<String, Object> request = new HashMap<>();
        request.put("appid", config.getAppId());
        request.put("mchid", config.getMchId());
        request.put("description", getOrderDescription(vipOrder.getPlan()));
        request.put("out_trade_no", vipOrder.getOrderNo());
        request.put("notify_url", config.getNotifyUrl());
        
        // 订单金额
        Map<String, Object> amount = new HashMap<>();
        amount.put("total", vipOrder.getAmount());
        amount.put("currency", "CNY");
        request.put("amount", amount);
        
        return request;
    }
    

    
    /**
     * 构建APP调起支付参数
     */
    private Map<String, Object> buildAppPayParams(String appId, String prepayId) {
        String timestamp = WechatPaySignKit.generateTimestamp();
        String nonce = WechatPaySignKit.generateNonce();
        
        // 构建签名串
        String signStr = appId + "\n" + timestamp + "\n" + nonce + "\n" + prepayId + "\n";
        String paySign = WechatPaySignKit.sign("", "", timestamp, nonce, signStr, privateKey);
        
        Map<String, Object> payParams = new HashMap<>();
        payParams.put("appid", appId);
        payParams.put("partnerid", WechatPayConfig.me.getMchId());
        payParams.put("prepayid", prepayId);
        payParams.put("package", "Sign=WXPay");
        payParams.put("noncestr", nonce);
        payParams.put("timestamp", timestamp);
        payParams.put("sign", paySign);
        
        return payParams;
    }
    
    /**
     * 获取订单描述
     */
    private String getOrderDescription(String plan) {
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                return "XPrinter VIP月度会员";
            case VipOrder.PLAN_YEARLY:
                return "XPrinter VIP年度会员";
            default:
                return "XPrinter VIP会员";
        }
    }
    

    
    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        return WechatPayConfig.me.isConfigValid() && privateKey != null && httpClient != null;
    }
}
