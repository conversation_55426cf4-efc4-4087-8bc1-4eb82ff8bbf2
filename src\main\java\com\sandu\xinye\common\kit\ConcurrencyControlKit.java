package com.sandu.xinye.common.kit;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.redis.Cache;
import com.jfinal.plugin.redis.Redis;
import com.sandu.xinye.common.config.RedisConfig;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 并发控制工具类
 * 提供分布式锁、限流、熔断等并发控制机制
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class ConcurrencyControlKit {
    
    // Redis锁前缀
    private static final String LOCK_PREFIX = "lock:";
    
    // 限流器前缀
    private static final String RATE_LIMIT_PREFIX = "rate:";
    
    // 默认锁超时时间（30秒）
    private static final int DEFAULT_LOCK_TIMEOUT = 30;
    
    // 本地信号量缓存（降级方案）
    private static final ConcurrentHashMap<String, Semaphore> localSemaphores = new ConcurrentHashMap<>();
    
    // 熔断器状态
    private static final ConcurrentHashMap<String, CircuitBreaker> circuitBreakers = new ConcurrentHashMap<>();
    
    /**
     * 获取分布式锁
     * 
     * @param lockKey 锁键
     * @param requestId 请求ID（用于识别锁的持有者）
     * @param expireSeconds 过期时间（秒）
     * @return 是否成功获取锁
     */
    public static boolean tryLock(String lockKey, String requestId, int expireSeconds) {
        if (!RedisConfig.isAvailable()) {
            // Redis不可用，使用本地锁降级
            return tryLocalLock(lockKey, requestId);
        }
        
        try {
            String key = LOCK_PREFIX + lockKey;
            Cache cache = Redis.use();
            
            // JFinal的Redis插件API有限，使用get+setex组合实现类似setnx的效果
            // 注意：这不是完全原子的操作，但在低并发场景下可接受
            String existingValue = cache.get(key);
            
            boolean locked = false;
            if (existingValue == null) {
                // key不存在，尝试设置锁
                cache.setex(key, expireSeconds, requestId);
                // 再次检查确认是自己的锁（减少竞态条件）
                String checkValue = cache.get(key);
                locked = requestId.equals(checkValue);
                
                if (locked) {
                    LogKit.debug("获取分布式锁成功: " + lockKey);
                }
            }
            return locked;
            
        } catch (Exception e) {
            LogKit.error("获取分布式锁异常", e);
            // 异常时降级到本地锁
            return tryLocalLock(lockKey, requestId);
        }
    }
    
    /**
     * 获取分布式锁（默认超时时间）
     */
    public static boolean tryLock(String lockKey, String requestId) {
        return tryLock(lockKey, requestId, DEFAULT_LOCK_TIMEOUT);
    }
    
    /**
     * 释放分布式锁
     * 
     * @param lockKey 锁键
     * @param requestId 请求ID
     * @return 是否成功释放
     */
    public static boolean releaseLock(String lockKey, String requestId) {
        if (!RedisConfig.isAvailable()) {
            releaseLocalLock(lockKey);
            return true;
        }
        
        try {
            String key = LOCK_PREFIX + lockKey;
            Cache cache = Redis.use();
            
            // 先获取锁的值，确认是自己的锁
            String currentValue = cache.get(key);
            if (requestId.equals(currentValue)) {
                cache.del(key);
                LogKit.debug("释放分布式锁成功: " + lockKey);
                return true;
            } else {
                LogKit.warn("尝试释放不属于自己的锁: " + lockKey);
                return false;
            }
            
        } catch (Exception e) {
            LogKit.error("释放分布式锁异常", e);
            releaseLocalLock(lockKey);
            return false;
        }
    }
    
    /**
     * 限流控制
     * 
     * @param resource 资源标识
     * @param maxRequests 最大请求数
     * @param windowSeconds 时间窗口（秒）
     * @return 是否允许通过
     */
    public static boolean checkRateLimit(String resource, int maxRequests, int windowSeconds) {
        if (!RedisConfig.isAvailable()) {
            // Redis不可用，使用本地限流
            return checkLocalRateLimit(resource, maxRequests);
        }
        
        try {
            String key = RATE_LIMIT_PREFIX + resource;
            Cache cache = Redis.use();
            
            // 获取当前计数
            String countStr = cache.get(key);
            int currentCount = countStr == null ? 0 : Integer.parseInt(countStr);
            
            if (currentCount >= maxRequests) {
                LogKit.warn("限流触发: " + resource + ", 当前: " + currentCount + "/" + maxRequests);
                return false;
            }
            
            // 增加计数
            Long newCount = cache.incr(key);
            
            // 第一次访问，设置过期时间
            if (newCount == 1) {
                cache.expire(key, windowSeconds);
            }
            
            return newCount <= maxRequests;
            
        } catch (Exception e) {
            LogKit.error("限流检查异常", e);
            // 异常时默认允许通过，避免影响业务
            return true;
        }
    }
    
    /**
     * 使用熔断器执行操作
     * 
     * @param name 熔断器名称
     * @param callable 要执行的操作
     * @param fallback 降级操作
     * @return 执行结果
     */
    public static <T> T executeWithCircuitBreaker(String name, Callable<T> callable, Callable<T> fallback) {
        CircuitBreaker breaker = circuitBreakers.computeIfAbsent(name, k -> new CircuitBreaker(name));
        
        if (!breaker.allowRequest()) {
            LogKit.warn("熔断器开启，执行降级操作: " + name);
            try {
                return fallback.call();
            } catch (Exception e) {
                LogKit.error("降级操作失败", e);
                return null;
            }
        }
        
        try {
            T result = callable.call();
            breaker.recordSuccess();
            return result;
            
        } catch (Exception e) {
            breaker.recordFailure();
            LogKit.error("操作执行失败，记录熔断器失败次数", e);
            
            try {
                return fallback.call();
            } catch (Exception fe) {
                LogKit.error("降级操作失败", fe);
                return null;
            }
        }
    }
    
    /**
     * 使用数据库乐观锁更新
     * 
     * @param sql 更新SQL（必须包含版本号条件）
     * @param params SQL参数
     * @param maxRetries 最大重试次数
     * @return 是否更新成功
     */
    public static boolean updateWithOptimisticLock(String sql, Object[] params, int maxRetries) {
        for (int i = 0; i < maxRetries; i++) {
            try {
                int rows = Db.update(sql, params);
                
                if (rows > 0) {
                    LogKit.debug("乐观锁更新成功，第" + (i + 1) + "次尝试");
                    return true;
                }
                
                if (i < maxRetries - 1) {
                    // 短暂休眠后重试
                    Thread.sleep(50 * (i + 1));
                }
                
            } catch (Exception e) {
                LogKit.error("乐观锁更新异常", e);
                return false;
            }
        }
        
        LogKit.warn("乐观锁更新失败，已达最大重试次数: " + maxRetries);
        return false;
    }
    
    /**
     * 获取信号量（控制并发数）
     * 
     * @param resource 资源标识
     * @param permits 许可数量
     * @return 信号量
     */
    public static Semaphore getSemaphore(String resource, int permits) {
        return localSemaphores.computeIfAbsent(resource, k -> new Semaphore(permits, true));
    }
    
    /**
     * 执行带并发控制的操作
     * 
     * @param resource 资源标识
     * @param maxConcurrency 最大并发数
     * @param callable 要执行的操作
     * @param timeoutMs 超时时间（毫秒）
     * @return 执行结果
     */
    public static <T> T executeWithConcurrencyControl(String resource, int maxConcurrency, 
                                                      Callable<T> callable, long timeoutMs) {
        Semaphore semaphore = getSemaphore(resource, maxConcurrency);
        
        try {
            if (!semaphore.tryAcquire(timeoutMs, TimeUnit.MILLISECONDS)) {
                LogKit.warn("获取并发许可超时: " + resource);
                throw new TimeoutException("获取并发许可超时");
            }
            
            try {
                return callable.call();
            } finally {
                semaphore.release();
            }
            
        } catch (Exception e) {
            LogKit.error("并发控制执行失败", e);
            return null;
        }
    }
    
    // ========== 私有辅助方法 ==========
    
    /**
     * 本地锁（降级方案）
     */
    private static boolean tryLocalLock(String lockKey, String requestId) {
        Semaphore semaphore = localSemaphores.computeIfAbsent(lockKey, k -> new Semaphore(1));
        return semaphore.tryAcquire();
    }
    
    /**
     * 释放本地锁
     */
    private static void releaseLocalLock(String lockKey) {
        Semaphore semaphore = localSemaphores.get(lockKey);
        if (semaphore != null) {
            semaphore.release();
        }
    }
    
    /**
     * 本地限流（降级方案）
     */
    private static boolean checkLocalRateLimit(String resource, int maxRequests) {
        Semaphore semaphore = getSemaphore("rate:" + resource, maxRequests);
        return semaphore.tryAcquire();
    }
    
    /**
     * 熔断器内部类
     */
    private static class CircuitBreaker {
        private final String name;
        private final AtomicInteger failureCount = new AtomicInteger(0);
        private final AtomicInteger successCount = new AtomicInteger(0);
        private volatile long lastFailureTime = 0;
        private volatile State state = State.CLOSED;
        
        // 配置参数
        private final int failureThreshold = 5;      // 失败阈值
        private final long timeout = 60000;          // 熔断超时时间（60秒）
        private final long halfOpenTimeout = 30000;  // 半开超时时间（30秒）
        
        enum State {
            CLOSED,     // 关闭（正常）
            OPEN,       // 开启（熔断）
            HALF_OPEN   // 半开（尝试恢复）
        }
        
        CircuitBreaker(String name) {
            this.name = name;
        }
        
        boolean allowRequest() {
            State currentState = state;
            
            if (currentState == State.CLOSED) {
                return true;
            }
            
            if (currentState == State.OPEN) {
                // 检查是否可以转为半开状态
                if (System.currentTimeMillis() - lastFailureTime > timeout) {
                    state = State.HALF_OPEN;
                    LogKit.info("熔断器转为半开状态: " + name);
                    return true;
                }
                return false;
            }
            
            // HALF_OPEN状态，允许少量请求通过
            return true;
        }
        
        void recordSuccess() {
            successCount.incrementAndGet();
            
            if (state == State.HALF_OPEN) {
                // 半开状态下成功，可以关闭熔断器
                state = State.CLOSED;
                failureCount.set(0);
                LogKit.info("熔断器恢复正常: " + name);
            }
        }
        
        void recordFailure() {
            lastFailureTime = System.currentTimeMillis();
            int failures = failureCount.incrementAndGet();
            
            if (failures >= failureThreshold && state == State.CLOSED) {
                state = State.OPEN;
                LogKit.warn("熔断器开启: " + name + ", 失败次数: " + failures);
            } else if (state == State.HALF_OPEN) {
                // 半开状态下失败，重新开启熔断
                state = State.OPEN;
                LogKit.warn("熔断器重新开启: " + name);
            }
        }
    }
}