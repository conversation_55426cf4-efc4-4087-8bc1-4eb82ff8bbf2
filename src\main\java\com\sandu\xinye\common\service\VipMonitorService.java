package com.sandu.xinye.common.service;

import com.jfinal.kit.JsonKit;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.config.RedisConfig;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * VIP监控服务
 * 负责监控系统运行状态、性能指标、异常告警
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class VipMonitorService {
    
    public static final VipMonitorService me = new VipMonitorService();
    
    // 监控指标存储
    private final Map<String, MetricCollector> metrics = new ConcurrentHashMap<>();
    
    // 告警队列
    private final BlockingQueue<Alert> alertQueue = new LinkedBlockingQueue<>();
    
    // 监控线程池
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    // 告警阈值配置
    private static final int ERROR_THRESHOLD = 10;          // 错误数阈值
    private static final int SLOW_QUERY_THRESHOLD = 1000;   // 慢查询阈值（毫秒）
    private static final double CPU_THRESHOLD = 0.8;        // CPU使用率阈值
    private static final double MEMORY_THRESHOLD = 0.9;     // 内存使用率阈值
    private static final int QUEUE_SIZE_THRESHOLD = 1000;   // 队列大小阈值
    
    // 告警级别
    public enum AlertLevel {
        INFO,     // 信息
        WARNING,  // 警告
        ERROR,    // 错误
        CRITICAL  // 严重
    }
    
    /**
     * 初始化监控服务
     */
    public void init() {
        LogKit.info("初始化VIP监控服务");
        
        // 启动监控任务
        startMonitoringTasks();
        
        // 启动告警处理
        startAlertProcessor();
        
        // 注册关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
    }
    
    /**
     * 记录业务指标
     */
    public void recordMetric(String name, double value) {
        getMetricCollector(name).record(value);
    }
    
    /**
     * 记录响应时间
     */
    public void recordResponseTime(String operation, long milliseconds) {
        getMetricCollector("response." + operation).record(milliseconds);
        
        // 检查慢操作
        if (milliseconds > SLOW_QUERY_THRESHOLD) {
            raiseAlert(AlertLevel.WARNING, 
                "慢操作检测: " + operation + " 耗时 " + milliseconds + "ms");
        }
    }
    
    /**
     * 记录错误
     */
    public void recordError(String type, String message, Exception error) {
        getMetricCollector("error." + type).increment();
        
        // 记录错误详情
        String errorDetail = String.format("错误类型: %s, 消息: %s, 异常: %s",
            type, message, error != null ? error.getMessage() : "无");
        
        LogKit.error(errorDetail, error);
        
        // 检查错误率
        MetricCollector errorMetric = getMetricCollector("error." + type);
        if (errorMetric.getCount() > ERROR_THRESHOLD) {
            raiseAlert(AlertLevel.ERROR, "错误频率过高: " + type + 
                      " (最近1分钟: " + errorMetric.getRecentCount(60) + "次)");
        }
    }
    
    /**
     * 监控订单创建
     */
    public void monitorOrderCreation(boolean success, long responseTime) {
        if (success) {
            getMetricCollector("order.create.success").increment();
        } else {
            getMetricCollector("order.create.fail").increment();
        }
        
        recordResponseTime("order.create", responseTime);
    }
    
    /**
     * 监控支付回调
     */
    public void monitorPaymentCallback(boolean success, long responseTime) {
        if (success) {
            getMetricCollector("payment.callback.success").increment();
        } else {
            getMetricCollector("payment.callback.fail").increment();
        }
        
        recordResponseTime("payment.callback", responseTime);
    }
    
    /**
     * 监控自动续费
     */
    public void monitorAutoRenew(int processed, int success, int failed) {
        getMetricCollector("autorenew.processed").add(processed);
        getMetricCollector("autorenew.success").add(success);
        getMetricCollector("autorenew.failed").add(failed);
        
        if (failed > 0) {
            double failRate = (double) failed / processed;
            if (failRate > 0.1) {
                raiseAlert(AlertLevel.WARNING, 
                    String.format("自动续费失败率过高: %.2f%% (%d/%d)",
                        failRate * 100, failed, processed));
            }
        }
    }
    
    /**
     * 获取监控仪表板数据
     */
    public Map<String, Object> getDashboard() {
        Map<String, Object> dashboard = new HashMap<>();
        
        // 系统状态
        dashboard.put("system", getSystemStatus());
        
        // 业务指标
        dashboard.put("business", getBusinessMetrics());
        
        // 性能指标
        dashboard.put("performance", getPerformanceMetrics());
        
        // 告警信息
        dashboard.put("alerts", getRecentAlerts());
        
        // 实时数据
        dashboard.put("realtime", getRealtimeData());
        
        return dashboard;
    }
    
    /**
     * 获取健康检查结果
     */
    public Map<String, Object> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", new Date());
        
        Map<String, Object> checks = new HashMap<>();
        
        // 数据库检查
        checks.put("database", checkDatabase());
        
        // Redis检查
        checks.put("redis", checkRedis());
        
        // 磁盘空间检查
        checks.put("disk", checkDiskSpace());
        
        // 内存检查
        checks.put("memory", checkMemory());
        
        health.put("checks", checks);
        
        // 如果有任何检查失败，标记为DOWN
        for (Map.Entry<String, Object> entry : checks.entrySet()) {
            Map<String, Object> check = (Map<String, Object>) entry.getValue();
            if (!"UP".equals(check.get("status"))) {
                health.put("status", "DOWN");
                break;
            }
        }
        
        return health;
    }
    
    /**
     * 发送告警
     */
    public void raiseAlert(AlertLevel level, String message) {
        Alert alert = new Alert(level, message, new Date());
        
        // 加入告警队列
        alertQueue.offer(alert);
        
        // 严重告警立即处理
        if (level == AlertLevel.CRITICAL) {
            processAlert(alert);
        }
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 启动监控任务
     */
    private void startMonitoringTasks() {
        // 系统监控（每分钟）
        scheduler.scheduleAtFixedRate(this::monitorSystem, 0, 1, TimeUnit.MINUTES);
        
        // 业务监控（每5分钟）
        scheduler.scheduleAtFixedRate(this::monitorBusiness, 0, 5, TimeUnit.MINUTES);
        
        // 性能监控（每30秒）
        scheduler.scheduleAtFixedRate(this::monitorPerformance, 0, 30, TimeUnit.SECONDS);
        
        // 缓存清理（每小时）
        scheduler.scheduleAtFixedRate(this::cleanupMetrics, 0, 1, TimeUnit.HOURS);
    }
    
    /**
     * 启动告警处理器
     */
    private void startAlertProcessor() {
        Thread alertProcessor = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    Alert alert = alertQueue.take();
                    processAlert(alert);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    LogKit.error("处理告警失败", e);
                }
            }
        }, "AlertProcessor");
        
        alertProcessor.setDaemon(true);
        alertProcessor.start();
    }
    
    /**
     * 处理告警
     */
    private void processAlert(Alert alert) {
        try {
            // 记录日志
            String logMessage = String.format("[%s] %s", alert.level, alert.message);
            
            switch (alert.level) {
                case INFO:
                    LogKit.info(logMessage);
                    break;
                case WARNING:
                    LogKit.warn(logMessage);
                    break;
                case ERROR:
                case CRITICAL:
                    LogKit.error(logMessage);
                    break;
            }
            
            // 发送通知（根据级别）
            if (alert.level == AlertLevel.ERROR || alert.level == AlertLevel.CRITICAL) {
                sendAlertNotification(alert);
            }
            
            // 记录到数据库
            saveAlertToDatabase(alert);
            
        } catch (Exception e) {
            LogKit.error("告警处理异常", e);
        }
    }
    
    /**
     * 发送告警通知
     */
    private void sendAlertNotification(Alert alert) {
        // TODO: 实现邮件、短信、钉钉等通知方式
        LogKit.info("发送告警通知: " + alert.message);
    }
    
    /**
     * 保存告警到数据库
     */
    private void saveAlertToDatabase(Alert alert) {
        try {
            String sql = "INSERT INTO vip_alert_log (level, message, created_at) VALUES (?, ?, ?)";
            Db.update(sql, alert.level.name(), alert.message, alert.timestamp);
        } catch (Exception e) {
            // 忽略数据库错误，避免影响主流程
            LogKit.debug("保存告警到数据库失败: " + e.getMessage());
        }
    }
    
    /**
     * 监控系统状态
     */
    private void monitorSystem() {
        try {
            Runtime runtime = Runtime.getRuntime();
            
            // 内存使用
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            double memoryUsage = (double) usedMemory / totalMemory;
            
            recordMetric("system.memory.used", usedMemory);
            recordMetric("system.memory.total", totalMemory);
            recordMetric("system.memory.usage", memoryUsage);
            
            // 内存告警
            if (memoryUsage > MEMORY_THRESHOLD) {
                raiseAlert(AlertLevel.WARNING, 
                    String.format("内存使用率过高: %.2f%%", memoryUsage * 100));
            }
            
            // 线程数
            int threadCount = Thread.activeCount();
            recordMetric("system.threads", threadCount);
            
        } catch (Exception e) {
            LogKit.error("系统监控失败", e);
        }
    }
    
    /**
     * 监控业务指标
     */
    private void monitorBusiness() {
        try {
            // 统计订单数据
            Record orderStats = Db.findFirst(
                "SELECT COUNT(*) as total, " +
                "SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid, " +
                "SUM(CASE WHEN create_time > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 ELSE 0 END) as recent " +
                "FROM vip_order"
            );
            
            if (orderStats != null) {
                recordMetric("business.order.total", orderStats.getLong("total"));
                recordMetric("business.order.paid", orderStats.getLong("paid"));
                recordMetric("business.order.recent", orderStats.getLong("recent"));
            }
            
            // 统计活跃VIP用户
            Long vipCount = Db.queryLong(
                "SELECT COUNT(*) FROM user WHERE user_type IN ('vip_monthly', 'vip_yearly') " +
                "AND vip_expire_time > NOW()"
            );
            
            recordMetric("business.vip.active", vipCount);
            
        } catch (Exception e) {
            LogKit.error("业务监控失败", e);
        }
    }
    
    /**
     * 监控性能指标
     */
    private void monitorPerformance() {
        try {
            // 缓存命中率
            Map<String, Object> cacheStats = VipCacheService.me.getCacheStats();
            if (cacheStats != null) {
                recordMetric("performance.cache.requests", 
                    ((Number) cacheStats.get("totalRequests")).doubleValue());
                recordMetric("performance.cache.hits", 
                    ((Number) cacheStats.get("localHits")).doubleValue() +
                    ((Number) cacheStats.get("ehcacheHits")).doubleValue() +
                    ((Number) cacheStats.get("redisHits")).doubleValue());
            }
            
        } catch (Exception e) {
            LogKit.error("性能监控失败", e);
        }
    }
    
    /**
     * 清理过期指标
     */
    private void cleanupMetrics() {
        for (MetricCollector collector : metrics.values()) {
            collector.cleanup();
        }
    }
    
    /**
     * 获取指标收集器
     */
    private MetricCollector getMetricCollector(String name) {
        return metrics.computeIfAbsent(name, k -> new MetricCollector(name));
    }
    
    /**
     * 检查数据库
     */
    private Map<String, Object> checkDatabase() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Db.queryInt("SELECT 1");
            result.put("status", "UP");
            result.put("message", "数据库连接正常");
        } catch (Exception e) {
            result.put("status", "DOWN");
            result.put("message", "数据库连接失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查Redis
     */
    private Map<String, Object> checkRedis() {
        Map<String, Object> result = new HashMap<>();
        
        if (RedisConfig.isAvailable()) {
            result.put("status", "UP");
            result.put("message", "Redis连接正常");
        } else {
            result.put("status", "WARNING");
            result.put("message", "Redis不可用");
        }
        
        return result;
    }
    
    /**
     * 检查磁盘空间
     */
    private Map<String, Object> checkDiskSpace() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            java.io.File root = new java.io.File("/");
            long freeSpace = root.getFreeSpace();
            long totalSpace = root.getTotalSpace();
            double usage = 1.0 - (double) freeSpace / totalSpace;
            
            if (usage < 0.9) {
                result.put("status", "UP");
                result.put("message", String.format("磁盘使用率: %.2f%%", usage * 100));
            } else {
                result.put("status", "WARNING");
                result.put("message", String.format("磁盘空间不足: %.2f%%", usage * 100));
            }
        } catch (Exception e) {
            result.put("status", "UNKNOWN");
            result.put("message", "无法检查磁盘空间");
        }
        
        return result;
    }
    
    /**
     * 检查内存
     */
    private Map<String, Object> checkMemory() {
        Map<String, Object> result = new HashMap<>();
        
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        double usage = 1.0 - (double) freeMemory / totalMemory;
        
        if (usage < MEMORY_THRESHOLD) {
            result.put("status", "UP");
            result.put("message", String.format("内存使用率: %.2f%%", usage * 100));
        } else {
            result.put("status", "WARNING");
            result.put("message", String.format("内存使用率过高: %.2f%%", usage * 100));
        }
        
        return result;
    }
    
    /**
     * 获取系统状态
     */
    private Map<String, Object> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        
        Runtime runtime = Runtime.getRuntime();
        status.put("memoryUsed", runtime.totalMemory() - runtime.freeMemory());
        status.put("memoryTotal", runtime.totalMemory());
        status.put("memoryMax", runtime.maxMemory());
        status.put("threads", Thread.activeCount());
        status.put("processors", runtime.availableProcessors());
        
        return status;
    }
    
    /**
     * 获取业务指标
     */
    private Map<String, Object> getBusinessMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        for (Map.Entry<String, MetricCollector> entry : this.metrics.entrySet()) {
            if (entry.getKey().startsWith("business.")) {
                metrics.put(entry.getKey(), entry.getValue().getSummary());
            }
        }
        
        return metrics;
    }
    
    /**
     * 获取性能指标
     */
    private Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        for (Map.Entry<String, MetricCollector> entry : this.metrics.entrySet()) {
            if (entry.getKey().startsWith("performance.") || entry.getKey().startsWith("response.")) {
                metrics.put(entry.getKey(), entry.getValue().getSummary());
            }
        }
        
        return metrics;
    }
    
    /**
     * 获取最近告警
     */
    private List<Map<String, Object>> getRecentAlerts() {
        List<Map<String, Object>> alerts = new ArrayList<>();
        
        // 获取队列中的告警（不移除）
        for (Alert alert : alertQueue) {
            Map<String, Object> alertMap = new HashMap<>();
            alertMap.put("level", alert.level);
            alertMap.put("message", alert.message);
            alertMap.put("timestamp", alert.timestamp);
            alerts.add(alertMap);
            
            if (alerts.size() >= 10) {
                break;
            }
        }
        
        return alerts;
    }
    
    /**
     * 获取实时数据
     */
    private Map<String, Object> getRealtimeData() {
        Map<String, Object> realtime = new HashMap<>();
        
        // 最近1分钟的数据
        realtime.put("orderCreateRate", getMetricCollector("order.create.success").getRecentCount(60));
        realtime.put("paymentCallbackRate", getMetricCollector("payment.callback.success").getRecentCount(60));
        realtime.put("errorRate", getErrorRate());
        
        return realtime;
    }
    
    /**
     * 获取错误率
     */
    private double getErrorRate() {
        double errors = 0;
        double total = 0;
        
        for (Map.Entry<String, MetricCollector> entry : metrics.entrySet()) {
            if (entry.getKey().startsWith("error.")) {
                errors += entry.getValue().getRecentCount(60);
            }
            total += entry.getValue().getRecentCount(60);
        }
        
        return total > 0 ? errors / total : 0;
    }
    
    /**
     * 关闭监控服务
     */
    private void shutdown() {
        LogKit.info("关闭VIP监控服务");
        
        scheduler.shutdown();
        
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
        }
    }
    
    /**
     * 告警对象
     */
    private static class Alert {
        final AlertLevel level;
        final String message;
        final Date timestamp;
        
        Alert(AlertLevel level, String message, Date timestamp) {
            this.level = level;
            this.message = message;
            this.timestamp = timestamp;
        }
    }
    
    /**
     * 指标收集器
     */
    private static class MetricCollector {
        private final String name;
        private final AtomicLong count = new AtomicLong();
        private final AtomicLong sum = new AtomicLong();
        private final Queue<TimedValue> recentValues = new ConcurrentLinkedQueue<>();
        
        MetricCollector(String name) {
            this.name = name;
        }
        
        void record(double value) {
            count.incrementAndGet();
            sum.addAndGet((long) value);
            recentValues.offer(new TimedValue(value, System.currentTimeMillis()));
        }
        
        void increment() {
            record(1);
        }
        
        void add(long value) {
            record(value);
        }
        
        long getCount() {
            return count.get();
        }
        
        long getRecentCount(int seconds) {
            long cutoff = System.currentTimeMillis() - seconds * 1000L;
            return recentValues.stream()
                .filter(v -> v.timestamp > cutoff)
                .count();
        }
        
        Map<String, Object> getSummary() {
            Map<String, Object> summary = new HashMap<>();
            summary.put("count", count.get());
            summary.put("sum", sum.get());
            
            if (count.get() > 0) {
                summary.put("average", sum.get() / count.get());
            }
            
            return summary;
        }
        
        void cleanup() {
            long cutoff = System.currentTimeMillis() - 3600000; // 保留1小时
            recentValues.removeIf(v -> v.timestamp < cutoff);
        }
        
        private static class TimedValue {
            final double value;
            final long timestamp;
            
            TimedValue(double value, long timestamp) {
                this.value = value;
                this.timestamp = timestamp;
            }
        }
    }
}