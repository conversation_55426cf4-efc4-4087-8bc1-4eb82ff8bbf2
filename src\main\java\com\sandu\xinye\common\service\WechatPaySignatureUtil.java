package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.UUID;

/**
 * 微信支付v3签名工具类
 * 实现微信支付API v3的签名算法和验签算法
 */
public class WechatPaySignatureUtil {
    
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    
    /**
     * 生成随机字符串（32位）
     */
    public static String generateNonce() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 获取当前时间戳（秒）
     */
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis() / 1000;
    }
    
    /**
     * 构造签名串
     * 
     * @param method HTTP方法（GET、POST等）
     * @param url 请求URL路径（不包含域名）
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param body 请求体（GET请求为空字符串）
     * @return 签名串
     */
    public static String buildSignatureString(String method, String url, long timestamp, String nonce, String body) {
        if (body == null) {
            body = "";
        }
        
        return method + "\n" +
               url + "\n" +
               timestamp + "\n" +
               nonce + "\n" +
               body + "\n";
    }
    
    /**
     * 使用私钥对签名串进行RSA-SHA256签名
     * 
     * @param signatureString 签名串
     * @param privateKey 商户私钥
     * @return Base64编码的签名
     */
    public static String sign(String signatureString, PrivateKey privateKey) {
        try {
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initSign(privateKey);
            signature.update(signatureString.getBytes(StandardCharsets.UTF_8));
            
            byte[] signatureBytes = signature.sign();
            return Base64.getEncoder().encodeToString(signatureBytes);
            
        } catch (Exception e) {
            LogKit.error("微信支付签名失败", e);
            throw new RuntimeException("微信支付签名失败", e);
        }
    }
    
    /**
     * 构造Authorization header
     * 
     * @param mchId 商户号
     * @param serialNo 证书序列号
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param signature 签名
     * @return Authorization header值
     */
    public static String buildAuthorizationHeader(String mchId, String serialNo, long timestamp, String nonce, String signature) {
        return String.format("WECHATPAY2-SHA256-RSA2048 mchid=\"%s\",nonce_str=\"%s\",timestamp=\"%d\",serial_no=\"%s\",signature=\"%s\"",
                mchId, nonce, timestamp, serialNo, signature);
    }
    
    /**
     * 验证微信支付回调签名
     * 
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param body 回调请求体
     * @param signature 微信传来的签名
     * @param certificate 微信支付平台证书
     * @return 验签结果
     */
    public static boolean verifyCallback(String timestamp, String nonce, String body, String signature, X509Certificate certificate) {
        try {
            // 构造验签串
            String signatureString = timestamp + "\n" + nonce + "\n" + body + "\n";
            
            // 使用平台证书验签
            Signature sig = Signature.getInstance(SIGNATURE_ALGORITHM);
            sig.initVerify(certificate.getPublicKey());
            sig.update(signatureString.getBytes(StandardCharsets.UTF_8));
            
            byte[] signatureBytes = Base64.getDecoder().decode(signature);
            return sig.verify(signatureBytes);
            
        } catch (Exception e) {
            LogKit.error("微信支付回调验签失败", e);
            return false;
        }
    }
    
    /**
     * 验证时间戳是否在有效范围内（5分钟内）
     * 
     * @param timestamp 时间戳字符串
     * @return 是否有效
     */
    public static boolean isTimestampValid(String timestamp) {
        try {
            long callbackTime = Long.parseLong(timestamp);
            long currentTime = getCurrentTimestamp();
            long timeDiff = Math.abs(currentTime - callbackTime);
            
            // 允许5分钟的时间差
            return timeDiff <= 300;
            
        } catch (NumberFormatException e) {
            LogKit.error("时间戳格式错误: " + timestamp, e);
            return false;
        }
    }
}
