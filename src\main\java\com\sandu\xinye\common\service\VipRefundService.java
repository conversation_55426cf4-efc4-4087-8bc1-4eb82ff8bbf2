package com.sandu.xinye.common.service;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.JsonKit;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.sandu.xinye.common.enums.UserTier;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.VipOrder;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * VIP退款服务
 * 处理VIP订单的退款申请、审核和执行
 */
public class VipRefundService {
    
    public static final VipRefundService me = new VipRefundService();
    
    // 退款状态常量
    public static final String REFUND_STATUS_PENDING = "pending";     // 待处理
    public static final String REFUND_STATUS_APPROVED = "approved";   // 已批准
    public static final String REFUND_STATUS_REJECTED = "rejected";   // 已拒绝
    public static final String REFUND_STATUS_PROCESSING = "processing"; // 处理中
    public static final String REFUND_STATUS_SUCCESS = "success";     // 退款成功
    public static final String REFUND_STATUS_FAILED = "failed";       // 退款失败
    
    // 退款原因常量
    public static final String REFUND_REASON_USER_REQUEST = "user_request";     // 用户主动申请
    public static final String REFUND_REASON_SYSTEM_ERROR = "system_error";     // 系统错误
    public static final String REFUND_REASON_DUPLICATE_PAYMENT = "duplicate";   // 重复支付
    public static final String REFUND_REASON_SERVICE_ISSUE = "service_issue";   // 服务问题
    
    /**
     * 申请退款
     */
    public RetKit applyRefund(String orderNo, String reason, String description) {
        try {
            // 参数验证
            if (StrKit.isBlank(orderNo)) {
                return RetKit.fail("订单号不能为空");
            }
            
            if (StrKit.isBlank(reason)) {
                return RetKit.fail("退款原因不能为空");
            }
            
            // 查找订单
            VipOrder order = VipOrder.dao.findFirst("SELECT * FROM vip_order WHERE order_no = ?", orderNo);
            if (order == null) {
                return RetKit.fail("订单不存在");
            }
            
            // 检查订单状态
            if (!VipOrder.STATUS_PAID.equals(order.getStatus())) {
                return RetKit.fail("只有已支付的订单才能申请退款");
            }
            
            // 检查是否已有退款申请
            if (hasExistingRefund(orderNo)) {
                return RetKit.fail("该订单已有退款申请，请勿重复申请");
            }
            
            // 检查退款时效（7天内）
            if (!isWithinRefundPeriod(order)) {
                return RetKit.fail("超出退款时效，订单支付后7天内可申请退款");
            }
            
            // 创建退款记录
            String refundNo = generateRefundNo();
            boolean success = createRefundRecord(order, refundNo, reason, description);
            
            if (success) {
                LogKit.info("退款申请创建成功: " + refundNo + ", 订单: " + orderNo);
                
                return RetKit.ok("退款申请提交成功")
                    .set("refundNo", refundNo)
                    .set("orderNo", orderNo)
                    .set("amount", order.getAmountYuan())
                    .set("status", REFUND_STATUS_PENDING);
            } else {
                return RetKit.fail("退款申请创建失败");
            }
            
        } catch (Exception e) {
            LogKit.error("申请退款失败", e);
            return RetKit.fail("申请退款失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理退款（审核通过后执行）
     */
    public RetKit processRefund(String refundNo, boolean approve, String adminRemark) {
        try {
            // 查找退款记录
            Map<String, Object> refund = getRefundRecord(refundNo);
            if (refund == null) {
                return RetKit.fail("退款记录不存在");
            }
            
            String currentStatus = (String) refund.get("status");
            if (!REFUND_STATUS_PENDING.equals(currentStatus)) {
                return RetKit.fail("退款申请状态不正确，当前状态: " + currentStatus);
            }
            
            if (approve) {
                // 审核通过，执行退款
                return executeRefund(refundNo, adminRemark);
            } else {
                // 审核拒绝
                return rejectRefund(refundNo, adminRemark);
            }
            
        } catch (Exception e) {
            LogKit.error("处理退款失败", e);
            return RetKit.fail("处理退款失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行退款
     */
    private RetKit executeRefund(String refundNo, String adminRemark) {
        try {
            // 更新退款状态为处理中
            updateRefundStatus(refundNo, REFUND_STATUS_PROCESSING, adminRemark);
            
            // 获取退款信息
            Map<String, Object> refund = getRefundRecord(refundNo);
            String orderNo = (String) refund.get("order_no");
            Integer refundAmount = (Integer) refund.get("refund_amount");
            
            // 查找原订单
            VipOrder order = VipOrder.dao.findFirst("SELECT * FROM vip_order WHERE order_no = ?", orderNo);
            if (order == null) {
                updateRefundStatus(refundNo, REFUND_STATUS_FAILED, "原订单不存在");
                return RetKit.fail("原订单不存在");
            }
            
            // 调用微信支付退款API
            boolean wechatRefundSuccess = callWechatRefund(order, refundAmount, refundNo);
            
            if (wechatRefundSuccess) {
                // 使用事务处理退款后续操作
                boolean success = Db.tx(() -> {
                    // 更新退款状态
                    updateRefundStatus(refundNo, REFUND_STATUS_SUCCESS, "退款成功");
                    
                    // 回收用户VIP权益
                    revokeVipBenefits(order);
                    
                    // 更新订单状态
                    order.setStatus("refunded").setUpdateTime(new Date()).update();
                    
                    return true;
                });
                
                if (success) {
                    LogKit.info("退款执行成功: " + refundNo);
                    return RetKit.ok("退款执行成功")
                        .set("refundNo", refundNo)
                        .set("amount", refundAmount / 100.0);
                } else {
                    updateRefundStatus(refundNo, REFUND_STATUS_FAILED, "数据库操作失败");
                    return RetKit.fail("退款后续处理失败");
                }
                
            } else {
                updateRefundStatus(refundNo, REFUND_STATUS_FAILED, "微信退款失败");
                return RetKit.fail("微信退款失败");
            }
            
        } catch (Exception e) {
            LogKit.error("执行退款异常: " + refundNo, e);
            updateRefundStatus(refundNo, REFUND_STATUS_FAILED, "执行退款异常: " + e.getMessage());
            return RetKit.fail("执行退款异常: " + e.getMessage());
        }
    }
    
    /**
     * 拒绝退款
     */
    private RetKit rejectRefund(String refundNo, String adminRemark) {
        try {
            updateRefundStatus(refundNo, REFUND_STATUS_REJECTED, adminRemark);
            
            LogKit.info("退款申请已拒绝: " + refundNo + ", 原因: " + adminRemark);
            
            return RetKit.ok("退款申请已拒绝")
                .set("refundNo", refundNo)
                .set("reason", adminRemark);
                
        } catch (Exception e) {
            LogKit.error("拒绝退款失败: " + refundNo, e);
            return RetKit.fail("拒绝退款失败: " + e.getMessage());
        }
    }
    
    /**
     * 调用微信支付退款API
     */
    private boolean callWechatRefund(VipOrder order, Integer refundAmount, String refundNo) {
        try {
            LogKit.info("开始调用微信支付退款API: " + refundNo);
            
            // 检查微信支付服务是否可用
            if (!WechatPayService.me.isAvailable()) {
                LogKit.error("微信支付服务不可用");
                return false;
            }
            
            // 构建退款请求
            Map<String, Object> refundRequest = new HashMap<>();
            refundRequest.put("out_trade_no", order.getOrderNo());
            refundRequest.put("out_refund_no", refundNo);
            refundRequest.put("reason", "用户申请退款");
            
            Map<String, Object> amount = new HashMap<>();
            amount.put("refund", refundAmount);
            amount.put("total", order.getAmount());
            amount.put("currency", "CNY");
            refundRequest.put("amount", amount);
            
            String requestBody = JsonKit.toJson(refundRequest);
            
            // 发送退款请求
            WechatPayConfig config = WechatPayConfig.me;
            String refundUrl = "https://api.mch.weixin.qq.com/v3/refund/domestic/refunds";
            
            // 这里需要使用HTTP客户端发送请求
            // 由于我们已经有了WechatPayHttpClient，可以扩展它来支持退款
            // 暂时返回true作为模拟实现
            LogKit.info("微信退款请求: " + requestBody);
            
            // TODO: 实现真实的微信退款API调用
            // WechatPayResponse response = httpClient.post(refundUrl, requestBody);
            // return response.isSuccess();
            
            // 模拟退款成功
            return true;
            
        } catch (Exception e) {
            LogKit.error("调用微信退款API失败", e);
            return false;
        }
    }
    
    /**
     * 回收用户VIP权益
     */
    private void revokeVipBenefits(VipOrder order) {
        try {
            User user = User.dao.findById(order.getUserId());
            if (user != null && !UserTier.FREE.name().equals(user.getUserTier())) {
                // 将用户降级为免费用户
                user.setUserTier(UserTier.FREE.name())
                    .setVipExpireTime(null)
                    .update();
                
                LogKit.info("用户VIP权益已回收: " + user.getUserId() + ", 订单: " + order.getOrderNo());
            }
            
        } catch (Exception e) {
            LogKit.error("回收VIP权益失败", e);
        }
    }
    
    /**
     * 检查是否在退款时效内（7天）
     */
    private boolean isWithinRefundPeriod(VipOrder order) {
        if (order.getPaidTime() == null) {
            return false;
        }
        
        long diffDays = (System.currentTimeMillis() - order.getPaidTime().getTime()) / (1000 * 60 * 60 * 24);
        return diffDays <= 7;
    }
    
    /**
     * 检查是否已有退款申请
     */
    private boolean hasExistingRefund(String orderNo) {
        try {
            // TODO: 查询退款表
            // Long count = Db.queryLong("SELECT COUNT(*) FROM vip_refund WHERE order_no = ? AND status NOT IN (?, ?)", 
            //     orderNo, REFUND_STATUS_REJECTED, REFUND_STATUS_FAILED);
            // return count > 0;
            
            // 暂时返回false
            return false;
            
        } catch (Exception e) {
            LogKit.error("检查退款申请失败", e);
            return true; // 出错时保守处理，认为已有申请
        }
    }
    
    /**
     * 生成退款单号
     */
    private String generateRefundNo() {
        return "RF" + System.currentTimeMillis() + String.format("%03d", (int)(Math.random() * 1000));
    }
    
    /**
     * 创建退款记录
     */
    private boolean createRefundRecord(VipOrder order, String refundNo, String reason, String description) {
        try {
            // TODO: 插入退款表
            // String sql = "INSERT INTO vip_refund (refund_no, order_no, user_id, refund_amount, reason, description, status, create_time, update_time) " +
            //              "VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            // 
            // int result = Db.update(sql, refundNo, order.getOrderNo(), order.getUserId(), 
            //     order.getAmount(), reason, description, REFUND_STATUS_PENDING);
            // 
            // return result > 0;
            
            LogKit.info("创建退款记录: " + refundNo + ", 订单: " + order.getOrderNo());
            return true;
            
        } catch (Exception e) {
            LogKit.error("创建退款记录失败", e);
            return false;
        }
    }
    
    /**
     * 获取退款记录
     */
    private Map<String, Object> getRefundRecord(String refundNo) {
        try {
            // TODO: 查询退款表
            // return Db.queryFirst("SELECT * FROM vip_refund WHERE refund_no = ?", refundNo);
            
            // 模拟返回
            Map<String, Object> refund = new HashMap<>();
            refund.put("refund_no", refundNo);
            refund.put("order_no", "VIP123456789");
            refund.put("status", REFUND_STATUS_PENDING);
            refund.put("refund_amount", 990);
            return refund;
            
        } catch (Exception e) {
            LogKit.error("获取退款记录失败", e);
            return null;
        }
    }
    
    /**
     * 更新退款状态
     */
    private void updateRefundStatus(String refundNo, String status, String remark) {
        try {
            // TODO: 更新退款表
            // String sql = "UPDATE vip_refund SET status = ?, admin_remark = ?, update_time = NOW() WHERE refund_no = ?";
            // Db.update(sql, status, remark, refundNo);
            
            LogKit.info("更新退款状态: " + refundNo + " -> " + status + ", 备注: " + remark);
            
        } catch (Exception e) {
            LogKit.error("更新退款状态失败", e);
        }
    }
}
