-- 订阅与价格改造（连续订阅 vs 一次性）
-- 注意：在执行前备份数据库

-- 1) vip_order 表新增字段
ALTER TABLE `vip_order`
  ADD COLUMN `subscription_type` VARCHAR(20) NULL COMMENT '订阅类型(once/monthly/yearly)' AFTER `plan`,
  ADD COLUMN `auto_renew` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否自动续费(0/1)' AFTER `subscription_type`,
  ADD COLUMN `contract_id` VARCHAR(64) NULL COMMENT '委托代扣协议号' AFTER `auto_renew`;

CREATE INDEX `idx_vip_order_subscription` ON `vip_order` (`subscription_type`);
CREATE INDEX `idx_vip_order_contract` ON `vip_order` (`contract_id`);

-- 2) 新增订阅管理表
CREATE TABLE IF NOT EXISTS `vip_subscription` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` INT NOT NULL,
  `subscription_type` VARCHAR(20) NOT NULL COMMENT 'monthly/yearly',
  `contract_id` VARCHAR(64) NULL COMMENT '委托代扣协议号',
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'active/cancelled/paused/expired',
  `auto_renew` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否自动续费',
  `start_time` DATETIME NOT NULL,
  `next_renew_time` DATETIME NULL,
  `cancel_time` DATETIME NULL,
  `fail_count` INT NOT NULL DEFAULT 0,
  `last_fail_reason` VARCHAR(255) NULL,
  `last_renew_order_no` VARCHAR(64) NULL,
  `create_time` DATETIME NOT NULL DEFAULT NOW(),
  `update_time` DATETIME NOT NULL DEFAULT NOW(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_type` (`user_id`,`subscription_type`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_status` (`status`),
  KEY `idx_next_renew_time` (`next_renew_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP订阅管理表';

-- 3) 扩展价格配置表：增加购买方式维度
ALTER TABLE `vip_price_config`
  ADD COLUMN `purchase_type` VARCHAR(20) NOT NULL DEFAULT 'one_time' COMMENT '购买方式(subscribe/one_time)' AFTER `plan`;

-- 旧唯一键 uk_plan 可能存在，需先删除再创建新唯一键
ALTER TABLE `vip_price_config`
  DROP INDEX `uk_plan`;

ALTER TABLE `vip_price_config`
  ADD UNIQUE KEY `uk_plan_purchase` (`plan`, `purchase_type`);

-- 初始化四种价格：
-- 月度：连续包月 990分；单月一次性 1590分
-- 年度：连续包年 9900分；一次性年费 15900分
INSERT INTO `vip_price_config` (`plan`, `purchase_type`, `price`, `original_price`, `description`, `is_active`, `sort_order`, `create_time`, `update_time`) VALUES
('monthly', 'subscribe', 990, 1590, '连续包月价', 1, 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE `price`=VALUES(`price`), `original_price`=VALUES(`original_price`), `description`=VALUES(`description`), `update_time`=NOW();

INSERT INTO `vip_price_config` (`plan`, `purchase_type`, `price`, `original_price`, `description`, `is_active`, `sort_order`, `create_time`, `update_time`) VALUES
('monthly', 'one_time', 1590, 1590, '单月一次性价', 1, 2, NOW(), NOW())
ON DUPLICATE KEY UPDATE `price`=VALUES(`price`), `original_price`=VALUES(`original_price`), `description`=VALUES(`description`), `update_time`=NOW();

INSERT INTO `vip_price_config` (`plan`, `purchase_type`, `price`, `original_price`, `description`, `is_active`, `sort_order`, `create_time`, `update_time`) VALUES
('yearly', 'subscribe', 9900, 15900, '连续包年价', 1, 3, NOW(), NOW())
ON DUPLICATE KEY UPDATE `price`=VALUES(`price`), `original_price`=VALUES(`original_price`), `description`=VALUES(`description`), `update_time`=NOW();

INSERT INTO `vip_price_config` (`plan`, `purchase_type`, `price`, `original_price`, `description`, `is_active`, `sort_order`, `create_time`, `update_time`) VALUES
('yearly', 'one_time', 15900, 15900, '一次性年费', 1, 4, NOW(), NOW())
ON DUPLICATE KEY UPDATE `price`=VALUES(`price`), `original_price`=VALUES(`original_price`), `description`=VALUES(`description`), `update_time`=NOW();

