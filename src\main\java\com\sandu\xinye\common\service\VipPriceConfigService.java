package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.VipOrder;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * VIP价格配置服务
 * 管理VIP套餐的价格配置，支持动态调价和促销活动
 */
public class VipPriceConfigService {

    public static final VipPriceConfigService me = new VipPriceConfigService();

    // 价格缓存
    private final Map<String, Integer> priceCache = new ConcurrentHashMap<>();

    // 价格缓存（按 plan + purchaseType）
    private final Map<String, Integer> priceCacheByType = new ConcurrentHashMap<>();

    // 按 plan + purchaseType 的缓存更新时间
    private volatile long lastCacheByTypeUpdate = 0;

    // 促销活动缓存
    private final Map<String, Map<String, Object>> promotionCache = new ConcurrentHashMap<>();

    // 缓存更新时间
    private volatile long lastCacheUpdate = 0;

    // 缓存有效期（5分钟）
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000;

    /**
     * 获取套餐价格（分）
     */
    public int getPlanPrice(String plan) {
        try {
            // 检查缓存是否过期
            if (isCacheExpired()) {
                refreshCache();
            }

            // 从缓存获取价格
            Integer price = priceCache.get(plan);
            if (price != null) {
                return price;
            }

            // 缓存未命中，从数据库查询
            price = queryPriceFromDatabase(plan);
            if (price != null) {
                priceCache.put(plan, price);
                return price;
            }

            // 数据库也没有，返回默认价格
            return getDefaultPrice(plan);

        } catch (Exception e) {
            LogKit.error("获取套餐价格失败: " + plan, e);
            return getDefaultPrice(plan);
        }
    }


    /**
     * 获取套餐价格（分），带购买方式维度
     * purchaseType: subscribe | one_time
     */
    public int getPlanPrice(String plan, String purchaseType) {
        try {
            if (StrKit.isBlank(plan)) return getDefaultPrice(VipOrder.PLAN_MONTHLY, "one_time");
            if (StrKit.isBlank(purchaseType)) purchaseType = "one_time";
            String key = plan + "|" + purchaseType;

            // 检查缓存是否过期
            if (isCacheByTypeExpired()) {
                refreshCacheByType();
            }

            Integer price = priceCacheByType.get(key);
            if (price != null) return price;

            // DB 查询
            price = queryPriceFromDatabase(plan, purchaseType);
            if (price != null) {
                priceCacheByType.put(key, price);
                return price;
            }

            // 兜底默认价
            return getDefaultPrice(plan, purchaseType);
        } catch (Exception e) {
            LogKit.error("获取套餐价格失败: " + plan + ", purchaseType=" + purchaseType, e);
            return getDefaultPrice(plan, purchaseType);
        }
    }

    /** 获取套餐价格（元），带购买方式维度 */
    public double getPlanPriceYuan(String plan, String purchaseType) {
        return getPlanPrice(plan, purchaseType) / 100.0;
    }

    /**
     * 获取套餐价格（元）
     */
    public double getPlanPriceYuan(String plan) {
        return getPlanPrice(plan) / 100.0;
    }

    /**
     * 计算实际支付价格（考虑促销活动）
     */
    public int calculateActualPrice(String plan, Integer userId) {
        try {
            int originalPrice = getPlanPrice(plan);

            // 检查是否有适用的促销活动
            Map<String, Object> promotion = getApplicablePromotion(plan, userId);
            if (promotion != null) {
                return calculatePromotionPrice(originalPrice, promotion);
            }

            return originalPrice;

        } catch (Exception e) {
            LogKit.error("计算实际价格失败: " + plan, e);
            return getPlanPrice(plan);
        }
    }

    /**
     * 获取价格配置列表
     */
    public RetKit getPriceConfigs() {
        try {
            // TODO: 从数据库查询价格配置
            List<Record> configs = Db.query("SELECT * FROM vip_price_config WHERE is_active = 1 ORDER BY sort_order");

            Map<String, Object> result = new HashMap<>();
            for (Record config : configs) {
                String plan = config.getStr("plan");
                Map<String, Object> m = new HashMap<>();
                m.put("plan", plan);
                m.put("price", config.getInt("price"));
                m.put("priceYuan", config.getInt("price") / 100.0);
                m.put("originalPrice", config.getInt("original_price"));
                m.put("description", config.getStr("description"));
                m.put("isActive", config.getBoolean("is_active"));
                result.put(plan, m);
            }

            return RetKit.ok("查询成功").set("configs", result);

        } catch (Exception e) {
            LogKit.error("获取价格配置失败", e);

            // 返回默认配置
            Map<String, Object> defaultConfigs = new HashMap<>();
            Map<String, Object> m1 = new HashMap<>();
            m1.put("plan", VipOrder.PLAN_MONTHLY);
            m1.put("price", 990);
            m1.put("priceYuan", 9.9);
            m1.put("originalPrice", 990);
            m1.put("description", "月度VIP");
            m1.put("isActive", true);
            defaultConfigs.put(VipOrder.PLAN_MONTHLY, m1);
            Map<String, Object> m2 = new HashMap<>();
            m2.put("plan", VipOrder.PLAN_YEARLY);
            m2.put("price", 9900);
            m2.put("priceYuan", 99.0);
            m2.put("originalPrice", 9900);
            m2.put("description", "年度VIP");
            m2.put("isActive", true);
            defaultConfigs.put(VipOrder.PLAN_YEARLY, m2);

            return RetKit.ok("查询成功（默认配置）").set("configs", defaultConfigs);
        }
    }

    /**
     * 更新价格配置
     */
    public RetKit updatePriceConfig(String plan, Integer price, String description) {
        try {
            // 参数验证
            if (StrKit.isBlank(plan)) {
                return RetKit.fail("套餐类型不能为空");
            }

            if (price == null || price <= 0) {
                return RetKit.fail("价格必须大于0");
            }

            // TODO: 验证管理员权限

            // 更新数据库
            String sql = "UPDATE vip_price_config SET price = ?, description = ?, update_time = NOW() WHERE plan = ?";
            int result = Db.update(sql, price, description, plan);

            if (result == 0) {
                // 如果更新失败，尝试插入新记录
                sql = "INSERT INTO vip_price_config (plan, price, original_price, description, is_active, sort_order, create_time, update_time) " +
                      "VALUES (?, ?, ?, ?, 1, 0, NOW(), NOW())";
                result = Db.update(sql, plan, price, price, description);
            }

            if (result > 0) {
                // 清除缓存
                clearCache();

                LogKit.info("价格配置更新成功: " + plan + " -> " + price + "分");

                return RetKit.ok("价格配置更新成功")
                    .set("plan", plan)
                    .set("price", price)
                    .set("priceYuan", price / 100.0);
            } else {
                return RetKit.fail("价格配置更新失败");
            }

        } catch (Exception e) {
            LogKit.error("更新价格配置失败", e);
            return RetKit.fail("更新价格配置失败: " + e.getMessage());
        }
    }

    /**
     * 创建促销活动
     */
    public RetKit createPromotion(String name, String plan, String discountType,
                                 Integer discountValue, Date startTime, Date endTime,
                                 Integer maxUses, String description) {
        try {
            // 参数验证
            if (StrKit.isBlank(name) || StrKit.isBlank(plan) || StrKit.isBlank(discountType)) {
                return RetKit.fail("必填参数不能为空");
            }

            if (discountValue == null || discountValue <= 0) {
                return RetKit.fail("折扣值必须大于0");
            }

            if (startTime == null || endTime == null || startTime.after(endTime)) {
                return RetKit.fail("活动时间设置无效");
            }

            // TODO: 验证管理员权限

            // 生成促销活动ID
            String promotionId = "PROMO" + System.currentTimeMillis();

            // 插入数据库
            String sql = "INSERT INTO vip_promotion (promotion_id, name, plan, discount_type, discount_value, " +
                        "start_time, end_time, max_uses, used_count, description, is_active, create_time, update_time) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 1, NOW(), NOW())";

            int result = Db.update(sql, promotionId, name, plan, discountType, discountValue,
                                 startTime, endTime, maxUses, description);

            if (result > 0) {
                // 清除促销缓存
                clearPromotionCache();

                LogKit.info("促销活动创建成功: " + promotionId);

                return RetKit.ok("促销活动创建成功")
                    .set("promotionId", promotionId)
                    .set("name", name)
                    .set("plan", plan);
            } else {
                return RetKit.fail("促销活动创建失败");
            }

        } catch (Exception e) {
            LogKit.error("创建促销活动失败", e);
            return RetKit.fail("创建促销活动失败: " + e.getMessage());
        }
    }

    /**
     * 获取适用的促销活动
     */
    private Map<String, Object> getApplicablePromotion(String plan, Integer userId) {
        try {
            // 检查缓存
            String cacheKey = plan + "_" + (userId != null ? userId : "all");
            Map<String, Object> cachedPromotion = promotionCache.get(cacheKey);
            if (cachedPromotion != null) {
                return cachedPromotion;
            }

            // 从数据库查询
            String sql = "SELECT * FROM vip_promotion WHERE plan = ? AND is_active = 1 " +
                        "AND start_time <= NOW() AND end_time >= NOW() " +
                        "AND (max_uses IS NULL OR used_count < max_uses) " +
                        "ORDER BY discount_value DESC LIMIT 1";

            Record promotion = Db.queryFirst(sql, plan);
            if (promotion != null) {
                Map<String, Object> promotionMap = promotion.getColumns();

                // 检查用户是否已使用过此促销
                if (userId != null && hasUserUsedPromotion(userId, promotion.getStr("promotion_id"))) {
                    return null;
                }

                // 缓存促销信息
                promotionCache.put(cacheKey, promotionMap);
                return promotionMap;
            }

            return null;

        } catch (Exception e) {
            LogKit.error("获取适用促销活动失败", e);
            return null;
        }
    }

    /**
     * 计算促销价格
     */
    private int calculatePromotionPrice(int originalPrice, Map<String, Object> promotion) {
        try {
            String discountType = (String) promotion.get("discount_type");
            Integer discountValue = (Integer) promotion.get("discount_value");

            if ("percentage".equals(discountType)) {
                // 百分比折扣
                return originalPrice * (100 - discountValue) / 100;
            } else if ("fixed".equals(discountType)) {
                // 固定金额折扣
                int discountedPrice = originalPrice - discountValue;
                return Math.max(discountedPrice, 1); // 最低1分
            }

            return originalPrice;

        } catch (Exception e) {
            LogKit.error("计算促销价格失败", e);
            return originalPrice;
        }
    }

    /**
     * 检查用户是否已使用过促销
     */
    private boolean hasUserUsedPromotion(Integer userId, String promotionId) {
        try {
            Long count = Db.queryLong("SELECT COUNT(*) FROM vip_order WHERE user_id = ? AND promotion_id = ?",
                                    userId, promotionId);
            return count > 0;

        } catch (Exception e) {
            LogKit.error("检查用户促销使用情况失败", e);
            return false;
        }
    }

    /**
     * 从数据库查询价格
     */
    private Integer queryPriceFromDatabase(String plan) {
        try {
            Record record = Db.queryFirst("SELECT price FROM vip_price_config WHERE plan = ? AND is_active = 1", plan);
            return record != null ? record.getInt("price") : null;
        } catch (Exception e) {
            LogKit.error("从数据库查询价格失败", e);
            return null;
        }
    }

    // ====== 新增：按 plan + purchaseType 的缓存与查询 ======
    private boolean isCacheByTypeExpired() {
        return System.currentTimeMillis() - lastCacheByTypeUpdate > CACHE_EXPIRE_TIME;
    }

    private void refreshCacheByType() {
        try {
            java.util.List<Record> rows = Db.query("SELECT plan, purchase_type, price FROM vip_price_config WHERE is_active = 1");
            Map<String, Integer> newCache = new HashMap<>();
            for (Record row : rows) {
                String planVal = row.getStr("plan");
                String purchaseType = row.getStr("purchase_type");
                Integer price = row.getInt("price");
                if (planVal != null && purchaseType != null && price != null) {
                    newCache.put(planVal + "|" + purchaseType, price);
                }
            }
            priceCacheByType.clear();
            priceCacheByType.putAll(newCache);
            lastCacheByTypeUpdate = System.currentTimeMillis();
            LogKit.debug("价格缓存(含购买方式)已刷新，共加载 " + newCache.size() + " 个配置");
        } catch (Exception e) {
            LogKit.error("刷新价格缓存(含购买方式)失败", e);
        }
    }

    private Integer queryPriceFromDatabase(String plan, String purchaseType) {
        try {
            Record record = Db.queryFirst(
                "SELECT price FROM vip_price_config WHERE plan = ? AND purchase_type = ? AND is_active = 1",
                plan, purchaseType
            );
            return record != null ? record.getInt("price") : null;
        } catch (Exception e) {
            LogKit.error("从数据库查询价格失败(plan+type)", e);
            return null;
        }
    }

    private int getDefaultPrice(String plan, String purchaseType) {
        if ("monthly".equals(plan)) {
            return "subscribe".equals(purchaseType) ? 990 : 1590;
        } else if ("yearly".equals(plan)) {
            return "subscribe".equals(purchaseType) ? 9900 : 15900;
        }
        // 默认回落到月度一次性
        return 1590;
    }

    /**
     * 获取默认价格
     */
    private int getDefaultPrice(String plan) {
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                return 990; // 9.9元
            case VipOrder.PLAN_YEARLY:
                return 9900; // 99元
            default:
                return 990;
        }
    }

    /**
     * 检查缓存是否过期
     */
    private boolean isCacheExpired() {
        return System.currentTimeMillis() - lastCacheUpdate > CACHE_EXPIRE_TIME;
    }

    /**
     * 刷新缓存
     */
    private void refreshCache() {
        try {
            List<Record> configs = Db.query("SELECT plan, price FROM vip_price_config WHERE is_active = 1");

            Map<String, Integer> newCache = new HashMap<>();
            for (Record config : configs) {
                newCache.put(config.getStr("plan"), config.getInt("price"));
            }

            priceCache.clear();
            priceCache.putAll(newCache);
            lastCacheUpdate = System.currentTimeMillis();

            LogKit.debug("价格缓存已刷新，共加载 " + newCache.size() + " 个配置");

        } catch (Exception e) {
            LogKit.error("刷新价格缓存失败", e);
        }
    }

    /**
     * 清除价格缓存
     */
    public void clearCache() {
        priceCache.clear();
        lastCacheUpdate = 0;
        LogKit.info("价格缓存已清除");
    }

    /**
     * 清除促销缓存
     */
    public void clearPromotionCache() {
        promotionCache.clear();
        LogKit.info("促销缓存已清除");
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("price_cache_size", priceCache.size());
        stats.put("promotion_cache_size", promotionCache.size());
        stats.put("last_cache_update", lastCacheUpdate);
        stats.put("cache_expired", isCacheExpired());
        return stats;
    }
}
