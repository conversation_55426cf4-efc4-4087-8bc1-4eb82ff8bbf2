package com.sandu.xinye.api.v2.promotion;

import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.RateLimitInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.monitor.PromotionEventMonitor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 推广事件上报控制器
 * 包含事件上报和统计功能
 */
public class PromotionController extends AppController {

    /**
     * 推广事件上报接口
     * POST /api/v2/promotion/report
     * 
     * 请求体：
     * {
     *   "actionType": 1|2|3,  // 1-注册绑定，2-首次有效打印，3-VIP订阅
     *   "promotionCode": "xxx" // actionType=1且未绑定时必填
     * }
     */
    @Before(RateLimitInterceptor.class)
    public void report() {
        LogKit.info("推广事件上报接口开始--------------------");
        
        try {
            // 获取参数
            Integer actionType = getParaToInt("actionType");
            String promotionCode = getPara("promotionCode");
            
            // 参数校验
            if (actionType == null || (actionType != 1 && actionType != 2 && actionType != 3)) {
                renderJson(RetKit.fail("actionType必须为1、2或3"));
                return;
            }
            
            User user = getUser();
            if (user == null) {
                renderJson(RetKit.fail("用户未登录"));
                return;
            }
            
            // 调用服务处理
            RetKit ret = PromotionService.me.reportEvent(user, actionType, promotionCode, getIpAddress());
            renderJson(ret);
            
        } catch (Exception e) {
            LogKit.error("推广事件上报接口异常：" + e.getMessage(), e);
            renderJson(RetKit.fail("系统异常，请稍后重试"));
        } finally {
            LogKit.info("推广事件上报接口结束--------------------");
        }
    }

    /**
     * 获取实时统计信息
     * GET /api/v2/promotion/stats
     */
    public void stats() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 内存统计
            result.put("runtime_stats", PromotionEventMonitor.me.getCurrentStats());
            
            // 数据库统计
            Map<String, Object> dbStats = getDatabaseStats();
            result.put("database_stats", dbStats);
            
            renderJson(RetKit.ok(result));
            
        } catch (Exception e) {
            LogKit.error("获取推广事件统计失败: " + e.getMessage(), e);
            renderJson(RetKit.fail("获取统计信息失败"));
        }
    }

    /**
     * 重置运行时统计计数器
     * POST /api/v2/promotion/reset-stats
     */
    public void resetStats() {
        try {
            PromotionEventMonitor.me.resetCounters();
            renderJson(RetKit.ok("统计计数器已重置"));
        } catch (Exception e) {
            LogKit.error("重置统计计数器失败: " + e.getMessage(), e);
            renderJson(RetKit.fail("重置失败"));
        }
    }

    /**
     * 打印详细统计到日志
     * POST /api/v2/promotion/log-stats
     */
    public void logStats() {
        try {
            PromotionEventMonitor.me.logStatistics();
            PromotionEventMonitor.me.logDatabaseStatistics();
            renderJson(RetKit.ok("统计信息已输出到日志"));
        } catch (Exception e) {
            LogKit.error("输出统计日志失败: " + e.getMessage(), e);
            renderJson(RetKit.fail("输出日志失败"));
        }
    }

    /**
     * 获取详细的数据库统计
     */
    private Map<String, Object> getDatabaseStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 按事件类型统计
            List<Record> typeStats = Db.find(
                "SELECT action_type, COUNT(*) as total, SUM(success) as success_count " +
                "FROM promotion_event_report GROUP BY action_type ORDER BY action_type"
            );
            
            Map<String, Object> typeStatsMap = new HashMap<>();
            for (Record record : typeStats) {
                int actionType = record.getInt("action_type");
                int total = record.getInt("total");
                int successCount = record.getInt("success_count");
                double rate = total > 0 ? (double) successCount / total * 100 : 0;
                
                Map<String, Object> typeStat = new HashMap<>();
                typeStat.put("total", total);
                typeStat.put("success", successCount);
                typeStat.put("failed", total - successCount);
                typeStat.put("success_rate", Math.round(rate * 100.0) / 100.0);
                
                String typeName = getActionTypeName(actionType);
                typeStatsMap.put(typeName, typeStat);
            }
            stats.put("by_type", typeStatsMap);
            
            // 最近24小时统计
            Record recentStats = Db.findFirst(
                "SELECT COUNT(*) as total, SUM(success) as success_count " +
                "FROM promotion_event_report WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)"
            );
            
            if (recentStats != null) {
                int recentTotal = recentStats.getInt("total");
                int recentSuccess = recentStats.getInt("success_count");
                double recentRate = recentTotal > 0 ? (double) recentSuccess / recentTotal * 100 : 0;
                
                Map<String, Object> recent24h = new HashMap<>();
                recent24h.put("total", recentTotal);
                recent24h.put("success", recentSuccess);
                recent24h.put("failed", recentTotal - recentSuccess);
                recent24h.put("success_rate", Math.round(recentRate * 100.0) / 100.0);
                stats.put("recent_24h", recent24h);
            }
            
            // 总体统计
            Record totalStats = Db.findFirst(
                "SELECT COUNT(*) as total, SUM(success) as success_count " +
                "FROM promotion_event_report"
            );
            
            if (totalStats != null) {
                int total = totalStats.getInt("total");
                int success = totalStats.getInt("success_count");
                double rate = total > 0 ? (double) success / total * 100 : 0;
                
                Map<String, Object> overall = new HashMap<>();
                overall.put("total", total);
                overall.put("success", success);
                overall.put("failed", total - success);
                overall.put("success_rate", Math.round(rate * 100.0) / 100.0);
                stats.put("overall", overall);
            }
            
        } catch (Exception e) {
            LogKit.error("获取数据库统计失败: " + e.getMessage());
            stats.put("error", "数据库查询失败: " + e.getMessage());
        }
        
        return stats;
    }

    /**
     * 获取事件类型名称
     */
    private String getActionTypeName(int actionType) {
        switch (actionType) {
            case 1: return "register_bind";
            case 2: return "first_print";
            case 3: return "vip_subscribe";
            default: return "unknown_" + actionType;
        }
    }
}
