package com.sandu.xinye.common.itask;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.cron4j.ITask;
import com.sandu.xinye.common.service.VipAutoRenewService;

/**
 * 订阅续费扫描定时任务（每小时）
 */
public class VipSubscriptionRenewTask implements ITask {

    @Override
    public void run() {
        LogKit.info("——————订阅续费扫描任务开始——————");
        try {
            int success = VipAutoRenewService.me.scanAndRenew(100);
            LogKit.info("订阅续费扫描结束，成功续费：" + success);
        } catch (Exception e) {
            LogKit.error("订阅续费扫描任务异常", e);
        }
        LogKit.info("——————订阅续费扫描任务结束——————");
    }

    @Override
    public void stop() {
        LogKit.info("订阅续费扫描任务停止");
    }
}

