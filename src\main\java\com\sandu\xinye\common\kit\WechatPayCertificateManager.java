package com.sandu.xinye.common.kit;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.redis.Cache;
import com.jfinal.plugin.redis.Redis;

import java.io.ByteArrayInputStream;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 微信支付平台证书管理器
 * 负责管理微信支付平台证书的下载、缓存、更新
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class WechatPayCertificateManager {
    
    public static final WechatPayCertificateManager me = new WechatPayCertificateManager();
    
    // 证书缓存
    private final Map<String, X509Certificate> certificateCache = new ConcurrentHashMap<>();
    
    // 定时更新任务执行器
    private ScheduledExecutorService scheduler;
    
    // Redis缓存key前缀
    private static final String CERT_CACHE_PREFIX = "wechat:pay:cert:";
    
    // 证书更新间隔（12小时）
    private static final long UPDATE_INTERVAL = 12;
    
    /**
     * 初始化证书管理器
     */
    public void init() {
        try {
            LogKit.info("初始化微信支付证书管理器");
            
            // 加载本地证书或从Redis缓存加载
            loadCertificates();
            
            // 启动定时更新任务
            startUpdateScheduler();
            
            LogKit.info("微信支付证书管理器初始化成功");
        } catch (Exception e) {
            LogKit.error("初始化证书管理器失败", e);
        }
    }
    
    /**
     * 获取平台证书
     * 
     * @param serial 证书序列号
     * @return 证书对象，如果不存在返回null
     */
    public X509Certificate getCertificate(String serial) {
        if (StrKit.isBlank(serial)) {
            return null;
        }
        
        // 先从内存缓存获取
        X509Certificate cert = certificateCache.get(serial);
        if (cert != null) {
            return cert;
        }
        
        // 从Redis缓存获取
        cert = loadCertificateFromRedis(serial);
        if (cert != null) {
            certificateCache.put(serial, cert);
            return cert;
        }
        
        // 如果都没有，尝试下载
        downloadAndCacheCertificate(serial);
        
        return certificateCache.get(serial);
    }
    
    /**
     * 加载证书
     * 优先从配置文件加载，然后从Redis缓存加载
     */
    private void loadCertificates() {
        try {
            // 从配置文件加载默认证书（如果有）
            String defaultCertPath = PropKit.get("wechat.pay.cert.path");
            if (StrKit.notBlank(defaultCertPath)) {
                // TODO: 从文件加载证书
                LogKit.info("从配置文件加载证书: " + defaultCertPath);
            }
            
            // 对于开发环境，创建一个模拟证书
            if (isDevelopmentMode()) {
                createMockCertificate();
            }
            
        } catch (Exception e) {
            LogKit.error("加载证书失败", e);
        }
    }
    
    /**
     * 从Redis缓存加载证书
     */
    private X509Certificate loadCertificateFromRedis(String serial) {
        try {
            Cache cache = Redis.use();
            String key = CERT_CACHE_PREFIX + serial;
            String certString = cache.get(key);
            
            if (StrKit.notBlank(certString)) {
                return parseCertificate(certString);
            }
        } catch (Exception e) {
            LogKit.error("从Redis加载证书失败", e);
        }
        return null;
    }
    
    /**
     * 下载并缓存证书
     * 实际项目中需要调用微信API下载证书
     */
    private void downloadAndCacheCertificate(String serial) {
        try {
            // TODO: 调用微信API下载证书
            // 这里需要实现微信支付平台证书下载接口
            // https://api.mch.weixin.qq.com/v3/certificates
            
            LogKit.info("尝试下载证书: " + serial);
            
            // 开发模式下跳过
            if (isDevelopmentMode()) {
                LogKit.warn("开发模式下跳过证书下载");
                return;
            }
            
            // 实际实现需要：
            // 1. 构造请求
            // 2. 签名
            // 3. 发送请求
            // 4. 解析响应
            // 5. 缓存证书
            
        } catch (Exception e) {
            LogKit.error("下载证书失败", e);
        }
    }
    
    /**
     * 创建模拟证书（仅用于开发测试）
     */
    private void createMockCertificate() {
        try {
            // 这是一个自签名的测试证书，仅用于开发环境
            String mockCertPem = "-----BEGIN CERTIFICATE-----\n" +
                "MIIDIDCCAggCCQC7mCk5Iu3bYTANBgkqhkiG9w0BAQsFADBSMQswCQYDVQQGEwJD\n" +
                "TjELMAkGA1UECAwCR0QxCzAJBgNVBAcMAkdaMQ0wCwYDVQQKDARURVNUMQ0wCwYD\n" +
                "VQQLDARURVNUMQ0wCwYDVQQDDARURVNUMB4XDTI0MDExNTAwMDAwMFoXDTI1MDEx\n" +
                "NTAwMDAwMFowUjELMAkGA1UEBhMCQ04xCzAJBgNVBAgMAkdEMQswCQYDVQQHDAJH\n" +
                "WjENMAsGA1UECgwEVEVTVDENMAsGA1UECwwEVEVTVDENMAsGA1UEAwwEVEVTVDCC\n" +
                "ASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBANqE8LV7fEpTQeVGquVFCDkx\n" +
                "QwSfd7CTcA+QHb0duNLK0j2z1EqCJACtIL2/2ku+Sxpu3LtRhxlGXuvNSqL3aLKp\n" +
                "Fe0LxHb8V8p8L2A8xOMiE8Nqc8LlF7HwLZ2q3frh/ZJvhJneDWCVtHVmpJ0+3dZV\n" +
                "7GWMBklfMZa9Y/8qfCG9pGWlJT/Y3wKSVGE5uW0UrZNP3pPz7qizYZMXyT3BPZLC\n" +
                "Mw2InNnQRmx8wCqFfZO0eoqaLLXKVMZLBZ3r8QLW9l1YZBBfQKfT5LP0YAwKQYVV\n" +
                "uu+Oqr7SZU8YQhKNU3cEYgfHLY4HI3RP6LbQJaKLPZMZjFC0W0AKfkCxXFK4hqkC\n" +
                "AwEAATANBgkqhkiG9w0BAQsFAAOCAQEAWP1yNPQc0C3/BkY7EsxlP9YV1YZ7fJqN\n" +
                "8RqFt05JHTBzVSx8UuKMbZAaQq5DeNwJLyKPhCtCGJtAwHUs3d5VmIHFYFpYP6kh\n" +
                "TFw8AYsPTwJfM7lXO0xH8BmVVA5tkV2GJmhntRpqxI5bXAYGEJ3hX3fYbhGMz7X7\n" +
                "XqV1JqLaIIVMpC4Y6tTBJ1P8iXpQqH3LH2V5KqVYvhNzt7u1tnH5NM5HvYxp9Z1c\n" +
                "5H3V5PO4jVuZNBpXVR5YLfCH7VxYkg8N7Q5BqIqHczKJV8pypwQqWVK9pY3b5Vjq\n" +
                "Gk5VqZ8NqVNH5qVYQpQ5V5VqVNqVqVqV5VqV5VqV5VqV5VqV5VqV5VqV5A==\n" +
                "-----END CERTIFICATE-----";
            
            X509Certificate mockCert = parseCertificate(mockCertPem);
            String mockSerial = "MOCK_SERIAL_12345";
            
            certificateCache.put(mockSerial, mockCert);
            
            // 缓存到Redis
            try {
                Cache cache = Redis.use();
                String key = CERT_CACHE_PREFIX + mockSerial;
                cache.setex(key, 3600, mockCertPem);
            } catch (Exception e) {
                LogKit.warn("缓存模拟证书到Redis失败: " + e.getMessage());
            }
            
            LogKit.info("创建模拟证书成功，序列号: " + mockSerial);
            
        } catch (Exception e) {
            LogKit.error("创建模拟证书失败", e);
        }
    }
    
    /**
     * 解析证书字符串
     */
    private X509Certificate parseCertificate(String certString) throws Exception {
        // 移除PEM头尾标记
        certString = certString.replace("-----BEGIN CERTIFICATE-----", "")
                              .replace("-----END CERTIFICATE-----", "")
                              .replaceAll("\\s", "");
        
        // Base64解码
        byte[] certBytes = Base64.getDecoder().decode(certString);
        
        // 生成证书对象
        CertificateFactory factory = CertificateFactory.getInstance("X.509");
        return (X509Certificate) factory.generateCertificate(new ByteArrayInputStream(certBytes));
    }
    
    /**
     * 启动定时更新任务
     */
    private void startUpdateScheduler() {
        if (scheduler == null || scheduler.isShutdown()) {
            scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread thread = new Thread(r);
                thread.setName("WechatPayCertificateUpdater");
                thread.setDaemon(true);
                return thread;
            });
            
            // 每12小时更新一次证书
            scheduler.scheduleWithFixedDelay(this::updateCertificates, 
                UPDATE_INTERVAL, UPDATE_INTERVAL, TimeUnit.HOURS);
            
            LogKit.info("证书更新任务已启动，更新间隔: " + UPDATE_INTERVAL + "小时");
        }
    }
    
    /**
     * 更新证书
     */
    private void updateCertificates() {
        try {
            LogKit.info("开始更新微信支付证书");
            
            // TODO: 调用微信API获取最新证书列表
            // 下载新证书
            // 更新缓存
            
            LogKit.info("微信支付证书更新完成");
        } catch (Exception e) {
            LogKit.error("更新证书失败", e);
        }
    }
    
    /**
     * 停止证书管理器
     */
    public void stop() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            LogKit.info("证书更新任务已停止");
        }
        certificateCache.clear();
    }
    
    /**
     * 判断是否为开发模式
     */
    private boolean isDevelopmentMode() {
        String mode = PropKit.get("app.mode", "production");
        return "development".equalsIgnoreCase(mode) || "dev".equalsIgnoreCase(mode);
    }
    
    /**
     * 获取当前缓存的证书数量
     */
    public int getCertificateCount() {
        return certificateCache.size();
    }
}