#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
有效推广事件上报测试脚本
测试代理商平台首次有效打印事件上报接口 (actionType=2)
"""

import hashlib
import time
import json
import requests
import argparse


def calculate_sign(user, secret, timestamp):
    """计算签名"""
    sign_str = user + secret + timestamp
    return hashlib.sha1(sign_str.encode()).hexdigest()


def test_activation_event(user_id):
    """测试有效推广事件上报"""
    
    # 配置参数（与Java代码中的配置保持一致）
    url = "https://support.xpyun.net/api/admin/member/promotionCall"
    secret = "123!@#wqee"
    project = "xprinter"
    timeout = 5
    action_type = 2  # 首次有效打印
    promotion_code = "RIPQFW"  # 使用已知绑定的推广码
    
    # 生成时间戳
    timestamp = str(int(time.time() * 1000))
    
    # 计算签名
    sign = calculate_sign(str(user_id), secret, timestamp)
    
    # 构造请求体
    payload = {
        "user": str(user_id),
        "timestamp": timestamp,
        "sign": sign,
        "promotionCode": promotion_code,
        "project": project,
        "actionType": action_type
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=" * 60)
    print("有效推广事件上报测试 (首次有效打印)")
    print("=" * 60)
    print(f"接口地址: {url}")
    print(f"HTTP方法: POST")
    print(f"超时时间: {timeout}秒")
    print(f"事件类型: {action_type} (首次有效打印)")
    print()
    
    print("请求参数:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    print("请求头:")
    print(json.dumps(headers, indent=2, ensure_ascii=False))
    print()
    
    print("业务说明:")
    print("- 用户必须已绑定推广码")
    print("- 需要近30天内有≥5个不同打印日期")
    print("- 每个用户只能成功上报一次激活事件")
    print("- 实际业务中是在打印记录保存时自动触发")
    print()
    
    try:
        print("发送POST请求...")
        start_time = time.time()
        
        # 发送POST请求
        response = requests.post(
            url, 
            json=payload, 
            headers=headers, 
            timeout=timeout
        )
        
        duration = int((time.time() - start_time) * 1000)
        
        print(f"响应时间: {duration}ms")
        print(f"HTTP状态码: {response.status_code}")
        print()
        
        # 解析响应
        try:
            response_json = response.json()
            print("响应内容:")
            print(json.dumps(response_json, indent=2, ensure_ascii=False))
            
            # 分析业务结果
            print()
            print("=" * 60)
            print("结果分析:")
            print("=" * 60)
            
            if response.status_code == 200:
                data = response_json.get("data")
                message = response_json.get("message", "")
                
                if data is True:
                    print("✅ 有效推广事件上报成功")
                    print("   用户达到激活条件，代理商平台已记录")
                elif data is False:
                    print("❌ 有效推广事件上报失败")
                    print(f"   失败原因: {message}")
                    print("   可能的原因:")
                    print("   1. 用户未绑定推广码")
                    print("   2. 用户未达到激活条件（近30天内<5个打印日期）")
                    print("   3. 用户已上报过激活事件")
                    print("   4. 推广码在代理商平台不存在")
                else:
                    print(f"⚠️  未知状态: data={data}")
                    
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                
        except json.JSONDecodeError:
            print("响应内容 (非JSON):")
            print(response.text)
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def test_via_local_api(user_id):
    """通过本地API测试有效推广事件"""
    
    url = "http://localhost:8090/api/v2/promotion/report"
    
    payload = {
        "actionType": 2
    }
    
    headers = {
        "Content-Type": "application/json",
        # 这里需要添加用户认证信息，具体方式需要根据系统的认证机制调整
        # 例如：Cookie, Authorization 等
    }
    
    print("=" * 60)
    print("通过本地API测试有效推广事件")
    print("=" * 60)
    print(f"接口地址: {url}")
    print(f"用户ID: {user_id}")
    print()
    
    print("请求参数:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    print("注意: 此测试需要用户已登录并绑定推广码")
    print()
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=5)
        
        print(f"HTTP状态码: {response.status_code}")
        print("响应内容:")
        print(response.text)
        
    except Exception as e:
        print(f"请求失败: {e}")


def main():
    parser = argparse.ArgumentParser(description="有效推广事件上报测试脚本")
    parser.add_argument("--user", type=int, default=173972, help="用户ID")
    parser.add_argument("--mode", choices=['direct', 'local'], default='direct', 
                       help="测试模式: direct=直接调用代理商接口, local=通过本地API")
    
    args = parser.parse_args()
    
    if args.mode == 'direct':
        test_activation_event(args.user)
    else:
        test_via_local_api(args.user)
    
    print()
    print("=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    main()