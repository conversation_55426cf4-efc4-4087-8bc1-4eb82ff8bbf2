# 推广事件上报 - 实施计划

## 1. 背景与目标

- 为代理商拉新统计实现三类事件上报（由业务流程自动触发，App 不直接调用）：
  - actionType=1 注册并绑定推广码（注册/绑定）
  - actionType=2 用户激活（规则：近30天内有5个不同日期存在打印成功记录）
  - actionType=3 VIP 订阅/续费（尚未开发，上报时机：支付成功）
- 与代理商平台数据保持口径一致，便于审计与对账。

## 2. 业务范围与已确认决策

- user 字段使用我们系统的 userId（字符串），唯一且稳定
- 签名：sign = sha1(userId + secret + timestamp)
- 事件触发时机：
  - 1：用户通过绑定推广码接口首次绑定成功时，系统触发上报；若历史已绑定但未成功上报过，可在登录等环节补上报
  - 2：用户已绑定推广码，且满足“近30天内有≥5个不同打印日期”的条件时，在达标的当次打印保存后由系统触发上报，且每用户仅上报一次
  - 3：用户订阅/续费支付成功后触发上报，可多次
- 前置条件：actionType=2/3 必须已绑定推广码
- failover：网络异常时不允许绑定成功（failover=false）
- 失败重试：当前不做服务端重试，由系统在业务点重试/补上报

## 3. 代理商平台 API 摘要

- URL（测试）：https://support.xpyun.net/api/admin/member/verifyPromotion
- 方法：POST，Content-Type: application/json
- 请求体：
  {
  "user": "`<userId as string>`",
  "timestamp": "`<millis>`",
  "sign": "sha1(user+secret+timestamp)",
  "promotionCode": "<推广码>",
  "project": "xprinter",
  "actionType": 1|2|3
  }
- 返回：{ status: 200, data: true|false, rel: true }
- 成功判断：以 data=true 为准

## 4. 配置项

在 src/main/resources/common_config.txt：

- promotion.agent.verify.enable=true
- promotion.agent.verify.url=https://support.xpyun.net/api/admin/member/verifyPromotion
- promotion.agent.verify.timeout=5000
- promotion.agent.verify.failover=false
- promotion.agent.verify.secret=<按环境配置>
- promotion.agent.verify.project=xprinter

## 5. 数据模型变更（promotion_event_report）

- 用途：记录“已成功向代理商上报”的事件，作为对外统计口径（与代理商一致）
- 建表字段（建议）：
  - id BIGINT PK
  - user_id INT NOT NULL
  - action_type TINYINT NOT NULL  -- 1/2/3
  - success TINYINT NOT NULL      -- 1 成功 / 0 失败（仅在需要记录失败轨迹时使用；统计只看 success=1）
  - promotion_code VARCHAR(32)    -- 冗余记录当时推广码
  - biz_ref VARCHAR(128)          -- 业务引用：
    - actionType=1：bind:`<promotionCode>` 或空
    - actionType=2：首打对应的 print_record_id
    - actionType=3：订单号/收据号（如有）或 UserGoods.id
  - event_time DATETIME           -- 业务事件发生时间
  - reported_at DATETIME NOT NULL -- 实际上报时间
  - http_status INT               -- HTTP 状态码
  - duration_ms INT               -- 调用耗时
  - third_party_raw_resp TEXT     -- 第三方响应原文/异常概览（不含敏感请求数据）
  - created_at DATETIME NOT NULL
  - updated_at DATETIME NOT NULL
- 索引：
  - INDEX idx_user_action (user_id, action_type)
  - 说明：MySQL 无法对 actionType=1/2 做部分唯一索引；“1/2 仅一次”由应用层保证。
- 统计口径：
  - 注册绑定：count(action_type=1 AND success=1)
  - 首次有效打印：count(action_type=2 AND success=1)
  - VIP 订阅次数：count(action_type=3 AND success=1)
- 审计对齐：以 promotion_event_report 为准；若需要交叉核对：
  - actionType=2 对齐 PrintRecord（首次打印记录）
  - actionType=3 对齐订阅/订单表（当前可先用 UserGoods，后续若有订单表再迭代）

示例迁移（仅示意，实际按规范拆分为 V2 脚本）：
CREATE TABLE promotion_event_report (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  action_type TINYINT NOT NULL,
  success TINYINT NOT NULL DEFAULT 1,
  promotion_code VARCHAR(32),
  biz_ref VARCHAR(128),
  event_time DATETIME,
  reported_at DATETIME NOT NULL,
  http_status INT,
  duration_ms INT,
  third_party_raw_resp TEXT,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  INDEX idx_user_action (user_id, action_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

## 6. 第三方客户端封装（PromotionAgentClient）

- 统一入口：同时支持"推广码存在性验证"和"事件上报"两种功能
- 推广码验证：verifyPromotionCodeExists(userId, promotionCode) 返回 Boolean
- 事件上报：reportEvent(userId, promotionCode, actionType) 返回 ReportResult
- 流程：
  1) 读取配置：enable/url/timeout/secret/project
  2) 组装 payload：user=String.valueOf(userId)、timestamp=System.currentTimeMillis()
  3) 计算 sign=sha1(user+secret+timestamp)
  4) POST JSON，记录开始时间与响应
  5) 解析返回体，得到 data 布尔值
  6) 返回统一结果：{ success:boolean, httpStatus:int, durationMs:int, rawResp:string, msg?:string }
- 日志：info 级记录 actionType、userId、httpStatus、durationMs；不打印 secret；promotionCode 可做脱敏（只打印前后各 2 位）
- 超时/异常：捕获并返回 success=false，httpStatus=0，rawResp 写入异常概览
- 向后兼容：AgentPromotionKit 已重构为委托给 PromotionAgentClient，保持原有 API 不变

## 7. 应用接口与触发点

- 外部接口 POST /api/v2/promotion/report 仅保留用于测试/补上报，正常业务不调用
- 业务触发点（已优化，避免重复调用）：
  - 绑定推广码成功后：由 UserService.bindPromotionCode 内部触发上报 actionType=1
  - 保存打印记录成功后：**统一由 PrintRecordService.saveTemplatePrintRecord 触发**判断并在达标时上报 actionType=2
  - PrintRecordKit.saveTemplatePrintRecord 不再重复触发（已移除），建议业务代码使用 Service 层方法
  - 订阅支付成功后（待开发）：在订单/订阅成功回调中触发上报 actionType=3

- event_time 定义：
  - actionType=1：绑定时间 user.promotion_bind_time
  - actionType=2：达到激活条件的该次打印时间 print_record.print_time；biz_ref=print_record.id
  - actionType=3：订阅/续费支付成功时间；biz_ref=订单号/收据号或 UserGoods.id

- 性能优化（已实现）：
  - 早退优化：同一用户同一天多次打印，仅第一次触发激活统计
  - 时间窗口常量化：使用 BETWEEN 替代 DATE_SUB 函数，更好利用索引
  - 详细日志：记录激活条件检查过程，便于监控和调试

## 8. 日志、监控与限流

- 关键日志：调用耗时、响应状态；错误链路打印简要异常
- 敏感信息：不记录 secret；promotionCode 仅部分显示
- 限流：沿用 RateLimitInterceptor；必要时为该接口单独配置

## 9. 测试计划

- 单元测试：
  - 签名计算（userId+secret+timestamp）
  - 客户端返回解析（data=true/false、异常/超时）
  - 控制器参数校验与分支（未绑定、已绑定、缺少 promotionCode 等）
- 集成测试：
  - 模拟第三方响应，验证 1/2/3 三类路径与幂等
- 联调脚本：Postman/HTTPie 样例

## 10. 数据库索引建议

为确保激活条件查询的高性能，建议添加以下索引：

```sql
-- print_record 表：支持激活条件查询
CREATE INDEX idx_print_activation ON print_record
(userId, printStatus, printTime, deleteTime);

-- promotion_event_report 表：支持幂等性检查
CREATE INDEX idx_promotion_user_action ON promotion_event_report
(user_id, action_type, success);
```

详细的性能优化说明请参考：`推广事件上报-性能优化.md`

## 11. 上线计划与回滚

- 先发 DB 迁移（创建 promotion_event_report + 索引）
- 发服务端：配置 enable=false 验证服务健康
- 打开 enable=true，观察日志与调用成功率
- 性能监控：关注激活查询耗时，必要时调整索引策略
- 回滚：关闭 enable 或回滚版本；数据表保留

## 12. 风险与注意事项

- 第三方不稳定：严格超时与失败处理；不“假成功”
- 幂等：1/2 仅一次由应用逻辑保证，依赖查询+事务；需注意并发
- 性能：高频打印用户可能触发较多激活查询，已通过早退优化缓解
- 审计：以 promotion_event_report 为主；必要时用 biz_ref 对齐 PrintRecord/UserGoods
- 安全：配置与日志脱敏；禁止记录 secret
- 触发点：统一使用 Service 层触发，避免 Kit 层重复调用
