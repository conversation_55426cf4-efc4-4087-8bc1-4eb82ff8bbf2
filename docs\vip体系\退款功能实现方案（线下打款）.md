# VIP退款功能实现方案（线下打款模式）

> 文档版本：v1.0  
> 创建日期：2024-01-15  
> 说明：退款实际打款在线下进行，系统只做流程管理和记录

## 一、业务流程设计

### 1.1 退款流程图

```mermaid
graph TD
    A[用户申请退款] --> B[系统记录申请]
    B --> C{客服审核}
    C -->|通过| D[更新状态：已审核]
    C -->|拒绝| E[更新状态：已拒绝]
    D --> F[生成退款单]
    F --> G[财务线下打款]
    G --> H[财务确认打款]
    H --> I[系统记录打款信息]
    I --> J[更新状态：已完成]
    J --> K[回收VIP权益]
    K --> L[发送通知]
    E --> M[发送拒绝通知]
```

### 1.2 状态流转

```
申请中(pending) -> 已审核(approved) -> 已打款(paid) -> 已完成(completed)
                ↘
                 已拒绝(rejected)
                ↘
                 已取消(cancelled)
```

## 二、数据库设计

### 2.1 退款记录表

```sql
-- 退款主表（记录退款申请和状态）
CREATE TABLE `vip_refund` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `refund_no` VARCHAR(32) NOT NULL COMMENT '退款单号',
  `order_no` VARCHAR(32) NOT NULL COMMENT '原订单号',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `amount` INT NOT NULL COMMENT '退款金额（分）',
  `reason` VARCHAR(500) COMMENT '退款原因',
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending/approved/rejected/paid/completed/cancelled',
  `apply_time` DATETIME NOT NULL COMMENT '申请时间',
  `approve_time` DATETIME COMMENT '审核时间',
  `approve_by` INT COMMENT '审核人ID',
  `approve_remark` VARCHAR(500) COMMENT '审核备注',
  `reject_reason` VARCHAR(500) COMMENT '拒绝原因',
  `offline_pay_time` DATETIME COMMENT '线下打款时间',
  `offline_pay_by` VARCHAR(50) COMMENT '打款操作人',
  `offline_pay_method` VARCHAR(50) COMMENT '打款方式（支付宝/微信/银行转账）',
  `offline_pay_account` VARCHAR(100) COMMENT '收款账号',
  `offline_pay_proof` VARCHAR(500) COMMENT '打款凭证（图片URL）',
  `offline_pay_remark` VARCHAR(500) COMMENT '打款备注',
  `complete_time` DATETIME COMMENT '完成时间',
  `create_time` DATETIME NOT NULL DEFAULT NOW(),
  `update_time` DATETIME NOT NULL DEFAULT NOW() ON UPDATE NOW(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refund_no` (`refund_no`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP退款记录表';

-- 退款操作日志表
CREATE TABLE `vip_refund_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `refund_no` VARCHAR(32) NOT NULL COMMENT '退款单号',
  `operator_id` INT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `operator_role` VARCHAR(20) COMMENT '操作人角色（customer_service/finance/system）',
  `action` VARCHAR(50) NOT NULL COMMENT '操作类型',
  `from_status` VARCHAR(20) COMMENT '原状态',
  `to_status` VARCHAR(20) COMMENT '新状态',
  `remark` VARCHAR(500) COMMENT '操作备注',
  `create_time` DATETIME NOT NULL DEFAULT NOW(),
  PRIMARY KEY (`id`),
  KEY `idx_refund_no` (`refund_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款操作日志表';
```

## 三、接口实现

### 3.1 退款服务实现

```java
// 文件：src/main/java/com/sandu/xinye/common/service/VipRefundService.java
public class VipRefundService {
    
    public static final VipRefundService me = new VipRefundService();
    
    /**
     * 申请退款（用户端）
     */
    public RetKit applyRefund(Integer userId, String orderNo, String reason) {
        // 1. 验证订单
        VipOrder order = VipOrder.dao.findFirst(
            "SELECT * FROM vip_order WHERE order_no = ? AND user_id = ?",
            orderNo, userId
        );
        
        if (order == null) {
            return RetKit.fail("订单不存在");
        }
        
        if (!"paid".equals(order.getStatus())) {
            return RetKit.fail("该订单不支持退款");
        }
        
        // 2. 检查是否已有退款申请
        VipRefund existingRefund = VipRefund.dao.findFirst(
            "SELECT * FROM vip_refund WHERE order_no = ? AND status NOT IN ('rejected', 'cancelled')",
            orderNo
        );
        
        if (existingRefund != null) {
            return RetKit.fail("该订单已有退款申请进行中");
        }
        
        // 3. 检查退款时限（如：购买后7天内）
        long daysSincePaid = TimeKit.daysBetween(order.getPaidTime(), new Date());
        if (daysSincePaid > 7) {
            return RetKit.fail("超过退款时限（7天）");
        }
        
        // 4. 创建退款记录
        String refundNo = generateRefundNo();
        VipRefund refund = new VipRefund();
        refund.setRefundNo(refundNo)
              .setOrderNo(orderNo)
              .setUserId(userId)
              .setAmount(order.getAmount())
              .setReason(reason)
              .setStatus("pending")
              .setApplyTime(new Date())
              .setCreateTime(new Date());
        
        boolean saved = refund.save();
        if (!saved) {
            return RetKit.fail("创建退款申请失败");
        }
        
        // 5. 记录操作日志
        recordRefundLog(refundNo, userId, "用户", "apply", null, "pending", "申请退款：" + reason);
        
        // 6. 发送通知给客服
        notifyCustomerService(refundNo, userId, orderNo, reason);
        
        return RetKit.ok("退款申请已提交，请等待审核")
            .set("refundNo", refundNo)
            .set("status", "pending");
    }
    
    /**
     * 审核退款（客服端）
     */
    public RetKit approveRefund(Integer operatorId, String refundNo, boolean approved, String remark) {
        VipRefund refund = VipRefund.dao.findFirst(
            "SELECT * FROM vip_refund WHERE refund_no = ?", refundNo
        );
        
        if (refund == null) {
            return RetKit.fail("退款申请不存在");
        }
        
        if (!"pending".equals(refund.getStatus())) {
            return RetKit.fail("该退款申请已处理");
        }
        
        String newStatus = approved ? "approved" : "rejected";
        String action = approved ? "approve" : "reject";
        
        // 更新退款状态
        refund.setStatus(newStatus)
              .setApproveTime(new Date())
              .setApproveBy(operatorId)
              .setApproveRemark(remark);
        
        if (!approved) {
            refund.setRejectReason(remark);
        }
        
        boolean updated = refund.update();
        if (!updated) {
            return RetKit.fail("更新退款状态失败");
        }
        
        // 记录操作日志
        recordRefundLog(refundNo, operatorId, "客服", action, "pending", newStatus, remark);
        
        if (approved) {
            // 生成待打款清单，通知财务
            generatePaymentList(refund);
            notifyFinance(refundNo, refund.getUserId(), refund.getAmount());
        } else {
            // 通知用户审核结果
            notifyUser(refund.getUserId(), refundNo, false, remark);
        }
        
        return RetKit.ok(approved ? "退款审核通过" : "退款已拒绝")
            .set("refundNo", refundNo)
            .set("status", newStatus);
    }
    
    /**
     * 确认线下打款（财务端）
     */
    public RetKit confirmOfflinePayment(String operatorName, String refundNo, 
                                       String payMethod, String payAccount, 
                                       String payProof, String remark) {
        VipRefund refund = VipRefund.dao.findFirst(
            "SELECT * FROM vip_refund WHERE refund_no = ?", refundNo
        );
        
        if (refund == null) {
            return RetKit.fail("退款申请不存在");
        }
        
        if (!"approved".equals(refund.getStatus())) {
            return RetKit.fail("退款申请未审核通过");
        }
        
        // 使用事务确保数据一致性
        boolean success = Db.tx(() -> {
            try {
                // 1. 更新退款记录
                refund.setStatus("paid")
                      .setOfflinePayTime(new Date())
                      .setOfflinePayBy(operatorName)
                      .setOfflinePayMethod(payMethod)
                      .setOfflinePayAccount(payAccount)
                      .setOfflinePayProof(payProof)
                      .setOfflinePayRemark(remark)
                      .update();
                
                // 2. 记录操作日志
                recordRefundLog(refundNo, null, operatorName, "offline_pay", 
                              "approved", "paid", 
                              String.format("线下打款：%s，账号：%s", payMethod, payAccount));
                
                // 3. 执行完成处理（回收权益等）
                completeRefund(refund);
                
                return true;
            } catch (Exception e) {
                LogKit.error("确认线下打款失败", e);
                throw new RuntimeException(e);
            }
        });
        
        if (success) {
            // 通知用户退款已到账
            notifyUserRefundCompleted(refund.getUserId(), refundNo, payMethod);
            
            return RetKit.ok("线下打款已确认")
                .set("refundNo", refundNo)
                .set("status", "completed");
        }
        
        return RetKit.fail("确认打款失败");
    }
    
    /**
     * 完成退款（系统自动）
     */
    private void completeRefund(VipRefund refund) {
        // 1. 更新退款状态为已完成
        refund.setStatus("completed")
              .setCompleteTime(new Date())
              .update();
        
        // 2. 更新原订单状态
        Db.update("UPDATE vip_order SET status = 'refunded' WHERE order_no = ?", 
                 refund.getOrderNo());
        
        // 3. 回收用户VIP权益
        revokeUserVip(refund.getUserId(), refund.getOrderNo());
        
        // 4. 记录完成日志
        recordRefundLog(refund.getRefundNo(), null, "系统", "complete", 
                       "paid", "completed", "退款完成，权益已回收");
    }
    
    /**
     * 回收用户VIP权益
     */
    private void revokeUserVip(Integer userId, String orderNo) {
        // 查询订单信息
        VipOrder order = VipOrder.dao.findFirst(
            "SELECT * FROM vip_order WHERE order_no = ?", orderNo
        );
        
        if (order != null && order.getDelivered()) {
            // 根据退款情况调整VIP权益
            User user = User.dao.findById(userId);
            if (user != null) {
                // 如果VIP未到期，立即终止
                if (user.getVipExpireTime() != null && user.getVipExpireTime().after(new Date())) {
                    // 立即降级为免费用户
                    user.setUserTier("FREE")
                        .setVipExpireTime(new Date()) // 设置为当前时间，表示已过期
                        .update();
                    
                    LogKit.info("用户 " + userId + " VIP权益已回收，订单：" + orderNo);
                }
            }
            
            // 清除缓存
            VipCacheService.me.evictUserVipStatus(userId);
        }
    }
    
    /**
     * 查询退款详情
     */
    public RetKit getRefundDetail(String refundNo) {
        VipRefund refund = VipRefund.dao.findFirst(
            "SELECT * FROM vip_refund WHERE refund_no = ?", refundNo
        );
        
        if (refund == null) {
            return RetKit.fail("退款申请不存在");
        }
        
        // 查询操作日志
        List<Record> logs = Db.find(
            "SELECT * FROM vip_refund_log WHERE refund_no = ? ORDER BY create_time DESC",
            refundNo
        );
        
        Map<String, Object> detail = new HashMap<>();
        detail.put("refundInfo", refund.toRecord().getColumns());
        detail.put("logs", logs);
        
        return RetKit.ok("查询成功").set("data", detail);
    }
    
    /**
     * 查询用户退款列表
     */
    public RetKit getUserRefundList(Integer userId, int page, int pageSize) {
        String sql = "SELECT * FROM vip_refund WHERE user_id = ? ORDER BY apply_time DESC LIMIT ?, ?";
        int offset = (page - 1) * pageSize;
        
        List<VipRefund> refunds = VipRefund.dao.find(sql, userId, offset, pageSize);
        
        String countSql = "SELECT COUNT(*) FROM vip_refund WHERE user_id = ?";
        long total = Db.queryLong(countSql, userId);
        
        return RetKit.ok("查询成功")
            .set("list", refunds)
            .set("total", total)
            .set("page", page)
            .set("pageSize", pageSize);
    }
    
    /**
     * 生成退款单号
     */
    private String generateRefundNo() {
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date());
        String random = String.format("%04d", (int)(Math.random() * 10000));
        return "RF" + timestamp + random;
    }
    
    /**
     * 记录退款操作日志
     */
    private void recordRefundLog(String refundNo, Integer operatorId, String operatorName,
                                 String action, String fromStatus, String toStatus, String remark) {
        VipRefundLog log = new VipRefundLog();
        log.setRefundNo(refundNo)
           .setOperatorId(operatorId)
           .setOperatorName(operatorName)
           .setOperatorRole(getOperatorRole(operatorName))
           .setAction(action)
           .setFromStatus(fromStatus)
           .setToStatus(toStatus)
           .setRemark(remark)
           .setCreateTime(new Date());
        
        log.save();
    }
    
    /**
     * 生成待打款清单（供财务使用）
     */
    private void generatePaymentList(VipRefund refund) {
        // 可以生成Excel或PDF格式的打款清单
        Map<String, Object> data = new HashMap<>();
        data.put("refundNo", refund.getRefundNo());
        data.put("orderNo", refund.getOrderNo());
        data.put("userId", refund.getUserId());
        data.put("amount", refund.getAmount() / 100.0); // 转换为元
        data.put("applyTime", refund.getApplyTime());
        data.put("approveTime", refund.getApproveTime());
        
        // TODO: 生成Excel/PDF文件，发送给财务
        LogKit.info("生成待打款清单：" + JsonKit.toJson(data));
    }
}
```

### 3.2 退款控制器实现

```java
// 文件：src/main/java/com/sandu/xinye/api/v2/vip/VipRefundController.java
@Before({AppUserInterceptor.class})
public class VipRefundController extends BaseController {
    
    /**
     * 用户申请退款
     */
    public void apply() {
        User user = getAttr("user");
        String orderNo = getPara("orderNo");
        String reason = getPara("reason");
        
        if (StrKit.isBlank(orderNo)) {
            renderJson(RetKit.fail("订单号不能为空"));
            return;
        }
        
        if (StrKit.isBlank(reason)) {
            renderJson(RetKit.fail("请填写退款原因"));
            return;
        }
        
        RetKit result = VipRefundService.me.applyRefund(
            user.getUserId(), orderNo, reason
        );
        
        renderJson(result);
    }
    
    /**
     * 查询退款详情
     */
    public void detail() {
        String refundNo = getPara("refundNo");
        
        if (StrKit.isBlank(refundNo)) {
            renderJson(RetKit.fail("退款单号不能为空"));
            return;
        }
        
        RetKit result = VipRefundService.me.getRefundDetail(refundNo);
        renderJson(result);
    }
    
    /**
     * 查询用户退款列表
     */
    public void list() {
        User user = getAttr("user");
        int page = getParaToInt("page", 1);
        int pageSize = getParaToInt("pageSize", 10);
        
        RetKit result = VipRefundService.me.getUserRefundList(
            user.getUserId(), page, pageSize
        );
        
        renderJson(result);
    }
    
    /**
     * 取消退款申请
     */
    public void cancel() {
        User user = getAttr("user");
        String refundNo = getPara("refundNo");
        
        VipRefund refund = VipRefund.dao.findFirst(
            "SELECT * FROM vip_refund WHERE refund_no = ? AND user_id = ?",
            refundNo, user.getUserId()
        );
        
        if (refund == null) {
            renderJson(RetKit.fail("退款申请不存在"));
            return;
        }
        
        if (!"pending".equals(refund.getStatus())) {
            renderJson(RetKit.fail("该退款申请已处理，无法取消"));
            return;
        }
        
        refund.setStatus("cancelled").update();
        
        renderJson(RetKit.ok("退款申请已取消"));
    }
}
```

### 3.3 管理端接口

```java
// 文件：src/main/java/com/sandu/xinye/admin/vip/VipRefundAdminController.java
@Before({AdminAuthInterceptor.class})
public class VipRefundAdminController extends BaseController {
    
    /**
     * 客服审核退款
     */
    @Before(RequireRole.class("customer_service"))
    public void approve() {
        AdminUser admin = getAttr("admin");
        String refundNo = getPara("refundNo");
        boolean approved = getParaToBoolean("approved");
        String remark = getPara("remark");
        
        RetKit result = VipRefundService.me.approveRefund(
            admin.getId(), refundNo, approved, remark
        );
        
        renderJson(result);
    }
    
    /**
     * 财务确认线下打款
     */
    @Before(RequireRole.class("finance"))
    public void confirmPayment() {
        AdminUser admin = getAttr("admin");
        String refundNo = getPara("refundNo");
        String payMethod = getPara("payMethod");
        String payAccount = getPara("payAccount");
        String payProof = getPara("payProof");
        String remark = getPara("remark");
        
        RetKit result = VipRefundService.me.confirmOfflinePayment(
            admin.getName(), refundNo, payMethod, payAccount, payProof, remark
        );
        
        renderJson(result);
    }
    
    /**
     * 查询待处理退款列表
     */
    public void pendingList() {
        int page = getParaToInt("page", 1);
        int pageSize = getParaToInt("pageSize", 20);
        String status = getPara("status", "pending");
        
        String sql = "SELECT r.*, u.userName, o.plan " +
                    "FROM vip_refund r " +
                    "LEFT JOIN user u ON r.user_id = u.userId " +
                    "LEFT JOIN vip_order o ON r.order_no = o.order_no " +
                    "WHERE r.status = ? " +
                    "ORDER BY r.apply_time DESC " +
                    "LIMIT ?, ?";
        
        int offset = (page - 1) * pageSize;
        List<Record> list = Db.find(sql, status, offset, pageSize);
        
        String countSql = "SELECT COUNT(*) FROM vip_refund WHERE status = ?";
        long total = Db.queryLong(countSql, status);
        
        renderJson(RetKit.ok()
            .set("list", list)
            .set("total", total)
            .set("page", page)
            .set("pageSize", pageSize));
    }
    
    /**
     * 导出待打款清单
     */
    @Before(RequireRole.class("finance"))
    public void exportPaymentList() {
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        
        String sql = "SELECT r.refund_no, r.order_no, r.amount, " +
                    "r.approve_time, u.userName, u.userPhone " +
                    "FROM vip_refund r " +
                    "LEFT JOIN user u ON r.user_id = u.userId " +
                    "WHERE r.status = 'approved' " +
                    "AND DATE(r.approve_time) BETWEEN ? AND ? " +
                    "ORDER BY r.approve_time";
        
        List<Record> list = Db.find(sql, startDate, endDate);
        
        // 生成Excel文件
        File excel = ExcelKit.export(list, "退款待打款清单");
        renderFile(excel);
    }
}
```

## 四、测试用例

```java
// 文件：src/test/java/com/sandu/xinye/common/service/VipRefundServiceTest.java
public class VipRefundServiceTest {
    
    @Test
    public void testRefundWorkflow() {
        // 1. 用户申请退款
        Integer userId = 123;
        String orderNo = "VIP202401150001";
        String reason = "不需要了";
        
        RetKit applyResult = VipRefundService.me.applyRefund(userId, orderNo, reason);
        assertTrue(applyResult.isOk());
        String refundNo = applyResult.getStr("refundNo");
        
        // 2. 客服审核通过
        Integer csId = 1;
        RetKit approveResult = VipRefundService.me.approveRefund(
            csId, refundNo, true, "符合退款条件"
        );
        assertTrue(approveResult.isOk());
        assertEquals("approved", approveResult.getStr("status"));
        
        // 3. 财务确认打款
        RetKit payResult = VipRefundService.me.confirmOfflinePayment(
            "财务张三", refundNo, "支付宝", "13800138000", 
            "http://proof.jpg", "已转账"
        );
        assertTrue(payResult.isOk());
        assertEquals("completed", payResult.getStr("status"));
        
        // 4. 验证权益已回收
        User user = User.dao.findById(userId);
        assertEquals("FREE", user.getUserTier());
    }
    
    @Test
    public void testRefundReject() {
        // 测试拒绝流程
        Integer userId = 456;
        String orderNo = "VIP202401150002";
        
        RetKit applyResult = VipRefundService.me.applyRefund(userId, orderNo, "测试");
        String refundNo = applyResult.getStr("refundNo");
        
        RetKit rejectResult = VipRefundService.me.approveRefund(
            1, refundNo, false, "不符合退款条件"
        );
        
        assertEquals("rejected", rejectResult.getStr("status"));
    }
}
```

## 五、注意事项

### 5.1 关键点

1. **线下打款确认**：财务完成线下转账后，必须在系统中确认，系统才会回收用户权益
2. **权益回收时机**：在财务确认打款后立即回收，避免用户继续使用VIP服务
3. **审计日志**：所有操作都要记录日志，便于追溯和对账
4. **通知机制**：每个关键节点都要通知相关人员

### 5.2 安全考虑

1. **权限控制**：
   - 用户只能查看和申请自己的退款
   - 客服只能审核，不能确认打款
   - 财务才能确认打款

2. **防重复退款**：
   - 一个订单只能有一个进行中的退款申请
   - 已退款的订单不能再次申请

3. **金额安全**：
   - 退款金额不能超过订单金额
   - 金额记录使用分为单位，避免小数计算误差

### 5.3 监控指标

```java
// 退款相关监控指标
public class RefundMonitor {
    
    // 退款申请数
    private static final Counter refundApplyCounter = Counter.builder("vip.refund.apply")
        .description("退款申请数量")
        .register(registry);
    
    // 退款审核通过率
    private static final Gauge refundApprovalRate = Gauge.builder("vip.refund.approval.rate", () -> {
        // 计算审核通过率
        return calculateApprovalRate();
    }).register(registry);
    
    // 退款处理时长
    private static final Timer refundProcessTime = Timer.builder("vip.refund.process.time")
        .description("退款处理时长")
        .register(registry);
}
```

## 六、上线检查清单

- [ ] 数据库表已创建
- [ ] 退款状态流转正常
- [ ] 权限控制生效
- [ ] 线下打款确认功能正常
- [ ] 权益回收机制正常
- [ ] 通知发送正常
- [ ] 审计日志记录完整
- [ ] 测试用例全部通过
- [ ] 监控指标正常上报
- [ ] 财务对接培训完成