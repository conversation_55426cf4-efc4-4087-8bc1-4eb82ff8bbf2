-- VIP退款功能数据库设计
-- 创建退款相关的数据表和索引

-- 1. 创建VIP退款表
CREATE TABLE IF NOT EXISTS `vip_refund` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '退款ID',
  `refund_no` VARCHAR(32) NOT NULL COMMENT '退款单号',
  `order_no` VARCHAR(32) NOT NULL COMMENT '原订单号',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `refund_amount` INT NOT NULL COMMENT '退款金额(分)',
  `reason` VARCHAR(50) NOT NULL COMMENT '退款原因',
  `description` VARCHAR(500) NULL COMMENT '退款描述',
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '退款状态',
  `wechat_refund_id` VARCHAR(64) NULL COMMENT '微信退款单号',
  `admin_id` INT NULL COMMENT '处理管理员ID',
  `admin_remark` VARCHAR(500) NULL COMMENT '管理员备注',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  `update_time` DATETIME NOT NULL COMMENT '更新时间',
  `process_time` DATETIME NULL COMMENT '处理时间',
  `success_time` DATETIME NULL COMMENT '退款成功时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refund_no` (`refund_no`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP退款表';

-- 2. 为vip_order表添加退款相关字段
ALTER TABLE `vip_order` 
ADD COLUMN `refund_status` VARCHAR(20) NULL COMMENT '退款状态' AFTER `status`,
ADD COLUMN `refund_amount` INT NULL COMMENT '已退款金额(分)' AFTER `refund_status`,
ADD COLUMN `refund_time` DATETIME NULL COMMENT '退款时间' AFTER `refund_amount`;

-- 3. 创建退款日志表（记录退款操作历史）
CREATE TABLE IF NOT EXISTS `vip_refund_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `refund_no` VARCHAR(32) NOT NULL COMMENT '退款单号',
  `action` VARCHAR(50) NOT NULL COMMENT '操作类型',
  `old_status` VARCHAR(20) NULL COMMENT '原状态',
  `new_status` VARCHAR(20) NULL COMMENT '新状态',
  `operator_id` INT NULL COMMENT '操作员ID',
  `operator_type` VARCHAR(20) NOT NULL COMMENT '操作员类型(user/admin/system)',
  `remark` VARCHAR(500) NULL COMMENT '操作备注',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_refund_no` (`refund_no`),
  KEY `idx_action` (`action`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP退款操作日志表';

-- 4. 创建退款统计视图
CREATE OR REPLACE VIEW `vip_refund_stats` AS
SELECT 
    DATE(create_time) as refund_date,
    COUNT(*) as total_refunds,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_refunds,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_refunds,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_refunds,
    COUNT(CASE WHEN status = 'success' THEN 1 END) as success_refunds,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_refunds,
    SUM(CASE WHEN status = 'success' THEN refund_amount ELSE 0 END) as total_refund_amount,
    AVG(CASE WHEN status = 'success' THEN refund_amount ELSE NULL END) as avg_refund_amount
FROM vip_refund 
GROUP BY DATE(create_time)
ORDER BY refund_date DESC;

-- 5. 插入退款原因配置数据
CREATE TABLE IF NOT EXISTS `vip_refund_reason` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '原因ID',
  `reason_code` VARCHAR(50) NOT NULL COMMENT '原因代码',
  `reason_name` VARCHAR(100) NOT NULL COMMENT '原因名称',
  `description` VARCHAR(200) NULL COMMENT '原因描述',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_reason_code` (`reason_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP退款原因配置表';

-- 插入默认退款原因
INSERT INTO `vip_refund_reason` (`reason_code`, `reason_name`, `description`, `sort_order`, `create_time`) VALUES
('user_request', '用户主动申请', '用户因个人原因申请退款', 1, NOW()),
('system_error', '系统错误', '因系统故障导致的退款', 2, NOW()),
('duplicate_payment', '重复支付', '用户重复支付导致的退款', 3, NOW()),
('service_issue', '服务问题', '因服务质量问题导致的退款', 4, NOW()),
('feature_unavailable', '功能不可用', '承诺功能无法提供', 5, NOW()),
('user_dissatisfaction', '用户不满意', '用户对服务不满意', 6, NOW());

-- 6. 创建退款审核配置表
CREATE TABLE IF NOT EXISTS `vip_refund_config` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` VARCHAR(50) NOT NULL COMMENT '配置键',
  `config_value` VARCHAR(200) NOT NULL COMMENT '配置值',
  `description` VARCHAR(200) NULL COMMENT '配置描述',
  `update_time` DATETIME NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP退款配置表';

-- 插入默认配置
INSERT INTO `vip_refund_config` (`config_key`, `config_value`, `description`, `update_time`) VALUES
('refund_period_days', '7', '退款申请时效（天）', NOW()),
('auto_approve_amount', '0', '自动审批金额阈值（分，0表示不自动审批）', NOW()),
('require_admin_approval', '1', '是否需要管理员审批（1是0否）', NOW()),
('max_refund_per_user_per_day', '3', '每用户每日最大退款申请次数', NOW()),
('refund_processing_timeout_hours', '24', '退款处理超时时间（小时）', NOW());

-- 7. 创建存储过程：自动处理超时退款
DELIMITER //
CREATE PROCEDURE ProcessTimeoutRefunds()
BEGIN
    DECLARE timeout_hours INT DEFAULT 24;
    DECLARE affected_rows INT DEFAULT 0;
    
    -- 获取超时配置
    SELECT config_value INTO timeout_hours 
    FROM vip_refund_config 
    WHERE config_key = 'refund_processing_timeout_hours';
    
    -- 将超时的处理中退款标记为失败
    UPDATE vip_refund 
    SET status = 'failed', 
        admin_remark = CONCAT(IFNULL(admin_remark, ''), '; 系统自动处理：超时失败'),
        update_time = NOW()
    WHERE status = 'processing' 
    AND process_time IS NOT NULL 
    AND process_time < DATE_SUB(NOW(), INTERVAL timeout_hours HOUR);
    
    SET affected_rows = ROW_COUNT();
    
    -- 记录处理结果
    INSERT INTO vip_refund_log (refund_no, action, old_status, new_status, operator_type, remark, create_time)
    SELECT refund_no, 'timeout_process', 'processing', 'failed', 'system', 
           CONCAT('自动处理超时退款，超时时间：', timeout_hours, '小时'), NOW()
    FROM vip_refund 
    WHERE status = 'failed' 
    AND admin_remark LIKE '%系统自动处理：超时失败%'
    AND update_time >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);
    
    SELECT CONCAT('已处理 ', affected_rows, ' 个超时退款') as result;
END //
DELIMITER ;

-- 8. 创建触发器：记录退款状态变更日志
DELIMITER //
CREATE TRIGGER vip_refund_status_log 
AFTER UPDATE ON vip_refund
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO vip_refund_log (refund_no, action, old_status, new_status, operator_type, remark, create_time)
        VALUES (NEW.refund_no, 'status_change', OLD.status, NEW.status, 'system', 
                CONCAT('状态变更：', OLD.status, ' -> ', NEW.status), NOW());
    END IF;
END //
DELIMITER ;

-- 9. 创建索引优化查询性能
CREATE INDEX `idx_refund_user_status` ON `vip_refund` (`user_id`, `status`);
CREATE INDEX `idx_refund_create_status` ON `vip_refund` (`create_time`, `status`);
CREATE INDEX `idx_refund_amount_status` ON `vip_refund` (`refund_amount`, `status`);

-- 10. 权限和安全设置
-- 创建退款管理员角色（如果有权限系统的话）
-- INSERT INTO admin_role (role_name, description, permissions) VALUES 
-- ('refund_manager', '退款管理员', 'refund:view,refund:approve,refund:reject');

-- 使用说明：
-- 1. 执行此脚本前请备份数据库
-- 2. 根据实际业务需求调整配置参数
-- 3. 定期执行 ProcessTimeoutRefunds() 存储过程清理超时退款
-- 4. 监控 vip_refund_stats 视图了解退款趋势
-- 5. 可根据业务需要扩展退款原因和配置项
