package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.sandu.xinye.common.kit.IdempotentKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.VipOrder;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * VIP订单幂等性服务
 * 增强订单创建的幂等性控制，防止重复下单
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class VipOrderIdempotentService {
    
    public static final VipOrderIdempotentService me = new VipOrderIdempotentService();
    
    // 本地锁（作为Redis的降级方案）
    private final ConcurrentHashMap<String, Long> localLocks = new ConcurrentHashMap<>();
    
    // 本地锁过期时间（30分钟）
    private static final long LOCAL_LOCK_EXPIRE = 30 * 60 * 1000;
    
    /**
     * 创建订单（带完整幂等性控制）
     * 
     * @param userId 用户ID
     * @param plan 套餐类型
     * @param purchaseType 购买类型
     * @param clientKey 客户端幂等键（可选）
     * @return 订单创建结果
     */
    public RetKit createOrderWithIdempotent(Integer userId, String plan, String purchaseType, String clientKey) {
        // 1. 生成幂等键
        String idempotentKey;
        if (StrKit.notBlank(clientKey)) {
            // 使用客户端提供的幂等键
            idempotentKey = "client:" + clientKey;
        } else {
            // 生成基于业务参数的幂等键
            idempotentKey = IdempotentKit.generateOrderKey(userId, plan, purchaseType);
        }
        
        LogKit.info("订单创建幂等键: " + idempotentKey);
        
        // 2. 检查幂等性
        String existingValue = IdempotentKit.getValue(idempotentKey);
        if (existingValue != null) {
            if (IdempotentKit.isProcessing(idempotentKey)) {
                LogKit.warn("订单正在创建中，拒绝重复请求: " + idempotentKey);
                return RetKit.fail("订单正在创建中，请稍后查询结果");
            }
            
            // 已经创建过，返回已存在的订单
            LogKit.info("订单已存在，返回缓存结果: " + existingValue);
            VipOrder order = VipOrder.dao.findFirst(
                "SELECT * FROM vip_order WHERE order_no = ?", existingValue
            );
            
            if (order != null) {
                return buildOrderResponse(order, plan, purchaseType, false);
            }
        }
        
        // 3. 尝试获取幂等锁
        if (!IdempotentKit.tryLock(idempotentKey, "processing", 300)) {
            // 使用本地锁降级
            if (!tryLocalLock(idempotentKey)) {
                LogKit.warn("获取幂等锁失败: " + idempotentKey);
                return RetKit.fail("系统繁忙，请稍后重试");
            }
        }
        
        try {
            // 4. 再次检查是否有未支付订单（双重检查）
            VipOrder unpaidOrder = findRecentUnpaidOrder(userId, plan);
            if (unpaidOrder != null && !isOrderExpired(unpaidOrder)) {
                // 标记幂等完成
                IdempotentKit.markCompleted(idempotentKey, unpaidOrder.getOrderNo());
                LogKit.info("存在未支付订单，返回已有订单: " + unpaidOrder.getOrderNo());
                return buildOrderResponse(unpaidOrder, plan, purchaseType, false);
            }
            
            // 5. 使用事务创建订单
            final VipOrder[] orderHolder = new VipOrder[1];
            boolean success = Db.tx(new IAtom() {
                @Override
                public boolean run() {
                    try {
                        // 关闭过期订单
                        closeUserExpiredOrders(userId);
                        
                        // 创建新订单
                        VipOrder newOrder = buildOrder(userId, plan, purchaseType);
                        newOrder.set("idempotent_key", idempotentKey); // 存储幂等键
                        
                        if (newOrder.save()) {
                            orderHolder[0] = newOrder;
                            return true;
                        }
                        return false;
                        
                    } catch (Exception e) {
                        LogKit.error("订单创建事务失败", e);
                        return false;
                    }
                }
            });
            
            if (!success || orderHolder[0] == null) {
                throw new RuntimeException("订单创建失败");
            }
            
            VipOrder order = orderHolder[0];
            LogKit.info("订单创建成功: " + order.getOrderNo());
            
            // 6. 标记幂等完成
            IdempotentKit.markCompleted(idempotentKey, order.getOrderNo(), 1800);
            
            // 7. 返回结果
            return buildOrderResponse(order, plan, purchaseType, true);
            
        } catch (Exception e) {
            LogKit.error("创建订单异常", e);
            
            // 释放幂等锁
            IdempotentKit.release(idempotentKey);
            releaseLocalLock(idempotentKey);
            
            return RetKit.fail("订单创建失败: " + e.getMessage());
            
        } finally {
            // 清理本地锁
            releaseLocalLock(idempotentKey);
        }
    }
    
    /**
     * 构建订单对象
     */
    private VipOrder buildOrder(Integer userId, String plan, String purchaseType) {
        VipOrder order = new VipOrder();
        
        // 基本信息
        order.setUserId(userId);
        order.setOrderNo(generateOrderNo());
        order.setPlan(plan);
        order.setPurchaseType(StrKit.notBlank(purchaseType) ? purchaseType : "one_time");
        
        // 价格信息
        int price = VipPriceConfigService.me.calculateActualPrice(plan, userId);
        order.setAmount(price);
        order.setAmountYuan(String.format("%.2f", price / 100.0));
        
        // 状态信息
        order.setStatus(VipOrder.STATUS_CREATED);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        
        // 过期时间（30分钟后）
        Date expireTime = new Date(System.currentTimeMillis() + 30 * 60 * 1000);
        order.setExpireTime(expireTime);
        
        return order;
    }
    
    /**
     * 构建订单响应
     */
    private RetKit buildOrderResponse(VipOrder order, String plan, String purchaseType, boolean isNew) {
        Map<String, Object> payParams = generatePayParams(order);
        
        RetKit result = RetKit.ok(isNew ? "订单创建成功" : "订单已存在")
            .set("orderNo", order.getOrderNo())
            .set("amount", order.getAmountYuan())
            .set("plan", plan)
            .set("purchaseType", purchaseType)
            .set("payParams", payParams)
            .set("isNew", isNew);
        
        if (!isNew) {
            result.set("createTime", order.getCreateTime());
        }
        
        return result;
    }
    
    /**
     * 生成支付参数
     */
    private Map<String, Object> generatePayParams(VipOrder order) {
        try {
            if (WechatPayService.me.isAvailable()) {
                return WechatPayService.me.createAppOrder(order);
            }
        } catch (Exception e) {
            LogKit.error("生成支付参数失败", e);
        }
        
        // 返回模拟参数
        return VipOrderService.me.createMockPayParams(order);
    }
    
    /**
     * 查找最近的未支付订单
     */
    private VipOrder findRecentUnpaidOrder(Integer userId, String plan) {
        return VipOrder.dao.findFirst(
            "SELECT * FROM vip_order WHERE user_id = ? AND plan = ? AND status = ? " +
            "AND create_time > ? ORDER BY create_time DESC LIMIT 1",
            userId, plan, VipOrder.STATUS_CREATED,
            new Date(System.currentTimeMillis() - 30 * 60 * 1000) // 30分钟内
        );
    }
    
    /**
     * 关闭用户的过期订单
     */
    private void closeUserExpiredOrders(Integer userId) {
        try {
            int rows = Db.update(
                "UPDATE vip_order SET status = ?, update_time = ? " +
                "WHERE user_id = ? AND status = ? AND create_time < ?",
                VipOrder.STATUS_CLOSED, new Date(),
                userId, VipOrder.STATUS_CREATED,
                new Date(System.currentTimeMillis() - 30 * 60 * 1000)
            );
            
            if (rows > 0) {
                LogKit.info("关闭过期订单数量: " + rows);
            }
        } catch (Exception e) {
            LogKit.error("关闭过期订单失败", e);
        }
    }
    
    /**
     * 检查订单是否过期
     */
    private boolean isOrderExpired(VipOrder order) {
        if (order == null) {
            return true;
        }
        
        Date createTime = order.getCreateTime();
        if (createTime != null) {
            long diffMinutes = (System.currentTimeMillis() - createTime.getTime()) / (1000 * 60);
            return diffMinutes > 30;
        }
        
        return false;
    }
    
    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return String.format("VIP%d%04d", 
            System.currentTimeMillis(), 
            (int)(Math.random() * 10000));
    }
    
    /**
     * 尝试获取本地锁（降级方案）
     */
    private boolean tryLocalLock(String key) {
        Long now = System.currentTimeMillis();
        Long existing = localLocks.putIfAbsent(key, now);
        
        if (existing == null) {
            return true;
        }
        
        // 检查是否过期
        if (now - existing > LOCAL_LOCK_EXPIRE) {
            localLocks.put(key, now);
            return true;
        }
        
        return false;
    }
    
    /**
     * 释放本地锁
     */
    private void releaseLocalLock(String key) {
        localLocks.remove(key);
    }
    
    /**
     * 清理过期的本地锁（定期执行）
     */
    public void cleanupLocalLocks() {
        long now = System.currentTimeMillis();
        localLocks.entrySet().removeIf(entry -> 
            now - entry.getValue() > LOCAL_LOCK_EXPIRE
        );
    }
}