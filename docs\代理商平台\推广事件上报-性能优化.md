# 推广事件上报 - 性能优化指南

## 1. 数据库索引建议

### 1.1 print_record 表索引优化

为支持激活条件查询的高性能，建议添加以下复合索引：

```sql
-- 核心复合索引：支持激活条件查询
CREATE INDEX idx_print_activation ON print_record 
(userId, printStatus, printTime, deleteTime);

-- 或者更精确的版本（如果 deleteTime IS NULL 是主要查询条件）
CREATE INDEX idx_print_activation_v2 ON print_record 
(userId, printStatus, printTime) 
WHERE deleteTime IS NULL;
```

### 1.2 promotion_event_report 表索引

```sql
-- 支持幂等性检查
CREATE INDEX idx_promotion_user_action ON promotion_event_report 
(user_id, action_type, success);
```

### 1.3 索引使用说明

- **激活查询**：`WHERE userId = ? AND printStatus = ? AND printTime BETWEEN ? AND ?` 可以充分利用复合索引
- **今日打印检查**：同样利用上述索引，查询效率高
- **幂等检查**：`WHERE user_id = ? AND action_type = ? AND success = 1` 利用 promotion_event_report 索引

## 2. 性能优化策略

### 2.1 早退优化（已实现）

在统计"近30天不同打印日期"之前，先检查用户今日是否已有打印记录：

```java
// 如果今日已有打印，跳过重复统计
if (hasPrintedToday(userId, printTime)) {
    return; // 避免重复的 DISTINCT 统计
}
```

**效果**：同一用户同一天的多次打印，只有第一次会触发激活统计。

### 2.2 时间窗口常量化（已实现）

```java
// 优化前：使用 MySQL 函数
WHERE printTime >= DATE_SUB(?, INTERVAL 30 DAY)

// 优化后：使用常量边界
Date windowStart = new Date(printTime.getTime() - 30L * 24 * 60 * 60 * 1000);
WHERE printTime BETWEEN ? AND ?
```

**效果**：避免在 WHERE 条件中使用函数，更好地利用索引。

### 2.3 触发点收敛（已实现）

- **统一触发点**：仅在 `PrintRecordService.saveTemplatePrintRecord` 中触发激活检查
- **移除重复**：`PrintRecordKit.saveTemplatePrintRecord` 不再重复调用
- **建议路径**：业务代码优先使用 Service 层方法

## 3. 进一步优化方案（可选）

### 3.1 增加 print_date 列（最佳实践）

```sql
-- 在 print_record 表增加日期列
ALTER TABLE print_record ADD COLUMN print_date DATE 
GENERATED ALWAYS AS (DATE(printTime)) STORED;

-- 建立基于日期的索引
CREATE INDEX idx_print_date_activation ON print_record 
(userId, printStatus, print_date, deleteTime);
```

**优势**：
- `COUNT(DISTINCT print_date)` 比 `COUNT(DISTINCT DATE(printTime))` 性能更好
- 可以利用 print_date 列的索引进行高效 GROUP BY

### 3.2 用户打印日统计表（终极方案）

```sql
-- 创建用户打印日统计表
CREATE TABLE user_print_day (
  user_id INT NOT NULL,
  print_date DATE NOT NULL,
  print_count INT NOT NULL DEFAULT 1,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (user_id, print_date),
  INDEX idx_user_date (user_id, print_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

**使用方式**：
- 每次打印成功后，`INSERT ... ON DUPLICATE KEY UPDATE print_count = print_count + 1`
- 激活查询变为：`SELECT COUNT(*) FROM user_print_day WHERE user_id = ? AND print_date >= ?`
- 查询性能最优，但需要额外的数据维护

## 4. 监控指标

### 4.1 关键性能指标

- **激活查询耗时**：监控 `tryReportActivationOnPrint` 方法执行时间
- **今日打印检查耗时**：监控 `hasPrintedToday` 方法执行时间
- **数据库慢查询**：监控相关 SQL 的执行计划和耗时

### 4.2 业务指标

- **激活触发频率**：每日触发激活检查的次数
- **激活成功率**：达到条件并成功上报的比例
- **重复统计避免率**：通过"今日已打印"优化避免的查询次数

## 5. 部署建议

### 5.1 渐进式部署

1. **第一阶段**：部署当前优化版本（早退 + 时间窗口常量化）
2. **第二阶段**：添加建议的数据库索引
3. **第三阶段**：根据性能监控结果，考虑是否实施 print_date 列或统计表方案

### 5.2 性能测试

- **压测场景**：模拟高频打印用户（每日 50+ 次打印）
- **关注指标**：数据库 CPU、查询响应时间、并发处理能力
- **优化目标**：激活查询耗时 < 50ms，今日检查耗时 < 10ms

## 6. 风险控制

### 6.1 降级策略

如果性能问题严重，可以通过配置开关临时关闭激活检查：

```properties
# 临时关闭激活上报
promotion.activation.check.enable=false
```

### 6.2 异常处理

- 所有数据库查询都有异常捕获，不会影响主业务流程
- 异常情况下记录监控指标，便于问题排查
- 查询异常时采用保守策略（不跳过统计），确保不漏报

## 7. 总结

当前实现已包含主要的性能优化措施：
- ✅ 早退优化（避免重复统计）
- ✅ 时间窗口常量化（更好利用索引）
- ✅ 触发点收敛（避免重复调用）
- ✅ 详细日志和监控

建议优先添加数据库索引，然后根据实际性能表现决定是否需要进一步优化。
