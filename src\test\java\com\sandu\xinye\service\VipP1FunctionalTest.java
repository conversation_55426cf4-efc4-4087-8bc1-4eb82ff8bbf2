package com.sandu.xinye.service;

import com.jfinal.kit.JsonKit;
import com.sandu.xinye.common.kit.*;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.VipOrder;
import com.sandu.xinye.common.service.*;
import com.sandu.xinye.common.task.VipAutoRenewTask;
import org.junit.Before;
import org.junit.Test;
import org.junit.After;
import static org.junit.Assert.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * P1级功能综合测试
 * 测试订单幂等性、退款记录、自动续费、并发控制等核心功能
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class VipP1FunctionalTest {
    
    private static final int TEST_USER_ID = 10001;
    private static final String TEST_PLAN = "monthly";
    
    @Before
    public void setUp() {
        // 初始化测试环境
        System.out.println("========== P1级功能测试开始 ==========");
    }
    
    @After
    public void tearDown() {
        // 清理测试数据
        System.out.println("========== P1级功能测试结束 ==========");
    }
    
    /**
     * 测试订单创建幂等性
     */
    @Test
    public void testOrderIdempotency() {
        System.out.println("\n[测试] 订单创建幂等性");
        
        // 1. 使用相同的幂等键创建多次订单
        String idempotentKey = IdempotentKit.generateRandomKey();
        
        // 第一次创建
        RetKit result1 = VipOrderIdempotentService.me.createOrderWithIdempotent(
            TEST_USER_ID, TEST_PLAN, "one_time", idempotentKey
        );
        
        assertTrue("第一次创建应该成功", result1.success());
        String orderNo1 = result1.getStr("orderNo");
        assertNotNull("订单号不应为空", orderNo1);
        assertTrue("应该是新创建的订单", Boolean.TRUE.equals(result1.get("isNew")));
        
        // 第二次创建（相同幂等键）
        RetKit result2 = VipOrderIdempotentService.me.createOrderWithIdempotent(
            TEST_USER_ID, TEST_PLAN, "one_time", idempotentKey
        );
        
        assertTrue("第二次请求应该返回成功", result2.success());
        String orderNo2 = result2.getStr("orderNo");
        assertEquals("应该返回相同的订单号", orderNo1, orderNo2);
        assertFalse("不应该是新创建的订单", Boolean.TRUE.equals(result2.get("isNew")));
        
        System.out.println("✓ 幂等性测试通过：相同幂等键返回相同订单");
    }
    
    /**
     * 测试并发创建订单
     */
    @Test
    public void testConcurrentOrderCreation() throws Exception {
        System.out.println("\n[测试] 并发订单创建");
        
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        ConcurrentHashMap<String, Integer> orderCount = new ConcurrentHashMap<>();
        
        // 使用相同的幂等键
        String sharedIdempotentKey = IdempotentKit.generateRandomKey();
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int userId = TEST_USER_ID + i;
            executor.submit(() -> {
                try {
                    RetKit result = VipOrderIdempotentService.me.createOrderWithIdempotent(
                        userId, TEST_PLAN, "one_time", sharedIdempotentKey + userId
                    );
                    
                    if (result.success()) {
                        successCount.incrementAndGet();
                        String orderNo = result.getStr("orderNo");
                        orderCount.merge(orderNo, 1, Integer::sum);
                    } else {
                        failCount.incrementAndGet();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        assertTrue("并发测试应在10秒内完成", latch.await(10, TimeUnit.SECONDS));
        executor.shutdown();
        
        // 验证结果
        System.out.println("并发创建结果：成功=" + successCount.get() + ", 失败=" + failCount.get());
        
        // 每个订单号应该只出现一次
        for (Map.Entry<String, Integer> entry : orderCount.entrySet()) {
            assertEquals("每个订单应该只创建一次", 1, entry.getValue().intValue());
        }
        
        System.out.println("✓ 并发创建测试通过：无重复订单");
    }
    
    /**
     * 测试退款申请流程
     */
    @Test
    public void testRefundProcess() {
        System.out.println("\n[测试] 退款申请流程");
        
        // 1. 创建一个已支付订单（模拟）
        String orderNo = "TEST_ORDER_" + System.currentTimeMillis();
        
        // 2. 申请退款
        RetKit applyResult = VipRefundService.me.applyRefund(
            String.valueOf(TEST_USER_ID), orderNo, "测试退款"
        );
        
        if (applyResult.success()) {
            String refundNo = applyResult.getStr("refundNo");
            assertNotNull("退款单号不应为空", refundNo);
            
            // 3. 审批退款（管理员操作）
            RetKit approveResult = VipRefundService.me.approveRefund(
                refundNo, "admin", "同意退款"
            );
            assertTrue("审批应该成功", approveResult.success());
            
            // 4. 处理退款（财务操作）
            RetKit processResult = VipRefundService.me.processRefund(
                refundNo, true, "开始处理退款"
            );
            assertTrue("处理应该成功", processResult.success());
            
            // 5. 完成退款
            Map<String, Object> refundDetail = new HashMap<>();
            refundDetail.put("method", "bank_transfer");
            refundDetail.put("account", "****1234");
            refundDetail.put("amount", 2900);
            
            RetKit completeResult = VipRefundService.me.completeRefund(
                refundNo, "finance", refundDetail
            );
            assertTrue("完成退款应该成功", completeResult.success());
            
            System.out.println("✓ 退款流程测试通过：申请→审批→处理→完成");
        } else {
            System.out.println("⚠ 退款测试跳过：订单不存在或状态不符");
        }
    }
    
    /**
     * 测试自动续费任务
     */
    @Test
    public void testAutoRenewTask() {
        System.out.println("\n[测试] 自动续费任务");
        
        // 创建任务实例
        VipAutoRenewTask task = new VipAutoRenewTask();
        
        // 获取任务状态
        Map<String, Object> statusBefore = VipAutoRenewTask.getTaskStatus();
        System.out.println("任务状态（执行前）: " + JsonKit.toJson(statusBefore));
        
        // 执行任务
        task.execute();
        
        // 获取执行后状态
        Map<String, Object> statusAfter = VipAutoRenewTask.getTaskStatus();
        System.out.println("任务状态（执行后）: " + JsonKit.toJson(statusAfter));
        
        assertNotNull("最后执行时间应该更新", statusAfter.get("lastExecutionTime"));
        
        System.out.println("✓ 自动续费任务测试通过");
    }
    
    /**
     * 测试并发控制机制
     */
    @Test
    public void testConcurrencyControl() throws Exception {
        System.out.println("\n[测试] 并发控制机制");
        
        // 1. 测试分布式锁
        String lockKey = "test:lock:" + System.currentTimeMillis();
        String requestId1 = "req1";
        String requestId2 = "req2";
        
        boolean lock1 = ConcurrencyControlKit.tryLock(lockKey, requestId1, 5);
        assertTrue("第一次获取锁应该成功", lock1);
        
        boolean lock2 = ConcurrencyControlKit.tryLock(lockKey, requestId2, 5);
        assertFalse("第二次获取锁应该失败", lock2);
        
        boolean release1 = ConcurrencyControlKit.releaseLock(lockKey, requestId1);
        assertTrue("释放锁应该成功", release1);
        
        boolean lock3 = ConcurrencyControlKit.tryLock(lockKey, requestId2, 5);
        assertTrue("释放后重新获取锁应该成功", lock3);
        
        ConcurrencyControlKit.releaseLock(lockKey, requestId2);
        
        // 2. 测试限流
        String resource = "test:rate:" + System.currentTimeMillis();
        int maxRequests = 5;
        int successRequests = 0;
        
        for (int i = 0; i < 10; i++) {
            if (ConcurrencyControlKit.checkRateLimit(resource, maxRequests, 60)) {
                successRequests++;
            }
        }
        
        assertEquals("限流应该只允许5个请求通过", maxRequests, successRequests);
        
        // 3. 测试熔断器
        String breakerName = "test:breaker";
        AtomicInteger callCount = new AtomicInteger(0);
        
        for (int i = 0; i < 10; i++) {
            ConcurrencyControlKit.executeWithCircuitBreaker(
                breakerName,
                () -> {
                    callCount.incrementAndGet();
                    if (callCount.get() <= 5) {
                        throw new RuntimeException("模拟失败");
                    }
                    return "success";
                },
                () -> "fallback"
            );
        }
        
        System.out.println("熔断器测试：总调用=" + callCount.get());
        
        System.out.println("✓ 并发控制机制测试通过");
    }
    
    /**
     * 测试支付回调幂等性
     */
    @Test
    public void testPaymentCallbackIdempotency() {
        System.out.println("\n[测试] 支付回调幂等性");
        
        String transactionId = "WX_TRANS_" + System.currentTimeMillis();
        String callbackKey = IdempotentKit.generateCallbackKey(transactionId);
        
        // 第一次处理
        boolean firstLock = IdempotentKit.tryLock(callbackKey, "processing", 60);
        assertTrue("第一次处理应该成功获取锁", firstLock);
        
        // 模拟处理完成
        IdempotentKit.markCompleted(callbackKey, transactionId);
        
        // 第二次处理（应该识别为已完成）
        boolean isCompleted = IdempotentKit.isCompleted(callbackKey);
        assertTrue("应该识别为已完成", isCompleted);
        
        System.out.println("✓ 支付回调幂等性测试通过");
    }
    
    /**
     * 测试乐观锁更新
     */
    @Test
    public void testOptimisticLocking() {
        System.out.println("\n[测试] 乐观锁更新");
        
        // 模拟SQL（实际测试需要真实数据库）
        String sql = "UPDATE vip_order SET status = ?, version = version + 1 WHERE order_no = ? AND version = ?";
        Object[] params = {"paid", "TEST_ORDER_123", 1};
        
        // 测试重试机制
        boolean success = ConcurrencyControlKit.updateWithOptimisticLock(sql, params, 3);
        
        System.out.println("乐观锁更新结果: " + (success ? "成功" : "失败（预期）"));
        System.out.println("✓ 乐观锁测试完成");
    }
    
    /**
     * 综合压力测试
     */
    @Test
    public void testStressTest() throws Exception {
        System.out.println("\n[测试] 综合压力测试");
        
        int totalRequests = 100;
        int threadPoolSize = 20;
        CountDownLatch latch = new CountDownLatch(totalRequests);
        ExecutorService executor = Executors.newFixedThreadPool(threadPoolSize);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < totalRequests; i++) {
            final int index = i;
            executor.submit(() -> {
                try {
                    // 随机执行不同的操作
                    int operation = index % 4;
                    boolean success = false;
                    
                    switch (operation) {
                        case 0:
                            // 创建订单
                            RetKit result = VipOrderIdempotentService.me.createOrderWithIdempotent(
                                TEST_USER_ID + index, TEST_PLAN, "one_time", null
                            );
                            success = result.success();
                            break;
                            
                        case 1:
                            // 获取锁
                            success = ConcurrencyControlKit.tryLock(
                                "stress:lock:" + (index % 10), 
                                "req" + index, 
                                1
                            );
                            break;
                            
                        case 2:
                            // 限流检查
                            success = ConcurrencyControlKit.checkRateLimit(
                                "stress:rate", 50, 60
                            );
                            break;
                            
                        case 3:
                            // 幂等性检查
                            String key = "stress:idempotent:" + (index % 20);
                            success = IdempotentKit.tryLock(key, "value" + index, 60);
                            break;
                    }
                    
                    if (success) {
                        successCount.incrementAndGet();
                    } else {
                        failCount.incrementAndGet();
                    }
                    
                } catch (Exception e) {
                    failCount.incrementAndGet();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        assertTrue("压力测试应在30秒内完成", latch.await(30, TimeUnit.SECONDS));
        executor.shutdown();
        
        long totalTime = System.currentTimeMillis() - startTime;
        double tps = (double) totalRequests / (totalTime / 1000.0);
        
        System.out.println("压力测试结果：");
        System.out.println("  总请求: " + totalRequests);
        System.out.println("  成功: " + successCount.get());
        System.out.println("  失败: " + failCount.get());
        System.out.println("  耗时: " + totalTime + "ms");
        System.out.println("  TPS: " + String.format("%.2f", tps));
        
        assertTrue("成功率应该大于50%", successCount.get() > totalRequests / 2);
        
        System.out.println("✓ 压力测试通过");
    }
}