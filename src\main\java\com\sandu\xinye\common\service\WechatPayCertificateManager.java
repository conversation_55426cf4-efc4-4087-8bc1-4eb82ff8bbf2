package com.sandu.xinye.common.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.config.RedisConfig;
import com.sandu.xinye.common.kit.WechatPaySignKit;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 微信支付平台证书管理器
 * 负责下载、缓存、自动更新微信支付平台证书
 */
public class WechatPayCertificateManager {
    
    public static final WechatPayCertificateManager me = new WechatPayCertificateManager();
    
    // 证书缓存：序列号 -> 证书
    private final Map<String, X509Certificate> certificateCache = new ConcurrentHashMap<>();
    
    // 定时任务执行器
    private ScheduledExecutorService scheduler;
    
    // 是否已初始化
    private volatile boolean initialized = false;
    
    /**
     * 初始化证书管理器
     */
    public void init() {
        if (initialized) {
            return;
        }
        
        try {
            LogKit.info("初始化微信支付平台证书管理器");
            
            // 开发模式下使用Mock证书
            if (RedisConfig.isDevelopmentMode()) {
                LogKit.warn("开发模式下使用Mock证书");
                createMockCertificate();
                initialized = true;
                return;
            }
            
            // 立即下载一次证书
            downloadCertificates();
            
            // 启动定时任务，每12小时更新一次证书
            scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "WechatPayCertificateUpdater");
                t.setDaemon(true);
                return t;
            });
            
            scheduler.scheduleWithFixedDelay(this::downloadCertificates, 12, 12, TimeUnit.HOURS);
            
            initialized = true;
            LogKit.info("微信支付平台证书管理器初始化完成");
            
        } catch (Exception e) {
            LogKit.error("微信支付平台证书管理器初始化失败", e);
        }
    }
    
    /**
     * 获取证书（根据序列号）
     */
    public X509Certificate getCertificate(String serialNo) {
        if (StrKit.isBlank(serialNo)) {
            return null;
        }
        
        X509Certificate certificate = certificateCache.get(serialNo);
        if (certificate == null) {
            LogKit.warn("未找到序列号为 " + serialNo + " 的证书，尝试重新下载");
            downloadCertificates();
            certificate = certificateCache.get(serialNo);
        }
        
        return certificate;
    }
    
    /**
     * 获取任意一个可用证书（用于验签）
     */
    public X509Certificate getAnyCertificate() {
        if (certificateCache.isEmpty()) {
            downloadCertificates();
        }
        
        return certificateCache.values().stream().findFirst().orElse(null);
    }
    
    /**
     * 下载平台证书
     */
    private void downloadCertificates() {
        try {
            LogKit.info("开始下载微信支付平台证书");
            
            WechatPayConfig config = WechatPayConfig.me;
            if (!config.isConfigValid()) {
                LogKit.warn("微信支付配置不完整，跳过证书下载");
                return;
            }
            
            // 创建HTTP客户端（需要私钥进行签名）
            if (!WechatPayService.me.isAvailable()) {
                LogKit.warn("微信支付服务不可用，跳过证书下载");
                return;
            }
            
            // 构建证书下载URL
            String url = "https://api.mch.weixin.qq.com/v3/certificates";
            
            // 发送GET请求获取证书列表
            WechatPayHttpClient httpClient = new WechatPayHttpClient(config, 
                WechatPaySignKit.loadPrivateKey(config.getPrivateKeyPath()));
            WechatPayResponse response = httpClient.get(url);
            
            if (!response.isSuccess()) {
                LogKit.error("下载微信支付平台证书失败: " + response.getErrorMessage());
                return;
            }
            
            // 解析证书数据
            JSONObject responseData = response.getData();
            if (responseData == null || !responseData.containsKey("data")) {
                LogKit.error("微信支付平台证书响应格式错误");
                return;
            }
            
            JSONArray certificates = responseData.getJSONArray("data");
            int downloadCount = 0;
            
            for (int i = 0; i < certificates.size(); i++) {
                JSONObject certInfo = certificates.getJSONObject(i);
                
                try {
                    String serialNo = certInfo.getString("serial_no");
                    JSONObject encryptCertificate = certInfo.getJSONObject("encrypt_certificate");
                    
                    // 解密证书
                    String certificatePem = decryptCertificate(encryptCertificate, config.getApiV3Key());
                    
                    // 加载证书
                    X509Certificate certificate = WechatPaySignKit.loadCertificate(certificatePem);
                    
                    // 缓存证书
                    certificateCache.put(serialNo, certificate);
                    downloadCount++;
                    
                    LogKit.info("成功下载并缓存证书，序列号: " + serialNo);
                    
                } catch (Exception e) {
                    LogKit.error("处理证书失败", e);
                }
            }
            
            LogKit.info("微信支付平台证书下载完成，共下载 " + downloadCount + " 个证书");
            
        } catch (Exception e) {
            LogKit.error("下载微信支付平台证书异常", e);
        }
    }
    
    /**
     * 解密证书
     */
    private String decryptCertificate(JSONObject encryptCertificate, String apiV3Key) throws Exception {
        String associatedData = encryptCertificate.getString("associated_data");
        String nonce = encryptCertificate.getString("nonce");
        String ciphertext = encryptCertificate.getString("ciphertext");
        
        // 使用AES-GCM解密
        byte[] key = apiV3Key.getBytes(StandardCharsets.UTF_8);
        byte[] nonceBytes = nonce.getBytes(StandardCharsets.UTF_8);
        byte[] associatedDataBytes = associatedData.getBytes(StandardCharsets.UTF_8);
        byte[] ciphertextBytes = Base64.getDecoder().decode(ciphertext);
        
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        GCMParameterSpec spec = new GCMParameterSpec(128, nonceBytes);
        SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
        
        cipher.init(Cipher.DECRYPT_MODE, secretKey, spec);
        cipher.updateAAD(associatedDataBytes);
        
        byte[] decryptedBytes = cipher.doFinal(ciphertextBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }
    
    /**
     * 销毁资源
     */
    public void destroy() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        certificateCache.clear();
        initialized = false;
        LogKit.info("微信支付平台证书管理器已销毁");
    }
    
    /**
     * 获取缓存的证书数量
     */
    public int getCertificateCount() {
        return certificateCache.size();
    }
    
    /**
     * 清空证书缓存
     */
    public void clearCache() {
        certificateCache.clear();
        LogKit.info("微信支付平台证书缓存已清空");
    }
    
    /**
     * 创建模拟证书（仅用于开发测试）
     */
    private void createMockCertificate() {
        try {
            // 使用一个简单有效的自签名证书进行测试
            // 这个证书是有效的Base64编码格式
            String mockCertPem = "-----BEGIN CERTIFICATE-----\n" +
                "MIICpDCCAYwCCQDlHahEIgH6QDANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAls\n" +
                "b2NhbGhvc3QwHhcNMjQwMTE1MDAwMDAwWhcNMjUwMTE1MDAwMDAwWjAUMRIwEAYD\n" +
                "VQQDDAlsb2NhbGhvc3QwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC8\n" +
                "W2spyzbHLCGG7PoUlK12c8RMPwSx8zxW6kH3FTxAwrvZ3YFZfYS9Q1DWvVJHSj8l\n" +
                "XR8kPpGgZY2b7qmJZ8Y5qFBKnq2cdom6LeWRG3lQBxlMOsG2cGJNsUcQJKzqWr/F\n" +
                "GWBqLh+qnLI1R8exzTdE7ZAUMqPKGKXP6MwKgWJcPZBrhSC0hQMZSwZPT2IRUQXM\n" +
                "y7dOFTglLyWHqce7qNWlBh7y3LYClTfDrCZvQ4dnu/V5keIgwhMjeopGSEDZDp9C\n" +
                "cSVAMVp5GVhq+7BbSMkZnjv3TYX+KqnFfRfkHHGMxJoECVstWbvdW5UWkd5qL1Hh\n" +
                "S8qtb7p/8LJRyn0gW+5LAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAFgTGJhf+/1M\n" +
                "CmLPdKv3qZNK4gvXN0YEyEaBKJRU3d0F0F+Sze5Y6VkJf+9EBYh3kgS8PBGnlBr9\n" +
                "cS7l5V2GCOyM0PmvZBXeLiT5h2dYDM7ITjJzPDPj2qNQNPJVj4cBKmBwFq1OR8fE\n" +
                "gNO8nAEq8zqJFqY+KQGHmJ8KLuGYvMz+C3mPZwHFx7P7rVlV8zJ7CAKRqTzDIOtk\n" +
                "fJivsbHGRVE7lmxC3SZvZ5oEZAPaYNjXKZVmKjq/wgMPX1RlMQOqH1NsKF7jg2Y2\n" +
                "7P9OBPtJKJPx7pckP/BcXr3w0oUBRyLqXKVzpjTRRRcHqmhPxYJwhGkK2yYSxIYH\n" +
                "K6LNXvdWRCg=\n" +
                "-----END CERTIFICATE-----";
                
            // 将PEM格式证书转换为X509Certificate对象
            // 去除PEM头尾，只保留Base64内容
            String base64Cert = mockCertPem
                .replace("-----BEGIN CERTIFICATE-----", "")
                .replace("-----END CERTIFICATE-----", "")
                .replaceAll("\\s", "");
            
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            byte[] certBytes = Base64.getDecoder().decode(base64Cert);
            ByteArrayInputStream certStream = new ByteArrayInputStream(certBytes);
            X509Certificate mockCert = (X509Certificate) cf.generateCertificate(certStream);
            
            // 使用统一的Mock序列号
            String mockSerial = "MOCK_SERIAL";
            certificateCache.put(mockSerial, mockCert);
            
            LogKit.info("已创建Mock证书，序列号: " + mockSerial);
            
        } catch (Exception e) {
            LogKit.error("创建Mock证书失败", e);
        }
    }
}
