package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BasePromotionEventReport<M extends BasePromotionEventReport<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}

	public M setUserId(java.lang.Integer userId) {
		set("user_id", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("user_id");
	}

	public M setActionType(java.lang.Integer actionType) {
		set("action_type", actionType);
		return (M)this;
	}
	
	public java.lang.Integer getActionType() {
		return getInt("action_type");
	}

	public M setSuccess(java.lang.Integer success) {
		set("success", success);
		return (M)this;
	}
	
	public java.lang.Integer getSuccess() {
		return getInt("success");
	}

	public M setPromotionCode(java.lang.String promotionCode) {
		set("promotion_code", promotionCode);
		return (M)this;
	}
	
	public java.lang.String getPromotionCode() {
		return getStr("promotion_code");
	}

	public M setBizRef(java.lang.String bizRef) {
		set("biz_ref", bizRef);
		return (M)this;
	}
	
	public java.lang.String getBizRef() {
		return getStr("biz_ref");
	}

	public M setEventTime(java.util.Date eventTime) {
		set("event_time", eventTime);
		return (M)this;
	}
	
	public java.util.Date getEventTime() {
		return get("event_time");
	}

	public M setReportedAt(java.util.Date reportedAt) {
		set("reported_at", reportedAt);
		return (M)this;
	}
	
	public java.util.Date getReportedAt() {
		return get("reported_at");
	}

	public M setHttpStatus(java.lang.Integer httpStatus) {
		set("http_status", httpStatus);
		return (M)this;
	}
	
	public java.lang.Integer getHttpStatus() {
		return getInt("http_status");
	}

	public M setDurationMs(java.lang.Integer durationMs) {
		set("duration_ms", durationMs);
		return (M)this;
	}
	
	public java.lang.Integer getDurationMs() {
		return getInt("duration_ms");
	}

	public M setThirdPartyRawResp(java.lang.String thirdPartyRawResp) {
		set("third_party_raw_resp", thirdPartyRawResp);
		return (M)this;
	}
	
	public java.lang.String getThirdPartyRawResp() {
		return getStr("third_party_raw_resp");
	}

	public M setCreatedAt(java.util.Date createdAt) {
		set("created_at", createdAt);
		return (M)this;
	}
	
	public java.util.Date getCreatedAt() {
		return get("created_at");
	}

	public M setUpdatedAt(java.util.Date updatedAt) {
		set("updated_at", updatedAt);
		return (M)this;
	}
	
	public java.util.Date getUpdatedAt() {
		return get("updated_at");
	}

}
