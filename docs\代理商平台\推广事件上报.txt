测试环境：
url:  https://support.xpyun.net/api/admin/member/promotionCall

方法：post

请求头: Content-Type: application/json

请求体例子：

{
    "user":"<EMAIL>",
    "timestamp":"1689065333698",
    "sign":"db819d281eae9b66a6be6c2ad42f73e613c8818a",
    "promotionCode": "DU2UBA",
    "project": "xprinter",
    "actionType": 1
}

sign：加密字符串，用于验签，计算方式：user  +  '123!@#wqee'  + timestamp 进行 sha1 加密
promotionCode：推广码，每个代理商都有一个推广码，每个用户都可能绑定到唯一一个代理商
project: 项目名称， 目前 参数 固定 xprinter
actionType：事件类型， 1 - 注册  2 - 注册并打印（有效推广）  3 - 充值vip（vip订阅）

PS: '123!@#wqee' 是一串约定好的秘钥（可能会变更）



返回

正确返回：

{
    "status": 200,
    "data": true,
    "rel": true
}

以data 作为判断，true 请求成功，false 请求失败